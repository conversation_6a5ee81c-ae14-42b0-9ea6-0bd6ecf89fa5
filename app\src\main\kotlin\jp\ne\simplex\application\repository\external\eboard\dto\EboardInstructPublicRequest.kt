package jp.ne.simplex.application.repository.external.eboard.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.UpdatePropertyMaintenance
import jp.ne.simplex.application.repository.external.eboard.config.EboardApiPath
import jp.ne.simplex.application.repository.external.eboard.config.EboardRequest
import jp.ne.simplex.authentication.AuthInfo

class EboardInstructPublicRequest private constructor(
    @field:JsonProperty("list")
    private val list: List<InstructPublicUnitContent>,
) : EboardRequest {

    class InstructPublicUnitContent(
        @field:JsonProperty("tatemonoCd")
        private val tatemonoCd: String,

        @field:JsonProperty("heyaCd")
        private val heyaCd: String,

        @field:JsonProperty("userId")
        private val userId: String,

        @field:JsonProperty("keisai")
        private val keisai: String,
    )

    override fun getApiPath(): EboardApiPath {
        return EboardApiPath.INSTRUCT_PUBLIC
    }

    companion object {
        fun of(
            requestUser: AuthInfo.RequestUser,
            updatePublishStatusList: List<UpdatePropertyMaintenance.PublishStatus>
        ): EboardInstructPublicRequest {
            return EboardInstructPublicRequest(
                list = updatePublishStatusList.map {
                    InstructPublicUnitContent(
                        tatemonoCd = it.id.buildingCode.value,
                        heyaCd = it.id.roomCode.value,
                        userId = requestUser.value,
                        keisai = it.publishStatus.value.toString()
                    )
                }
            )
        }
    }
}
