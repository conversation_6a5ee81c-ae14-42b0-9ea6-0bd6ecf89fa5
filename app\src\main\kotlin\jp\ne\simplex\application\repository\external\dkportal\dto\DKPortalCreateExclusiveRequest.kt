package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.ExclusivePropertyAction
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRequest

@JsonIgnoreProperties(value = ["ecode"])
abstract class DKPortalCreateExclusiveRequest(
    @field:JsonProperty("dk_link_id")
    val dkLinkId: String,

    @field:JsonProperty("sales_office_id")
    val salesOfficeId: Int?,

    @field:JsonProperty("kentaku_building_code")
    val kentakuBuildingCode: String,

    @field:JsonProperty("kentaku_room_code")
    val kentakuRoomCode: String,

    @field:JsonProperty("exclusive_from")
    val exclusiveFrom: String,

    @field:JsonProperty("exclusive_to")
    val exclusiveTo: String,

    @field:JsonProperty("e_code")
    val eCode: String?,

    @field:JsonProperty("company_type")
    val companyType: Int,
) : DKPortalRequest {
    abstract override fun getDKPortalOperationName(): DKPortalOperationName

    companion object {

        fun of(
            property: Property,
            record: ExclusivePropertyAction.Record
        ): DKPortalCreateExclusiveRequest {
            return when (property.getType()) {
                Property.Type.RESIDENTIAL -> {
                    DKPortalCreateExclusiveHousingRequest(property, record)
                }

                Property.Type.COMMERCIAL -> {
                    DKPortalCreateExclusiveBusinessRequest(property, record)
                }
            }
        }

    }
}
