<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <!--「app.log.appender = console-text」 が指定された場合のログフォーマット -->
  <appender class="ch.qos.logback.core.ConsoleAppender" name="console-text">
    <encoder>
      <pattern><!-- @formatter:off -->
        %d{yyyy/MM/dd HH:mm:ss.SSS} [%level] [%thread] [%mdc{transaction-id}] %mdc{log-type} [%class{5}] %mdc{header} %mdc{uri} %msg%n
      </pattern>
    </encoder>
  </appender>

  <!-- 「app.log.appender = console-json」が指定された場合のログフォーマット -->
  <appender class="ch.qos.logback.core.ConsoleAppender" name="console-json">
    <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
      <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
        <valueMasker class="jp.ne.simplex.log.LogMaskingConfig"/>
      </jsonGeneratorDecorator>
      <providers>
        <pattern>
          <omitEmptyFields>true</omitEmptyFields>
          <pattern>
            {
            "timestamp": "%date{yyyy/MM/dd HH:mm:ss.SSS}",
            "level": "[%level]",
            "thread": "%thread",
            "transaction_id":"%mdc{transaction-id}",
            "log_type": "%mdc{log-type}",
            "logger": "%logger",
            "status": "%mdc{status}",
            "method": "%mdc{method}",
            "header": "%mdc{header}",
            "uri": "%mdc{uri}",
            "message": "%message",
            "latency": "%mdc{latency}",
            "stack_trace": "%ex{full}"
            }
          </pattern>
        </pattern>
      </providers>
    </encoder>
  </appender>

  <!--include resource="org/springframework/boot/logging/logback/console-appender.xml" /-->
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

  <root level="INFO">
    <appender-ref ref="${APP_LOG_APPENDER}"/>
  </root>

  <springProperty defaultValue="console-text" name="APP_LOG_APPENDER" source="app.log.appender"/>

</configuration>