package jp.ne.simplex.application.repository.file

import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.application.model.VacancyParkingLotTarget
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.PathExtension.Companion.createDirectoryIfNotExist
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.io.BufferedWriter
import java.io.File
import java.nio.charset.Charset
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Repository
class ParkingFileRepository : ParkingFileRepositoryInterface {
    companion object {
        private val log = LoggerFactory.getLogger(ParkingFileRepository::class.java)
        private const val PARKING_SUMMARY_FOR_DK_PORTAL_FILE_NAME_BASE =
            "parking-summary-for-dk-portal-"
        private val PARKING_SUMMARY_FOR_DK_PORTAL_FILE_DATETIME_PATTERN =
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        private const val PARKING_SUMMARY_FOR_DK_PORTAL_FILE_EXTENSION = ".csv"
        private val PARKING_SUMMARY_FOR_DK_PORTAL_FILE_CHARSET = Charset.forName("Shift-JIS")
        private val PARKING_SUMMARY_FOR_DK_PORTAL_FILE_HEADER =
            listOf("orderCode", "availableParkingFlag", "secondParkingContractPossibleFlag")

        private const val VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_NAME_BASE =
            "_WPVACANCYPARKINGDATA_NEW"
        private val VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_DATETIME_PATTERN =
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        private const val VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_EXTENSION = ".CSV"
        private val VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_CHARSET = Charset.forName("MS932")

        private const val CSV_DELIMITER = ","

        private fun ParkingContractPossibility.toCsvRow(): List<String> {
            val availableParkingFlag: Boolean
            val secondParkingContractPossibleFlag: Boolean
            if (this.isAutoJudge == ContractPossibilityAutoJudge.MANUAL) {
                availableParkingFlag = true
                secondParkingContractPossibleFlag = false
            } else {
                availableParkingFlag =
                    this.firstParkingContractPossibility == ContractPossibility.POSSIBLE
                secondParkingContractPossibleFlag =
                    this.secondParkingContractPossibility == ContractPossibility.POSSIBLE
            }
            return listOf(
                this.orderCode.value, availableParkingFlag.toInt().toString(),
                secondParkingContractPossibleFlag.toInt().toString()
            )
        }

        private fun VacancyParkingLotTarget.toCsvRow(
            dateStr: String,
            timeStr: String,
        ): List<String> {
            return listOf(
                this.id.buildingCode.value,
                this.id.parkingLotCode.value,
                this.parkingLotNumber?.trim()?.replace(",", ".") ?: "",
                this.parkingFee?.toString() ?: "",
                this.bulkLeaseFlag?.value?.toString() ?: "",
                this.assessmentDivision?.value?.toString() ?: "",
                this.parkingLotCategory?.value ?: "",
                dateStr,
                timeStr
            )
        }
    }

    override fun saveParkingSummaryForDkPortal(
        parkingContractPossibilityList: List<ParkingContractPossibility>
    ): Path {
        val fileName = PARKING_SUMMARY_FOR_DK_PORTAL_FILE_NAME_BASE +
                LocalDateTime.now().format(PARKING_SUMMARY_FOR_DK_PORTAL_FILE_DATETIME_PATTERN) +
                PARKING_SUMMARY_FOR_DK_PORTAL_FILE_EXTENSION
        // TODO いったんファイルに出力するようにしておく
        log.info("output path=${File("./work/${fileName}").absolutePath}")
        val outputPath = Path.of("./work", fileName)
        outputPath.createDirectoryIfNotExist()

        // ファイルに書き込み
        Files.newBufferedWriter(
            outputPath,
            PARKING_SUMMARY_FOR_DK_PORTAL_FILE_CHARSET
        ).use { writer ->
            writeParkingSummaryForDkPortal(writer, parkingContractPossibilityList)
        }

        return outputPath.toAbsolutePath()
    }

    fun writeParkingSummaryForDkPortal(
        writer: BufferedWriter,
        parkingContractPossibilityList: List<ParkingContractPossibility>
    ) {
        writer.write(PARKING_SUMMARY_FOR_DK_PORTAL_FILE_HEADER.joinToString(CSV_DELIMITER))
        writer.newLine()
        parkingContractPossibilityList.forEach {
            writer.write(it.toCsvRow().joinToString(CSV_DELIMITER))
            writer.newLine()
        }
    }

    override fun saveVacancyParkingDataForWelcomePark(
        vacancyParkingLotList: List<VacancyParkingLotTarget>,
        datetime: LocalDateTime,
    ): Path {
        val datetimeStr = datetime
            .format(VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_DATETIME_PATTERN)
        val dateStr = datetimeStr.substring(0, 8)
        val timeStr = datetimeStr.substring(8)
        val fileName = dateStr + VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_NAME_BASE +
                VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_EXTENSION
        // TODO いったんファイルに出力するようにしておく
        log.info("output path=${File("./work/${fileName}").absolutePath}")
        val outputPath = Path.of("./work", fileName)
        outputPath.createDirectoryIfNotExist()

        // ファイルに書き込み
        Files.newBufferedWriter(
            outputPath,
            VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_CHARSET
        ).use { writer ->
            writeVacancyParkingDataForWelcomePark(writer, vacancyParkingLotList, dateStr, timeStr)
        }

        return outputPath.toAbsolutePath()
    }

    fun writeVacancyParkingDataForWelcomePark(
        writer: BufferedWriter,
        vacancyParkingLotList: List<VacancyParkingLotTarget>,
        dateStr: String,
        timeStr: String,
    ) {
        vacancyParkingLotList.forEach {
            writer.write(it.toCsvRow(dateStr, timeStr).joinToString(CSV_DELIMITER))
            writer.newLine()
        }
    }

    override fun saveFromS3ForDkPortal(latestFile: ResponseInputStream<GetObjectResponse>): Path {
        val fileName = PARKING_SUMMARY_FOR_DK_PORTAL_FILE_NAME_BASE +
                LocalDateTime.now().format(PARKING_SUMMARY_FOR_DK_PORTAL_FILE_DATETIME_PATTERN) +
                PARKING_SUMMARY_FOR_DK_PORTAL_FILE_EXTENSION

        return saveFromS3(latestFile, fileName)
    }

    override fun saveFromS3ForWelcomePark(
        latestFile: ResponseInputStream<GetObjectResponse>,
    ): Path {
        val datetimeStr = LocalDateTime.now()
            .format(VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_DATETIME_PATTERN)
        val dateStr = datetimeStr.substring(0, 8)
        val fileName = dateStr + VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_NAME_BASE +
                VACANCY_PARKING_DATA_FOR_WELCOME_PARK_FILE_EXTENSION

        return saveFromS3(latestFile, fileName)
    }

    private fun saveFromS3(
        fileContent: ResponseInputStream<GetObjectResponse>,
        fileName: String
    ): Path {
        // 出力先パスの設定
        val outputPath = Path.of("./work", fileName)
        outputPath.createDirectoryIfNotExist()

        try {
            // ストリームからファイルに直接書き込む（文字コードの変換を避ける）
            Files.copy(
                fileContent,
                outputPath,
                java.nio.file.StandardCopyOption.REPLACE_EXISTING
            )

            log.info("Successfully saved file from S3: path=$outputPath")
            return outputPath.toAbsolutePath()
        } catch (e: Exception) {
            log.error("Failed to save file from S3", e)
            throw e
        } finally {
            fileContent.close()
        }
    }
}

interface ParkingFileRepositoryInterface {
    /** DK-PORTAL向け駐車場サマリCSVファイルを保存する。 */
    fun saveParkingSummaryForDkPortal(
        parkingContractPossibilityList: List<ParkingContractPossibility>
    ): Path

    /** WelcomePark向け空き駐車場CSVファイルを保存する。 */
    fun saveVacancyParkingDataForWelcomePark(
        vacancyParkingLotList: List<VacancyParkingLotTarget>,
        datetime: LocalDateTime,
    ): Path

    fun saveFromS3ForDkPortal(latestFile: ResponseInputStream<GetObjectResponse>): Path

    fun saveFromS3ForWelcomePark(
        latestFile: ResponseInputStream<GetObjectResponse>,
    ): Path
}
