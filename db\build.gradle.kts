import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName

val jooqVersion = rootProject.extra.get("jooqVersion") as String
val postgresVersion = rootProject.extra.get("postgresVersion") as String
val flywayVersion = rootProject.extra.get("flywayVersion") as String

plugins {
    id("org.flywaydb.flyway") version "10.14.0"
    id("nu.studer.jooq") version "9.0"
}

buildscript {
    dependencies {
        classpath("org.flywaydb:flyway-database-postgresql:10.22.0")
        classpath("org.testcontainers:junit-jupiter:1.20.4")
        classpath("org.testcontainers:postgresql:1.20.4")
    }

    configurations["classpath"].resolutionStrategy.eachDependency {
        if (requested.group.startsWith("org.jooq") && requested.name.startsWith("jooq")) {
            useVersion(rootProject.extra.get("jooqVersion") as String)
        }
        if (requested.group.startsWith("org.flywaydb") && requested.name.startsWith("flyway")) {
            useVersion(rootProject.extra.get("flywayVersion") as String)
        }

    }
}

dependencies {
    implementation("org.jooq:jooq-postgres-extensions:$jooqVersion")
    jooqGenerator("org.postgresql:postgresql:$postgresVersion")
    jooqGenerator("jakarta.xml.bind:jakarta.xml.bind-api:4.0.2")
    jooqGenerator(project(":db-strategy"))
}

object DBProperty {
    const val DRIVER = "org.postgresql.Driver"
    const val URL = "****************************************"
    const val NAME = "gpb_dev"
    const val USER = "app"
    const val PASSWORD = "simplex"
}

flyway {
    url = DBProperty.URL
    user = DBProperty.USER
    password = DBProperty.PASSWORD
    schemas = arrayOf(DBProperty.USER)
    cleanDisabled = true
}

jooq {
    version.set(jooqVersion)
    configurations {
        create(DBProperty.USER) {
            generateSchemaSourceOnCompilation.set(false)
            jooqConfiguration.apply {
                jdbc.apply {
                    driver = DBProperty.DRIVER
                    url = DBProperty.URL
                    user = DBProperty.USER
                    password = DBProperty.PASSWORD
                }
                generator.apply {
                    name = "org.jooq.codegen.KotlinGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = DBProperty.USER
                        excludes = "flyway_schema_history"
                    }
                    generate.apply {
                        isDeprecated = false
                        isRecords = true
                        isTables = true
                        isPojos = true
                        isKotlinNotNullRecordAttributes = true
                        isKotlinNotNullPojoAttributes = true
                        isKotlinNotNullInterfaceAttributes = true
                        isRecordsImplementingRecordN = false
                        isPojosAsJavaRecordClasses = false
                        isPojosAsScalaCaseClasses = false
                        isPojosToString = false
                    }
                    target.apply {
                        packageName = "jp.ne.simplex.db.jooq.gen"
                        directory = "build/jooq_gen/"
                    }
                    strategy.apply {
                        name = "jp.ne.simplex.db.JooqGeneratorStrategy"
                    }
                }
            }
        }
    }
}

sourceSets {
    main {
        java {
            srcDir("build/jooq_gen")
        }
    }
}

tasks.register("setup") {
    group = "build"

    dependsOn(rootProject.tasks["buildTestContainer"])
    dependsOn("startTestContainer")
    dependsOn("overwriteConfig")
    dependsOn("generateAppJooq")
    dependsOn("stopTestContainer")

    tasks["startTestContainer"].mustRunAfter(rootProject.tasks["buildTestContainer"])
    tasks["overwriteConfig"].mustRunAfter("startTestContainer")
    tasks["generateAppJooq"].mustRunAfter("overwriteConfig")
    tasks["stopTestContainer"].mustRunAfter("generateAppJooq")
}

tasks.register("startTestContainer") {
    doLast {
        val container: PostgreSQLContainer<*> =
            PostgreSQLContainer(
                DockerImageName.parse("testcontainers/local-postgres:latest")
                    .asCompatibleSubstituteFor("postgres")
            )
                .withDatabaseName(DBProperty.NAME)
                .withUsername("postgres")
                .withPassword(DBProperty.PASSWORD)
                .withCommand("--max-connections=5000")
                .waitingFor(
                    Wait.forLogMessage(".*ready for start up.*\\s", 1)
                )
                .withCommand()


        container.start()

        project.ext.set("dbInstance", container)
        project.ext.set("dbUrl", container.getJdbcUrl())

        println("TestContainers started. URL=${project.ext.get("dbUrl")}")
    }
}

tasks.register("overwriteConfig") {
    doLast {
        val dbUrl = project.ext.get("dbUrl") as String

        flyway {
            url = dbUrl
        }
        jooq {
            configurations {
                named(DBProperty.USER) {
                    generateSchemaSourceOnCompilation.set(false)
                    jooqConfiguration.apply {
                        jdbc.apply {
                            url = dbUrl
                        }
                    }
                }
            }
        }
    }
}

tasks.register("stopTestContainer") {
    doLast {
        val dbUrl = project.ext.get("dbUrl") as String
        val dbInstance = project.ext.get("dbInstance") as PostgreSQLContainer<*>

        dbInstance.stop()

        println("TestContainers stop. URL=${dbUrl}")
    }
}
