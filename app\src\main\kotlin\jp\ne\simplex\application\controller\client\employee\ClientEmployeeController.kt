package jp.ne.simplex.application.controller.client.employee

import jp.ne.simplex.application.controller.client.employee.dto.ClientEmployeesOfBranchRequest
import jp.ne.simplex.application.controller.client.employee.dto.ClientEmployeesOfBranchResponse
import jp.ne.simplex.application.service.EmployeeService
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/employee")
class ClientEmployeeController(
    private val service: EmployeeService,
) {

    @PostMapping()
    @ApiDefinition(summary = "支店所属従業員一覧取得API")
    fun getEmployeesOfBranch(
        @RequestBody request: ClientEmployeesOfBranchRequest,
    ): ClientEmployeesOfBranchResponse {
        return ClientEmployeesOfBranchResponse.of(service.listBy(request.getBranch()))
    }
}
