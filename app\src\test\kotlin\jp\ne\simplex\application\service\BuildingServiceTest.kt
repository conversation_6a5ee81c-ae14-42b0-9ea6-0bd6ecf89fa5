package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.repository.db.BuildingMasterRepositoryInterface
import jp.ne.simplex.application.repository.external.eboard.EboardRepositoryInterface
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiServerException
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.mock.MockBuildingMasterRepository
import jp.ne.simplex.mock.MockEboardRepository
import jp.ne.simplex.stub.stubBuilding
import jp.ne.simplex.stub.stubJwtRequestUser
import jp.ne.simplex.stub.stubRegisterGarbageImage
import jp.ne.simplex.stub.stubRegisterParkingImage
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class BuildingServiceTest {

    private fun buildingService(
        buildingMasterRepository: BuildingMasterRepositoryInterface
        = MockBuildingMasterRepository(),
        eboardRepository: EboardRepositoryInterface = MockEboardRepository(),
    ): BuildingService {
        return BuildingService(
            buildingMasterRepository = buildingMasterRepository,
            eboardRepository = eboardRepository,
        )
    }

    @Nested
    @DisplayName("駐車場配置図画像登録の検証")
    inner class Scenario1 {

        @Test
        @DisplayName("建物コードに紐づく建物が存在しない場合、建物コードが不正の例外が発生すること")
        fun case1() {
            val buildingService = buildingService()

            val err = assertThrows<ServerValidationException> {
                val param = stubRegisterParkingImage()
                buildingService.registerParkingImage(stubJwtRequestUser(), param)
            }
            assertEquals(ErrorType.BAD_REQUEST, err.type)
            assertEquals(
                ErrorMessage.INVALID_BUILDING_CODE.format().errorMessage,
                err.detail.errorMessage
            )
        }

        @Test
        @DisplayName("駐車場配置図画像登録外部API呼び出しでエラーが発生した場合、例外がthrowされること")
        fun case2() {
            val buildingService = buildingService(
                buildingMasterRepository = MockBuildingMasterRepository(
                    findByFunc = { _ -> stubBuilding() }
                ),
                eboardRepository = MockEboardRepository(
                    registerParkingImageFunc = { _, _, _ ->
                        throw ExternalApiServerException(
                            ErrorType.EBOARD_API_ERROR,
                            ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format()
                        )
                    },
                )
            )

            val err = assertThrows<ExternalApiServerException> {
                val param = stubRegisterParkingImage()
                buildingService.registerParkingImage(stubJwtRequestUser(), param)
            }
            assertEquals(ErrorType.EBOARD_API_ERROR, err.type)
        }
    }

    @Nested
    @DisplayName("駐車場配置図画像削除の検証")
    inner class Scenario2 {

        @Test
        @DisplayName("建物コードに紐づく建物が存在しない場合、建物コードが不正の例外が発生すること")
        fun case1() {
            val buildingService = buildingService()

            val err = assertThrows<ServerValidationException> {
                buildingService.deleteParkingImage(
                    stubJwtRequestUser(),
                    Building.Code.of("000130301")
                )
            }
            assertEquals(ErrorType.BAD_REQUEST, err.type)
            assertEquals(
                ErrorMessage.INVALID_BUILDING_CODE.format().errorMessage,
                err.detail.errorMessage
            )
        }

        @Test
        @DisplayName("駐車場配置図画像登録外部API呼び出しでエラーが発生した場合、例外がthrowされること")
        fun case2() {
            val buildingService = buildingService(
                buildingMasterRepository = MockBuildingMasterRepository(
                    findByFunc = { _ -> stubBuilding() }
                ),
                eboardRepository = MockEboardRepository(
                    deleteParkingImageFunc = { _, _ ->
                        throw ExternalApiServerException(
                            ErrorType.EBOARD_API_ERROR,
                            ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format()
                        )
                    },
                )
            )

            val err = assertThrows<ExternalApiServerException> {
                buildingService.deleteParkingImage(
                    stubJwtRequestUser(),
                    Building.Code.of("000130301")
                )
            }
            assertEquals(ErrorType.EBOARD_API_ERROR, err.type)
        }
    }

    @Nested
    @DisplayName("ゴミ置場画像登録の検証")
    inner class Scenario3 {

        @Test
        @DisplayName("建物コードに紐づく建物が存在しない場合、建物コードが不正の例外が発生すること")
        fun case1() {
            val buildingService = buildingService()

            val err = assertThrows<ServerValidationException> {
                val param = stubRegisterGarbageImage()
                buildingService.registerGarbageImage(stubJwtRequestUser(), param)
            }
            assertEquals(ErrorType.BAD_REQUEST, err.type)
            assertEquals(
                ErrorMessage.INVALID_BUILDING_CODE.format().errorMessage,
                err.detail.errorMessage
            )
        }

        @Test
        @DisplayName("ゴミ置場画像登録外部API呼び出しでエラーが発生した場合、例外がthrowされること")
        fun case2() {
            val buildingService = buildingService(
                buildingMasterRepository = MockBuildingMasterRepository(
                    findByFunc = { _ -> stubBuilding() }
                ),
                eboardRepository = MockEboardRepository(
                    registerGarbageImageFunc = { _, _, _ ->
                        throw ExternalApiServerException(
                            ErrorType.EBOARD_API_ERROR,
                            ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format()
                        )
                    },
                )
            )

            val err = assertThrows<ExternalApiServerException> {
                val param = stubRegisterGarbageImage()
                buildingService.registerGarbageImage(stubJwtRequestUser(), param)
            }
            assertEquals(ErrorType.EBOARD_API_ERROR, err.type)
        }
    }

    @Nested
    @DisplayName("ゴミ置場画像削除の検証")
    inner class Scenario4 {

        @Test
        @DisplayName("建物コードに紐づく建物が存在しない場合、建物コードが不正の例外が発生すること")
        fun case1() {
            val buildingService = buildingService()

            val err = assertThrows<ServerValidationException> {
                buildingService.deleteGarbageImage(
                    stubJwtRequestUser(),
                    Building.Code.of("000130301")
                )
            }
            assertEquals(ErrorType.BAD_REQUEST, err.type)
            assertEquals(
                ErrorMessage.INVALID_BUILDING_CODE.format().errorMessage,
                err.detail.errorMessage
            )
        }

        @Test
        @DisplayName("ゴミ置場画像登録外部API呼び出しでエラーが発生した場合、例外がthrowされること")
        fun case2() {
            val buildingService = buildingService(
                buildingMasterRepository = MockBuildingMasterRepository(
                    findByFunc = { _ -> stubBuilding() }
                ),
                eboardRepository = MockEboardRepository(
                    deleteGarbageImageFunc = { _, _ ->
                        throw ExternalApiServerException(
                            ErrorType.EBOARD_API_ERROR,
                            ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format()
                        )
                    },
                )
            )

            val err = assertThrows<ExternalApiServerException> {
                buildingService.deleteGarbageImage(
                    stubJwtRequestUser(),
                    Building.Code.of("000130301")
                )
            }
            assertEquals(ErrorType.EBOARD_API_ERROR, err.type)
        }
    }
}
