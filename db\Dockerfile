# ベースイメージを指定
FROM postgres:16.1

# 必要なパッケージをインストールし、ロケールを設定
RUN apt-get update && apt-get install -y locales && rm -rf /var/lib/apt/lists/* \
    && localedef -i ja_JP -c -f UTF-8 -A /usr/share/locale/locale.alias ja_JP.UTF-8

# 環境変数を設定
ENV LANG=ja_JP.utf8
ENV TZ='Asia/Tokyo'
ENV POSTGRES_PASSWORD='simplex'
ENV SCRIPT_ARG='dev'

# 初期化スクリプトをコンテナにコピー
COPY ./src/test/resources/ddl /docker-entrypoint-initdb.d/ddl/
COPY ./src/test/resources/scripts /docker-entrypoint-initdb.d/scripts/
COPY ./src/test/resources/env /docker-entrypoint-initdb.d/env/
COPY ./src/test/resources/initdb.sh /docker-entrypoint-initdb.d/initdb.sh

# カスタムコマンドを実行
CMD ["postgres", "-c", "log_destination=stderr", "-c", "log_statement=all", "-c", "log_connections=on", "-c", "log_disconnections=on"]
