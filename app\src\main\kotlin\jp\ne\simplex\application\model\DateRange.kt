package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import java.time.LocalDate

data class DateRange private constructor(
    val from: LocalDate,
    val to: LocalDate,
) {

    companion object {

        fun of(from: LocalDate, to: LocalDate, allowSameDate: Boolean = false): DateRange {
            if (from.isBefore(to)) {
                return DateRange(from, to)
            }
            if (from.isEqual(to)) {
                if (allowSameDate) {
                    return DateRange(from, to)
                }
                throw ModelCreationFailedException(
                    ErrorMessage.INVALID_DATE_RANGE_FORMAT.format(from.yyyyMMdd(), to.yyyyMMdd())
                )
            }
            throw ModelCreationFailedException(
                ErrorMessage.INVALID_DATE_RANGE_FORMAT.format(from.yyyyMMdd(), to.yyyyMMdd())
            )
        }
    }

    fun isStarted(allowSameDate: Boolean = false): Boolean {
        val now = LocalDate.now()

        return if (allowSameDate) {
            this.from.isBefore(now) || this.from.isEqual(now)
        } else {
            this.from.isBefore(now)
        }
    }

    fun isEnded(allowSameDate: Boolean = false): Boolean {
        val now = LocalDate.now()

        return if (allowSameDate) {
            this.to.isBefore(now) || this.to.isEqual(now)
        } else {
            this.to.isBefore(now)
        }
    }

    fun isConflict(other: DateRange, allowSameDate: Boolean = false): Boolean {
        // 境界日の比較条件
        val boundaryCondition = if (allowSameDate) {
            // allowSameDate=true: 境界が同じ日の場合は重複とみなさない
            this.from.isAfter(other.to) || this.to.isBefore(other.from) ||
                    this.from.isEqual(other.to) || this.to.isEqual(other.from)
        } else {
            // allowSameDate=false: 境界が同じ日の場合も重複とみなす
            this.from.isAfter(other.to) || this.to.isBefore(other.from)
        }

        // 期間が重複しない条件の否定が、期間が重複する条件
        return !boundaryCondition
    }
}
