package jp.ne.simplex.application.controller.external.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.external.shared.ErrorResponseForWelcomePark
import jp.ne.simplex.exception.ServerValidationException

class ExternalCheckParkingReservableForWelcomeParkResponse(
    @JsonProperty("result")
    @field:Schema(
        description = "WelcomeParkが予約できるかの結果(0:予約不可, 1:予約可能)",
        example = "1"
    )
    val result: Int,
    @JsonProperty("errorCode")
    @field:Schema(
        description = "エラーコード(10: 建物コードまたは駐車場コード不正, 20: 使用中, 99: 予期せぬエラー)",
        example = "10"
    )
    val errorCode: String? = null,
    @JsonProperty("errorMessage")
    @field:Schema(
        description = "エラーメッセージ",
        example = "建物コードまたは、駐車場コードが存在しません。"
    )
    val errorMessage: String? = null,
) {
    companion object {
        fun toSuccessResponse(): ExternalCheckParkingReservableForWelcomeParkResponse {
            return ExternalCheckParkingReservableForWelcomeParkResponse(
                result = 1,
            )
        }

        fun toFailureResponse(e: ServerValidationException): ExternalCheckParkingReservableForWelcomeParkResponse {
            val errorMessage = ErrorResponseForWelcomePark.of(e.detail.errorMessage)

            return ExternalCheckParkingReservableForWelcomeParkResponse(
                result = 0,
                errorCode = errorMessage.code,
                errorMessage = errorMessage.message
            )
        }
    }
}
