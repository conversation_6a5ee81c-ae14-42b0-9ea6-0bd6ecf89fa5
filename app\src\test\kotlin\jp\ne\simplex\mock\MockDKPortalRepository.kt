package jp.ne.simplex.mock

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.external.dkportal.DKPortalRepositoryInterface

class MockDKPortalRepository(
    val updateAdFfFunc: (updateAdFfList: List<UpdatePropertyMaintenance.AdFf>) ->
    UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>> = { _ ->
        UpdatePropertyMaintenance.Result(emptyList(), emptyList())
    },

    val updateRoomStatusFunc: (
        updatePublishStatus: List<UpdatePropertyMaintenance.PublishStatusWithUpState>,
    ) ->
    UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>> = { _ ->
        UpdatePropertyMaintenance.Result(emptyList(), emptyList())
    },

    val updateParkingLotFunc: (status: ParkingContractPossibility) -> Unit = { _ -> },

    val isNeedUpdateParkingLotFunc: (
        oldStatus: ParkingContractPossibility?,
        newStatus: ParkingContractPossibility
    ) -> Boolean = { _, _ -> false },

    val createExclusiveFunc: (
        property: Property,
        record: ExclusivePropertyAction.Record
    ) -> Unit = { _, _ -> },

    val updateExclusiveFunc: (
        property: Property,
        exclusiveId: ExclusiveProperty.Id,
        record: ExclusivePropertyAction.Record
    ) -> Unit = { _, _, _ -> },

    val deleteExclusiveFunc: (property: Property, id: ExclusiveProperty.Id) -> Unit = { _, _ -> }

) : DKPortalRepositoryInterface {

    override fun updateAdff(updateAdFfList: List<UpdatePropertyMaintenance.AdFf>): UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>> {
        return updateAdFfFunc(updateAdFfList)
    }

    override fun updateRoomStatus(
        updatePublishStatus: List<UpdatePropertyMaintenance.PublishStatusWithUpState>
    ): UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>> {
        return updateRoomStatusFunc(updatePublishStatus)
    }

    override fun updateParkingLot(status: ParkingContractPossibility) {
        updateParkingLotFunc(status)
    }

    override fun isNeedUpdateParkingLot(
        oldStatus: ParkingContractPossibility,
        newStatus: ParkingContractPossibility
    ): Boolean {
        return isNeedUpdateParkingLotFunc(oldStatus, newStatus)
    }

    override fun createExclusive(
        property: Property,
        record: ExclusivePropertyAction.Record
    ) {
        return createExclusiveFunc(property, record)
    }

    override fun updateExclusive(
        property: Property,
        exclusiveId: ExclusiveProperty.Id,
        record: ExclusivePropertyAction.Record
    ) {
        return updateExclusiveFunc(property, exclusiveId, record)
    }

    override fun deleteExclusive(property: Property, exclusiveId: ExclusiveProperty.Id) {
        return deleteExclusiveFunc(property, exclusiveId)
    }
}
