package jp.ne.simplex.authentication

import com.auth0.jwt.exceptions.SignatureVerificationException
import com.auth0.jwt.exceptions.TokenExpiredException
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.mock.MockSecretManagerClient
import jp.ne.simplex.stub.stubAuthConfig
import jp.ne.simplex.stub.stubEmployee
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.springframework.boot.web.server.Cookie
import java.time.LocalDateTime
import kotlin.test.fail

class JwtAuthProcessorTest {

    private val cookieConfig = AuthConfig.Jwt.CookieConfig(
        httpOnly = true,
        secure = true,
        sameSite = Cookie.SameSite.STRICT,
        maxAge = 86400
    )

    @AfterEach
    fun tearDown() {
        MockLocalDateTime.close()
    }

    /**
     * 以下で実現しているJWTの生成/検証処理をいずれ、Springに移行するが、現時点（25/1/14）では、
     * 「認証基盤：Lambda、一部API：Spring」となっているため、その整合性を担保するためのテストを実施する
     *
     * 参照）https://github.com/sxi-propetech/propetech/blob/main/apps/server/src/domain/auth/auth.ts
     */
    @Nested
    @DisplayName("Lambdaで実施している認証機構と同様のロジックでJWTの生成/検証ができること")
    inner class Scenario1 {

        private val jwtConfig = AuthConfig.Jwt(
            AuthConfig.Jwt.TokenConfig("access_token_key", 300),
            AuthConfig.Jwt.TokenConfig("refresh_token_key", 10800),
            cookieConfig
        )
        private val authConfig = stubAuthConfig(jwtConfig)

        // 下記のトークンを署名した時の秘密鍵の情報
        private val secretManagerClient = MockSecretManagerClient.of(
            config = jwtConfig,
            accessTokenSecretKey = """
                {
                    "jwt_key": "hL{&Vq\"@1}Z(fjQ4[1%G;=!_2P^oZfiT"
                }
            """.trimIndent(),
            refreshTokenSecretKey = """
                {
                    "jwt_key":"\"v\"Bp'&R!k*IeC{3\"j5^q4-H;FWt!Cu;"
                }
            """.trimIndent()
        )

        private val secretManagerRepository = SecretManagerRepository(secretManagerClient)

        /** Lambdaで作成したアクセストークン（ただし有効期限は切れている）
         * "employeeId": "000011",
         * "businessOfficeCode": "627",
         * "iat": 1736822251,
         * "exp": 1736822551
         */
        private val accessTokenValue =
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbXBsb3llZUlkIjoiMDAwMDExIiwiYnVzaW5lc3NPZmZpY2VDb2RlIjoiNjI3IiwiaWF0IjoxNzM2ODIyMjUxLCJleHAiOjE3MzY4MjI1NTF9.TvVNu2lUD5q9BsbqO7eZi6pymqKpGD8AgXVHNT0sKwY"

        /** Lambdaで作成したリフレッシュトークン（ただし有効期限は切れている）
         * "employeeId": "000011",
         * "businessOfficeCode": "627",
         * "iat": 1736822251,
         * "exp": 1736833051
         */
        private val refreshTokenValue =
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbXBsb3llZUlkIjoiMDAwMDExIiwiYnVzaW5lc3NPZmZpY2VDb2RlIjoiNjI3IiwiaWF0IjoxNzM2ODIyMjUxLCJleHAiOjE3MzY4MzMwNTF9.AjKfTv8yGLcoTHCICHS-BBy_qdXlcJ1rQV_cV-esykA"

        @Test
        @DisplayName("Lambdaで作成したトークンを検証できること")
        fun case01() {
            // setup
            val processor = JwtAuthProcessor(authConfig, secretManagerRepository)

            // execute & verify refresh token
            assertThrows<TokenExpiredException> {
                processor.verify(AuthTokenType.ACCESS_TOKEN, accessTokenValue)
            }
            assertThrows<TokenExpiredException> {
                processor.verify(AuthTokenType.REFRESH_TOKEN, refreshTokenValue)
            }
        }

        // @Test
        // @DisplayName("Lambdaで作成したトークンと同じJWTを生成できること")
        // fun case02() {
        //     // 以下トークンの生成日時で時刻を固定する
        //     MockLocalDateTime.setNow(LocalDateTime.of(2025, 1, 14, 11, 37, 31))
        // 
        //     val processor = JwtAuthProcessor(authConfig, secretManagerRepository)
        // 
        //     val actual = processor.gen(
        //         employee = stubEmployee(code = "000011", affiliationCode = "627001")
        //     )
        // 
        //     assertEquals(accessTokenValue, actual.accessTokenValue)
        //     assertEquals(refreshTokenValue, actual.refreshTokenValue)
        // }
    }

    @Nested
    @DisplayName("トークン検証処理が想定通り行えること")
    inner class Scenario2 {

        @Test
        @DisplayName("有効期限切れのトークンを検証した場合、TokenExpiredExceptionがスローされること")
        fun case01() {
            val jwtConfig = AuthConfig.Jwt(
                AuthConfig.Jwt.TokenConfig("access_token_key", 3),
                AuthConfig.Jwt.TokenConfig("refresh_token_key", 3),
                cookieConfig
            )
            val authConfig = stubAuthConfig(jwtConfig)

            val processor = JwtAuthProcessor(
                authConfig,
                SecretManagerRepository(
                    MockSecretManagerClient.of(
                        config = jwtConfig,
                        accessTokenSecretKey = """
                            {
                                "jwt_key":"access_token_secret_key"
                            }
                        """.trimIndent(),
                        refreshTokenSecretKey = """
                            {
                                "jwt_key":"refresh_token_secret_key"
                            }
                        """.trimIndent()
                    )
                )
            )

            val authToken = processor.gen(employee = stubEmployee())

            // トークンの有効期限が3秒なので、作成してすぐは、検証は成功する
            try {
                processor.verify(AuthTokenType.ACCESS_TOKEN, authToken.accessTokenValue)
                processor.verify(AuthTokenType.REFRESH_TOKEN, authToken.refreshTokenValue)
            } catch (e: Exception) {
                fail()
            }

            // トークンの有効期限が3秒なので、3秒待機すると、有効期限切れとなる
            Thread.sleep(3000)

            assertThrows<TokenExpiredException> {
                processor.verify(AuthTokenType.ACCESS_TOKEN, authToken.accessTokenValue)
            }
            assertThrows<TokenExpiredException> {
                processor.verify(AuthTokenType.REFRESH_TOKEN, authToken.refreshTokenValue)
            }
        }

        @Test
        @DisplayName("異なる秘密鍵で署名されたトークンを検証した場合、JWTVerificationExceptionがスローされること")
        fun case02() {
            val jwtConfig = AuthConfig.Jwt(
                AuthConfig.Jwt.TokenConfig("access_token_key", 300),
                AuthConfig.Jwt.TokenConfig("refresh_token_key", 10800),
                cookieConfig
            )
            val authConfig = stubAuthConfig(jwtConfig)

            val processor1 = JwtAuthProcessor(
                authConfig,
                SecretManagerRepository(
                    MockSecretManagerClient.of(
                        config = jwtConfig,
                        accessTokenSecretKey = """
                            {
                                "jwt_key":"access_token_secret_key_1"
                            }
                        """.trimIndent(),
                        refreshTokenSecretKey = """
                            {
                                "jwt_key":"refresh_token_secret_key_1"
                            }
                        """.trimIndent()
                    )
                )
            )

            val processor2 = JwtAuthProcessor(
                authConfig,
                SecretManagerRepository(
                    MockSecretManagerClient.of(
                        config = jwtConfig,
                        accessTokenSecretKey = """
                            {
                                "jwt_key":"access_token_secret_key_2"
                            }
                        """.trimIndent(),
                        refreshTokenSecretKey = """
                            {
                                "jwt_key":"refresh_token_secret_key_2"
                            }
                        """.trimIndent()
                    )
                )
            )

            val authToken1 = processor1.gen(employee = stubEmployee())

            // processor1で作成したトークンは、processor1による検証は成功する
            try {
                processor1.verify(AuthTokenType.ACCESS_TOKEN, authToken1.accessTokenValue)
                processor1.verify(AuthTokenType.REFRESH_TOKEN, authToken1.refreshTokenValue)
            } catch (e: Exception) {
                fail()
            }

            // processor1で作成したトークンは、processor2による検証は失敗する
            assertThrows<SignatureVerificationException> {
                processor2.verify(AuthTokenType.ACCESS_TOKEN, authToken1.accessTokenValue)
            }
            assertThrows<SignatureVerificationException> {
                processor2.verify(AuthTokenType.REFRESH_TOKEN, authToken1.refreshTokenValue)
            }
        }
    }
}
