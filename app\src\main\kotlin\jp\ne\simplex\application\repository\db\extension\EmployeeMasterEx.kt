package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Company
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.db.jooq.gen.tables.pojos.EmployeeMasterPojo
import org.slf4j.LoggerFactory

class EmployeeMasterEx {

    companion object {

        private val log = LoggerFactory.getLogger(EmployeeMasterEx::class.java)

        fun EmployeeMasterPojo.getEmployee(): Employee? {
            return try {
                Employee(
                    Employee.Code(this.employeeNumber),
                    Employee.Name.of(this.nameKanji),
                    this.affiliationCode,
                    Company.of(this.companyCode),
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize EmployeeMaster record. $this")
                null
            }
        }
    }
}
