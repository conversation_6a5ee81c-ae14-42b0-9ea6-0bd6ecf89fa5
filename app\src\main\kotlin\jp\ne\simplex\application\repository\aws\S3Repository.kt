package jp.ne.simplex.application.repository.aws

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import java.nio.file.Files
import java.nio.file.Path

/**
 * AWS S3操作のためのリポジトリ実装
 */
@Repository
class S3Repository(private val s3Client: S3Client) : S3RepositoryInterface {

    companion object {
        private val log = LoggerFactory.getLogger(S3Repository::class.java)
    }

    override fun uploadFile(
        localFilePath: Path,
        s3Key: String,
        bucketName: String
    ): String {

        // S3クライアントの作成
        try {
            // ファイルの内容を読み込む
            val fileContent = Files.readAllBytes(localFilePath)
            val requestBody = RequestBody.fromBytes(fileContent)

            // S3にアップロードするリクエストを作成
            val request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(s3Key)
                .build()

            // S3にアップロード
            s3Client.putObject(request, requestBody)
            log.info("Successfully uploaded vacancy parking data to S3: bucket=$bucketName, key=$s3Key")

            return s3Key
        } catch (e: Exception) {
            log.error("Failed to upload vacancy parking data to S3", e)
            throw e
        }
    }

    override fun getLatestFile(
        bucketName: String,
    ): ResponseInputStream<GetObjectResponse> {

        val listRequest =
            ListObjectsV2Request.builder()
                .bucket(bucketName)
                .build()

        val response = s3Client.listObjectsV2(listRequest)

        val latestCsvFile = response.contents()
            .maxByOrNull { it.lastModified() }
            ?: throw NoSuchElementException(
                "No CSV file found in bucket $bucketName."
            )

        return getObject(latestCsvFile.key(), bucketName)

    }

    private fun getObject(
        s3Key: String,
        bucketName: String,
    ): ResponseInputStream<GetObjectResponse> {
        val request = GetObjectRequest.builder()
            .bucket(bucketName)
            .key(s3Key)
            .build()
        return s3Client.getObject(request)
    }
}

/**
 * AWS S3操作のためのリポジトリインターフェース
 */
interface S3RepositoryInterface {
    /**
     * S3にファイルをアップロードする
     *
     * @param bucketName アップロード対象バケット
     * @param s3Key アップロード時のS3キー
     * @param localFilePath アップロード対象ファイルのファイルパス
     *
     * @return アップロードされたS3のキー
     */
    fun uploadFile(
        localFilePath: Path,
        s3Key: String,
        bucketName: String
    ): String

    /**
     * S3から最新のファイルのストリームを取得する
     *
     * @param bucketName ダウンロード対象バケット
     * @param localFilePath ダウンロード先のローカルファイルパス
     *
     * @return S3の最新ファイルのストリーム
     */
    fun getLatestFile(
        bucketName: String,
    ): ResponseInputStream<GetObjectResponse>
}
