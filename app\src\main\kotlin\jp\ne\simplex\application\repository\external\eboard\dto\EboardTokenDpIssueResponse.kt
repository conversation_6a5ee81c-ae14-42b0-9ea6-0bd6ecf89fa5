package jp.ne.simplex.application.repository.external.eboard.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.repository.external.eboard.config.EboardResponse
import jp.ne.simplex.log.MaskTarget
import jp.ne.simplex.log.MaskValue

@MaskTarget
class EboardTokenDpIssueResponse(

    @MaskValue
    @JsonProperty("accesstoken")
    val accesstoken: String,
) : EboardResponse