package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.PropertyMaintenance
import jp.ne.simplex.application.model.UpdatePropertyMaintenance
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.filterBy
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.filterByPublicInstruction
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.getUpdateAdFf
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.getUpdatePublishStatus
import jp.ne.simplex.application.model.UpdatePropertyMaintenance.Companion.getUpdatePublishStatusWithUpState
import jp.ne.simplex.application.repository.db.PropertyMaintenanceRepositoryInterface
import jp.ne.simplex.application.repository.db.PropertyRepositoryInterface
import jp.ne.simplex.application.repository.db.TemporaryReservationRepositoryInterface
import jp.ne.simplex.application.repository.external.dkportal.DKPortalRepositoryInterface
import jp.ne.simplex.application.repository.external.eboard.EboardRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.springframework.stereotype.Service

@Service
class PropertyMaintenanceService(
    private val propertyRepository: PropertyRepositoryInterface,
    private val temporaryReservationRepository: TemporaryReservationRepositoryInterface,
    private val repository: PropertyMaintenanceRepositoryInterface,
    private val eboardRepository: EboardRepositoryInterface,
    private val dkportalRepository: DKPortalRepositoryInterface
) {
    fun update(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance>
    ): List<UpdatePropertyMaintenance.FailedDetail> {
        val propertyList = propertyRepository.list(parameter.map { it.id })
        if (propertyList.size != parameter.size) {
            throw ServerValidationException(ErrorMessage.INVALID_PROPERTY_SPECIFIED.format())
        }

        val publicInstructionList = parameter.filterByPublicInstruction()
        val propertyMaintenanceList =
            repository.listBy(publicInstructionList.map { it.id }).associateBy { it.propertyId }

        publicInstructionList.forEach { request ->
            val propertyMaintenance = propertyMaintenanceList[request.id]
            // 非公開->公開時のみバリデーションを実施。公開->公開時（AD/FF金額のみの更新）は不要
            if (propertyMaintenance == null || propertyMaintenance.publishStatus == PropertyMaintenance.PublishStatus.PRIVATE) {
                val property = propertyList.firstOrNull { it.id == request.id }
                    ?: throw ServerValidationException(
                        ErrorMessage.PROPERTY_NOT_FOUND.format(
                            request.id.buildingCode.value,
                            request.id.roomCode.value
                        )
                    )

                validatePrivateToPublicInstruction(property, request)

            }
        }

        /**
         * 物件メンテナンスAPIで更新できる項目（公開指示/AD・FF）を更新する
         * それぞれの項目は、以下の通り、主管サービスが異なるため、外部APIで更新した結果成功したリクエストのみをDBに反映させる流れとする
         * 　・公開指示：いい物件ボード
         * 　・AD・FF：DKポータル
         *
         * 外部APIが成功したリクエストのみDBに永続化するため、一部データの更新に失敗しても業務上問題にならないように以下の順番で処理を行う
         * ・AD・FF -> 公開指示
         **/

        return updateAdFf(requestUser, parameter, propertyList).let {
            updateInstructPublic(
                requestUser,
                parameter,
                propertyList,
                it
            )
        }
    }

    private fun validatePrivateToPublicInstruction(
        property: Property,
        request: UpdatePropertyMaintenance
    ) {
        if (property.changeDivision == null || property.customerCompletionFlag) {
            // 公開不可物件は公開できない(AD/FFは更新可能)
            throw ServerValidationException(ErrorMessage.PUBLISH_UNAVAILABLE_PROPERTY_NOT_ALLOWED.format())
        }

        val temporaryReservation = temporaryReservationRepository.findBy(
            request.id
        )
        if (property.isTemporaryReserved(temporaryReservation)) {
            // 仮押さえ中物件は公開指示を行うことができない
            throw ServerValidationException(ErrorMessage.PUBLISH_UNAVAILABLE_STATUS_TEMPORARY_RESERVATION.format())
        }
        if (property.isApplicationSubmitted(temporaryReservation)) {
            // 申込済み物件は公開指示を行うことができない
            throw ServerValidationException(ErrorMessage.PUBLISH_UNAVAILABLE_STATUS_APPLICATION_SUBMITTED.format())
        }

        if (property.direction == null) {
            // 方位が未設定の場合は公開不可
            throw ServerValidationException(ErrorMessage.DIRECTION_NOT_SET.format())
        }
    }

    private fun updateAdFf(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance>,
        propertyList: List<Property>,
    ): UpdatePropertyMaintenance.Result<List<Property.Id>, List<UpdatePropertyMaintenance.FailedDetail>> {
        dkportalRepository.updateAdff(
            parameter.getUpdateAdFf(propertyList)
        )
            .let { result ->
                repository.updateAdFf(
                    requestUser,
                    parameter.filterBy(result.success).getUpdateAdFf(propertyList)
                )

                return UpdatePropertyMaintenance.Result(
                    result.success,
                    result.failed.map { id ->
                        UpdatePropertyMaintenance.FailedDetail(
                            id = id,
                            adFfUpdateResult = UpdatePropertyMaintenance.FailedDetail.Status.FAILED,
                            publicInstructionResult = UpdatePropertyMaintenance.FailedDetail.Status.SKIP
                        )
                    }
                )
            }
    }

    private fun updateInstructPublic(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance>,
        propertyList: List<Property>,
        adFfResult: UpdatePropertyMaintenance.Result<List<Property.Id>, List<UpdatePropertyMaintenance.FailedDetail>>,
    ): List<UpdatePropertyMaintenance.FailedDetail> {
        eboardRepository.instructPublic(
            requestUser,
            parameter.filterBy(adFfResult.success).getUpdatePublishStatus(propertyList)
        )
            .let { result ->
                val filteredBySuccess = parameter.filterBy(result.success.keys.toList())

                // DKポータルへの公開指示のデータ連携は、ただの通知（主管サービスではない）なので、
                // 非同期で行いレスポンスのハンドリングも行わない
                CoroutineScope(Dispatchers.IO).launch {
                    dkportalRepository.updateRoomStatus(
                        filteredBySuccess.getUpdatePublishStatusWithUpState(
                            propertyList,
                            result.success
                        )
                    )
                }

                repository.updatePublishStatus(
                    requestUser,
                    filteredBySuccess.getUpdatePublishStatus(propertyList),
                )

                return adFfResult.failed + result.failed.map { id ->
                    UpdatePropertyMaintenance.FailedDetail(
                        id = id,
                        adFfUpdateResult = UpdatePropertyMaintenance.FailedDetail.Status.SUCCESS,
                        publicInstructionResult = UpdatePropertyMaintenance.FailedDetail.Status.FAILED
                    )
                }
            }
    }
}
