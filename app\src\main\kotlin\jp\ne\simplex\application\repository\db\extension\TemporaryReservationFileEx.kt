package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.TemporaryReservationInfo.*
import jp.ne.simplex.db.jooq.gen.tables.pojos.TemporaryReservationFilePojo
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import org.slf4j.LoggerFactory

class TemporaryReservationFileEx {

    companion object {

        private val log = LoggerFactory.getLogger(TemporaryReservationFileEx::class.java)

        /**
         * 解除された仮押さえされた情報
         */
        fun TemporaryReservationFilePojo.getCancelledTemporaryReservationInfo(): TemporaryReservationInfo? {
            return try {
                CancelledTemporaryReservationInfo(
                    id = this.getId()!!,
                    comment = TemporaryReservation.Comment.of(comment),
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize TemporaryReservationFile record. $this")
                null
            }
        }

        /**
         * 自社の従業員によって仮押さえされた情報
         *  @param branch customerRepBranchCd を元に、KtAllBranch/AffiliationMaster で検索した結果取得した支店
         *  @param employee applicationScheduledPersonCd を元に、EmployeeMaster で検索した結果取得した従業員
         */
        // @formatter:off
        fun TemporaryReservationFilePojo.getOwnCompanyTemporaryReservationInfo(branch: Branch, employee: Employee): TemporaryReservationInfo? {
            // @formatter:on
            return try {
                OwnCompanyTemporaryReservationInfo(
                    id = this.getId()!!,
                    assignedBranch = branch,
                    assignedEmployee = employee,
                    scheduledMoveInDate = applicationScheduledDate?.yyyyMMdd()!!,
                    comment = TemporaryReservation.Comment.of(comment),
                    version = this.getVersion()!!
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize TemporaryReservationFile record. $this")
                null
            }
        }

        /** 他社（不動産会社）によって仮押さえされた情報 */
        fun TemporaryReservationFilePojo.getOtherCompanyTemporaryReservationInfo(): TemporaryReservationInfo? {
            return try {
                OtherCompanyTemporaryReservationInfo(
                    id = this.getId()!!,
                    scheduledMoveInDate = applicationScheduledDate?.yyyyMMdd()!!,
                    comment = TemporaryReservation.Comment.of(comment),
                    otherCompanyInfo = this.getOtherCompanyInfo()!!,
                    version = this.getVersion()!!
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize TemporaryReservationFile record. $this")
                null
            }
        }

        fun TemporaryReservationFilePojo.isAlreadyCancelled(): Boolean {
            return applicationScheduledDate.isNullOrEmpty()
        }

        fun TemporaryReservationFilePojo.getEmployeeCode(): Employee.Code? {
            return applicationScheduledPersonCd?.let { Employee.Code.of(it) }
        }

        fun TemporaryReservationFilePojo.getBranchCode(): Branch.Code? {
            return customerRepBranchCd?.let { Branch.Code.of(it) }
        }

        fun TemporaryReservationFilePojo.isRegisteredByOtherCompany(): Boolean {
            return otherCompanyFlag == "1"
        }

        fun TemporaryReservationFilePojo.getVersion(): TemporaryReservation.Version? {
            return registrationDate?.let { date ->
                linkCdRegistrationTime?.let { time ->
                    TemporaryReservation.Version.of(date, time)
                }
            }
        }

        private fun TemporaryReservationFilePojo.getId(): Property.Id? {
            return buildingCd?.let { buildingCode ->
                roomCd?.let { roomCode ->
                    Property.Id(
                        Building.Code.of(buildingCode),
                        Room.Code.of(roomCode)
                    )
                }
            }
        }

        private fun TemporaryReservationFilePojo.getOtherCompanyInfo(): TemporaryReservation.OtherCompanyInfo? {
            if (this.isRegisteredByOtherCompany()) {
                return TemporaryReservation.OtherCompanyInfo(
                    companyCode = contractFormECode!!,
                    companyName = otherCompanyName!!,
                    storeName = otherCompanyStoreName!!,
                    staffName = otherCompanyRepName!!,
                )
            }
            return null
        }

    }
}
