package jp.ne.simplex.application.batch

enum class ExecuteOptionType(val optionString: String) {
    RERUN("RERUN"),
    RERUN_ONLY_SEND_FILE("RERUN_ONLY_SEND_FILE");

    companion object {
        fun fromValue(optionString: String): ExecuteOptionType {
            return entries.find { it.optionString == optionString }
                ?: throw IllegalArgumentException("Invalid optionString: $optionString")
        }
    }
}
