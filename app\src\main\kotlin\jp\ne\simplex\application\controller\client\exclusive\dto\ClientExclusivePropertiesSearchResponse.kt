package jp.ne.simplex.application.controller.client.exclusive.dto

import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.ExclusivePropertyInfo
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd

data class ClientExclusivePropertiesSearchResponse(

    @field:Schema(description = "リスト")
    val list: List<ClientExclusivePropertiesSearchResponseItem>,

    @field:Schema(description = "総件数", example = "100")
    val totalCount: Int,
) {
    companion object {
        fun of(
            list: List<ExclusivePropertyInfo>,
            totalCount: Int
        ): ClientExclusivePropertiesSearchResponse {
            return ClientExclusivePropertiesSearchResponse(
                list.map { item ->
                    ClientExclusivePropertiesSearchResponseItem.of(item)
                },
                totalCount = totalCount
            )
        }
    }
}

data class ClientExclusivePropertiesSearchResponseItem(
    @field:Schema(description = "先行公開ID", example = "174554456383418101")
    val id: String,

    @field:Schema(description = "建物CD", example = "004665401")
    val buildingCode: String,

    @field:Schema(description = "部屋CD", example = "01010")
    val roomCode: String,

    @field:Schema(description = "物件名", example = "ジュノン扇")
    val propertyName: String?,

    @field:Schema(description = "先行期間From", example = "20240406")
    val exclusiveFrom: String,

    @field:Schema(description = "先行期間To", example = "20241001")
    val exclusiveTo: String,

    @field:Schema(description = "先行先名", example = "リーシング")
    val exclusiveTargetName: String?,

    @field:Schema(description = "先行先情報")
    val exclusiveTargetInfo: ClientExclusiveTargetDto,

    @field:Schema(description = "作成日", example = "20230114")
    val createDate: String,

    @field:Schema(description = "作成者", example = "鈴木 たろう")
    val creator: String?,

    @field:Schema(description = "削除日", example = "20250115")
    val deleteDate: String?,

    @field:Schema(description = "削除者", example = "鈴木 たろう")
    val deleter: String?,

    ) {
    companion object {
        fun of(info: ExclusivePropertyInfo): ClientExclusivePropertiesSearchResponseItem {
            val propertyName = when {
                info.buildingName == null -> null
                info.roomNumber == null -> info.buildingName
                else -> "${info.buildingName} ${info.roomNumber.getFormattedValue()}"
            }

            return ClientExclusivePropertiesSearchResponseItem(
                id = info.id.value.toString(),
                buildingCode = info.propertyId.buildingCode.value,
                roomCode = info.propertyId.roomCode.value,
                propertyName = propertyName,
                exclusiveFrom = info.exclusiveRange.from.yyyyMMdd(),
                exclusiveTo = info.exclusiveRange.to.yyyyMMdd(),
                exclusiveTargetName = info.exclusiveTargetName,
                exclusiveTargetInfo = ClientExclusiveTargetDto.of(info.exclusiveTarget),
                createDate = info.createDate.yyyyMMdd(),
                creator = info.creator?.kanji,
                deleteDate = if (info.earlyClosureFlag || info.deleteFlag) info.updateDate.yyyyMMdd() else null, // 早期終了と削除済の場合のみ返却される
                deleter = if (info.earlyClosureFlag || info.deleteFlag) info.updater?.kanji else null, // 早期終了と削除済の場合のみ返却される
            )
        }
    }
}
