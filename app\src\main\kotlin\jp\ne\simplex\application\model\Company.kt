package jp.ne.simplex.application.model

sealed class Company private constructor(
    val code: String,
    val name: String,
) {

    data object DaitouKentaku : Company(
        code = "001",
        name = "大東建託",
    )

    data object DaitouKentakuPartners : Company(
        code = "018",
        name = "大東建託パートナーズ",
    )

    data object DaitouKentakuLeasing : Company(
        code = "160",
        name = "大東建託リーシング",
    )

    data class OtherCompany(private val value: String?) : Company(
        code = value ?: "",
        name = "",
    )

    companion object {
        fun of(value: String?): Company {
            return when (value) {
                DaitouKentaku.code -> DaitouKentaku
                DaitouKentakuPartners.code -> DaitouKentakuPartners
                DaitouKentakuLeasing.code -> DaitouKentakuLeasing
                else -> OtherCompany(value)
            }
        }
    }
}
