package jp.ne.simplex.application.controller.client.parking.dto

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.external.parking.dto.ExternalParkingDetailResponse
import jp.ne.simplex.application.model.Parking
import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingReservationInfo
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ClientParkingDetailResponse(
    @JsonProperty("buildings")
    @field:Schema(description = "建物駐車場詳細情報リスト")
    val buildings: List<ParkingBuildingResponse>,
    @JsonProperty("contractPossibleStatus")
    @field:Schema(description = "契約可否ステータス")
    val contractPossibleStatus: ExternalParkingDetailResponse.ContractPossibleStatusResponse,
    @JsonProperty("roomCount")
    @field:Schema(
        description = "総戸数",
        example = "0"
    )
    val roomCount: Int,
    @JsonProperty("vacantRoomCount")
    @field:Schema(
        description = "空き戸数",
        example = "0"
    )
    val vacantRoomCount: Int,
    @JsonProperty("allowAllParkingLotAvailabilityEdit")
    @field:Schema(
        description = "駐車場区画利用不可設定全ユーザ編集許可フラグ",
        example = "true"
    )
    val allowAllParkingLotAvailabilityEdit: Boolean,
) {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ParkingBuildingResponse(
        //建物情報
        @JsonProperty("buildingCode")
        @field:Schema(
            description = "建物コード",
            example = "025102002"
        )
        val buildingCode: String,
        @JsonProperty("buildingName")
        @field:Schema(
            description = "建物名称",
            example = "ルジーナ ベルデ"
        )
        val buildingName: String?,
        @JsonProperty("buildingContractForm")
        @field:Schema(
            description = "建物契約形態",
            example = "1"
        )
        val buildingContractForm: String?,
        @JsonProperty("landlordName")
        @field:Schema(
            description = "家主名",
            example = "山田太郎"
        )
        val landlordName: String?,
        @JsonProperty("completionDeliveryDate")
        @field:Schema(
            description = "完成日",
            example = "20250101"
        )
        val completionDeliveryDate: String?,
        @JsonProperty("location")
        @field:Schema(
            description = "建物住所",
            example = "東京都 足立区 扇1丁目 31-30"
        )
        val location: String?,
        @JsonProperty("layoutImageFileName")
        @field:Schema(
            description = "配置図画像ファイル名",
            example = "025/102/025102002.jpg"
        )
        val layoutImageFileName: String?,
        @JsonProperty("dumpsterImageFileName")
        @field:Schema(
            description = "ゴミ置き場画像画像ファイル名",
            example = "025/102/025102002.jpg"
        )
        val dumpsterImageFileName: String?,
        @JsonProperty("parkingLots")
        @field:Schema(
            description = "駐車場区画情報リスト",
        )
        val parkingLots: List<ParkingLotResponse>,
        @JsonProperty("businessOfficeCode")
        @field:Schema(description = "営業所コード", example = "924")
        val businessOfficeCode: String?
    ) {
        companion object {
            fun from(
                parking: Parking,
                isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge
            ): ParkingBuildingResponse {
                return ParkingBuildingResponse(
                    buildingCode = parking.building.code.value,
                    buildingName = parking.building.name.value,
                    buildingContractForm = parking.building.buildingContractForm,
                    landlordName = parking.building.landlordName,
                    completionDeliveryDate = parking.building.completionDeliveryDate?.yyyyMMdd(),
                    location = parking.building.location,
                    layoutImageFileName = parking.building.code.toLayoutImageUrl(),
                    dumpsterImageFileName = parking.building.code.toLayoutImageUrl(), // ゴミ置き場画像は駐車場配置図と同じファイル名
                    parkingLots = parking.parkingLotList.map { lot ->
                        ParkingLotResponse.from(lot, isAutoJudge)
                    },
                    businessOfficeCode = parking.building.businessOfficeCode?.value
                )
            }
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ParkingLotResponse(
        // 駐車場区画情報
        @JsonProperty("parkingLotCode")
        @field:Schema(
            description = "駐車場コード",
            example = "001"
        )
        val parkingLotCode: String,
        @JsonProperty("status")
        @field:Schema(
            description = "利用状況",
            example = "0"
        )
        val status: String?,
        @JsonProperty("localDisplayNumber")
        @field:Schema(
            description = "現地表示",
            example = "1"
        )
        val localDisplayNumber: String?,
        @JsonProperty("parkingFee")
        @field:Schema(
            description = "賃料",
            example = "1000"
        )
        val parkingFee: Int?,
        @JsonProperty("parkingFeeInTax")
        @field:Schema(
            description = "賃料税込サイン",
            example = "1"
        )
        val parkingFeeInTax: Int?,
        @JsonProperty("parkingCategory")
        @field:Schema(
            description = "駐車区分",
            example = "1"
        )
        val parkingCategory: String?,
        @JsonProperty("assessmentDivision")
        @field:Schema(
            description = "査定区分",
            example = "1"
        )
        val assessmentDivision: String?,
        @JsonProperty("offSiteParkingCategory")
        @field:Schema(
            description = "敷地内外区分",
            example = "1"
        )
        val offSiteParkingCategory: String,
        @JsonProperty("distance")
        @field:Schema(
            description = "敷地外距離",
            example = "100"
        )
        val distance: Int?,
        //駐車場付随情報
        @JsonProperty("contractForm")
        @field:Schema(
            description = "契約形態",
            example = "1"
        )
        val contractForm: String?,
        @JsonProperty("firstParkingContractPossibility")
        @field:Schema(
            description = "契約可否",
            example = "1"
        )
        val firstParkingContractPossibility: String?,
        @JsonProperty("reservePossibility")
        @field:Schema(
            description = "予約可否",
            example = "1"
        )
        val reservePossibility: String?,
        @JsonProperty("possibleUseDate")
        @field:Schema(
            description = "利用可能日",
            example = "20240101"
        )
        val possibleUseDate: String?,
        @JsonProperty("specialContractFlag")
        @field:Schema(
            description = "特約有無",
            example = "0"
        )
        val specialContractFlag: String?,
        @JsonProperty("parkingLotEnable")
        @field:Schema(
            description = "区画利用可否",
            example = "true"
        )
        val parkingLotEnable: Boolean?,
        // テナント契約情報
        @JsonProperty("tenantContract")
        @field:Schema(
            description = "入居者契約情報",
        )
        val tenantContract: TenantContractResponse?,
        // 予約情報
        @JsonProperty("reservations")
        @field:Schema(
            description = "予約情報情報",
        )
        val reservations: List<ParkingReservationResponse>?,
    ) {
        companion object {
            fun from(
                parkingLot: ParkingLot,
                isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge
            ): ParkingLotResponse {
                val firstParkingContractPossibility =
                    parkingLot.convertFirstParkingContractPossibility(
                        parkingLot.vacancyParkingStatus,
                        isAutoJudge
                    )
                return ParkingLotResponse(
                    parkingLotCode = parkingLot.id.parkingLotCode.value,
                    status = parkingLot.parkingStatusDivision.value,
                    localDisplayNumber = parkingLot.localDisplayNumber?.trim(),
                    parkingFee = parkingLot.parkingFee,
                    parkingFeeInTax = parkingLot.parkingFeeInTax,
                    parkingCategory = parkingLot.parkingLotCategory?.value,
                    assessmentDivision = parkingLot.assessmentDivision?.value?.toString(),
                    offSiteParkingCategory = parkingLot.offSiteParkingLotCategory.value.toString(),
                    distance = parkingLot.offSiteParkingDistance,
                    contractForm = parkingLot.contractForm,
                    reservePossibility = parkingLot.vacancyParkingStatus.value.toString(),
                    firstParkingContractPossibility = firstParkingContractPossibility.value,
                    possibleUseDate = parkingLot.convertPossibleUseDate(
                        parkingLot.expectedMoveOutDate,
                        parkingLot.parkingStatusDivision,
                        firstParkingContractPossibility,
                        parkingLot.reservationList,
                        isAutoJudge
                    )?.yyyyMMdd(),
                    specialContractFlag = parkingLot.specialContractFlag?.value?.toString(),
                    parkingLotEnable = parkingLot.isAvailable,
                    tenantContract = if (parkingLot.tenant != null) {
                        TenantContractResponse.from(parkingLot, parkingLot.tenant)
                    } else null,
                    reservations = parkingLot.reservationList.map { reservation ->
                        ParkingReservationResponse.from(reservation)
                    }
                )
            }
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class TenantContractResponse(
        //テナント契約情報
        @JsonProperty("tenantContractNumber")
        @field:Schema(
            description = "テナント契約番号",
            example = "11709540"
        )
        val tenantContractNumber: String?,
        @JsonProperty("originalContractNumber")
        @field:Schema(
            description = "元テナント契約番号",
            example = "11709539"
        )
        val originalContractNumber: String?,
        @JsonProperty("propertyTenantName")
        @field:Schema(
            description = "部屋入居者",
            example = "部屋入居三郎"
        )
        val propertyTenantName: String?,
        @JsonProperty("propertyTenantContractName")
        @field:Schema(
            description = "部屋契約者",
            example = "部屋契約者四郎"
        )
        val propertyTenantContractName: String?,
        @JsonProperty("tenantContractName")
        @field:Schema(
            description = "駐車場契約者",
            example = "駐車場契約契約次郎"
        )
        val tenantContractName: String?,
        @JsonProperty("tenantBuildingCode")
        @field:Schema(
            description = "テナント契約建物番号",
            example = "025102001"
        )
        val tenantBuildingCode: String?,
        @JsonProperty("tenantRoomCode")
        @field:Schema(
            description = "テナント契約部屋番号",
            example = "01010"
        )
        val tenantRoomCode: String?,
        @JsonProperty("tenantRoomNumber")
        @field:Schema(
            description = "テナント契約部屋番号",
            example = "0101"
        )
        val tenantRoomNumber: String?,
        @JsonProperty("moveInScheduledDate")
        @field:Schema(
            description = "入居予定日",
            example = "20240101"
        )
        val moveInScheduledDate: String?,
        @JsonProperty("expectedMoveOutDate")
        @field:Schema(
            description = "退去予定日",
            example = "20250101"
        )
        val expectedMoveOutDate: String?,
        //車種情報
        @JsonProperty("carNumber")
        @field:Schema(
            description = "車両ナンバー情報",
        )
        val carNumber: CarNumberResponse?,
        @JsonProperty("manufacturerDivision")
        @field:Schema(
            description = "メーカー区分",
            example = "1"
        )
        val manufacturerDivision: String?,
        @JsonProperty("carModelName")
        @field:Schema(
            description = "車名",
            example = "レクサス"
        )
        val carModelName: String?,
        @JsonProperty("parkingCertIssueSign")
        @field:Schema(
            description = "車庫証明発給サイン",
            example = "1"
        )
        val parkingCertIssueSign: String?,
        @JsonProperty("parkingCertComment")
        @field:Schema(
            description = "車庫証明コメント",
            example = "コメント"
        )
        val parkingCertComment: String?,
    ) {
        companion object {
            fun from(parkingLot: ParkingLot, tenant: ParkingLot.Tenant): TenantContractResponse {
                return TenantContractResponse(
                    tenantContractNumber = tenant.tenantContractNumber,
                    originalContractNumber = tenant.originalContractNumber,
                    tenantContractName = tenant.tenantContractName,
                    propertyTenantName = tenant.propertyTenantName,
                    propertyTenantContractName = tenant.propertyTenantContractName,
                    tenantBuildingCode = parkingLot.linkedPropertyId?.buildingCode?.value,
                    tenantRoomCode = parkingLot.linkedPropertyId?.roomCode?.value,
                    tenantRoomNumber = parkingLot.linkedRoomNumber?.getFormattedValue(),
                    moveInScheduledDate = parkingLot.moveInScheduledDate?.yyyyMMdd(),
                    expectedMoveOutDate = parkingLot.expectedMoveOutDate?.yyyyMMdd(),
                    carNumber = CarNumberResponse.from(tenant),
                    manufacturerDivision = tenant.manufacturerDivision,
                    carModelName = tenant.carModelName,
                    parkingCertIssueSign = tenant.parkingCertIssueSign,
                    parkingCertComment = tenant.parkingCertComment
                )
            }
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class CarNumberResponse(
        @JsonProperty("landTransportName")
        @field:Schema(
            description = "陸事名",
            example = "東京都"
        )
        val landTransportName: String?,
        @JsonProperty("type")
        @field:Schema(
            description = "車種分類番号",
            example = "400"
        )
        val type: String?,
        @JsonProperty("businessCategory")
        @field:Schema(
            description = "用途分類番号",
            example = "さ"
        )
        val businessCategory: String?,
        @JsonProperty("leftNumber")
        @field:Schema(
            description = "左ナンバー",
            example = "12"
        )
        val leftNumber: String?,
        @JsonProperty("rightNumber")
        @field:Schema(
            description = "右ナンバー",
            example = "34"
        )
        val rightNumber: String?
    ) {
        companion object {
            fun from(tenant: ParkingLot.Tenant): CarNumberResponse {
                return CarNumberResponse(
                    landTransportName = tenant.landTransportName,
                    type = tenant.type,
                    businessCategory = tenant.businessCategory,
                    leftNumber = tenant.leftNumber,
                    rightNumber = tenant.rightNumber
                )
            }
        }
    }

    companion object {
        fun from(
            parkingList: List<Parking>,
            contractPossibility: ParkingContractPossibility,
            roomCounts: Int,
            vacantRooms: Int
        ): ClientParkingDetailResponse {
            // 総区画数が0であれば建物情報は返却しない
            val buildings = if (parkingList.fold(0) { sum, parking -> sum + parking.parkingLotList.size } == 0) {
                emptyList()
            } else {
                parkingList.map { ParkingBuildingResponse.from(it, contractPossibility.isAutoJudge) }
            }
            return ClientParkingDetailResponse(
                buildings,
                ExternalParkingDetailResponse.from(contractPossibility),
                roomCount = roomCounts,
                vacantRoomCount = vacantRooms,
                allowAllParkingLotAvailabilityEdit = parkingList.firstOrNull()?.allowAllParkingLotAvailabilityEdit
                    ?: false
            )
        }
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ParkingReservationResponse(
    @JsonProperty("reservationId")
    @field:Schema(
        description = "予約ID",
        example = "aff6ae28-8a65-f188-1ee2-2e9903ca55b7"
    )
    val reservationId: String,
    @JsonProperty("reservationStatus")
    @field:Schema(
        description = "予約状態(0: 仮申込, 1: 受付, 2: 契約済み, 3: キャンセル)",
        example = "1"
    )
    val reservationStatus: String,
    @JsonProperty("reservationType")
    @field:Schema(
        description = "予約種別(0: 仮押さえ(自動), 1: 仮押さえ(手動), 2: 作業, 3: 場所変更, 4: 1日利用)",
        example = "1"
    )
    val reservationType: String,
    @JsonProperty("reserveStartDate")
    @field:Schema(description = "利用開始日(yyyyMMdd)", example = "20250215")
    val reserveStartDate: String? = null,
    @JsonProperty("reserveEndDate")
    @field:Schema(description = "利用終了日(yyyyMMdd)", example = "20260215")
    val reserveEndDate: String? = null,
    @JsonProperty("receptionStaff")
    @field:Schema(description = "受付担当者氏名", example = "田邊宗一郎")
    val receptionStaff: String? = null,
    @JsonProperty("receptionDate")
    @field:Schema(description = "受付日", example = "20170901")
    val receptionDate: String? = null,
    @JsonProperty("reserverName")
    @field:Schema(description = "利用者氏名", example = "日下部慎一郎")
    val reserverName: String? = null,
    @JsonProperty("reserverTel")
    @field:Schema(description = "利用者電話番号", example = "090-1234-5678")
    val reserverTel: String? = null,
    @JsonProperty("remarks")
    @field:Schema(description = "予約メモ", example = "コメント")
    val remarks: String? = null,
    @JsonProperty("reserverSystem")
    @field:Schema(
        description = "予約システム(0: DKリンク, 1: キマルームサイン, 2: ウェルカムパーク)",
        example = "1"
    )
    val reserverSystem: String? = null,
    @JsonProperty("updateDateTime")
    @field:Schema(description = "更新日時", example = "2024/08/30 12:00:00")
    val updateDateTime: String? = null,
) {
    companion object {
        fun from(reservation: ParkingReservationInfo): ParkingReservationResponse {
            return ParkingReservationResponse(
                reservationId = reservation.id.value,
                reservationStatus = reservation.status.value,
                reservationType = reservation.reservationType.value,
                reserveStartDate = reservation.reserveStartDatetime?.yyyyMMdd(),
                reserveEndDate = reservation.reserveEndDatetime?.yyyyMMdd(),
                receptionStaff = reservation.receptionStaff,
                receptionDate = reservation.receptionDate.yyyyMMdd(),
                reserverName = reservation.reserverName,
                reserverTel = reservation.reserverTel?.value,
                remarks = reservation.remarks?.value,
                reserverSystem = reservation.requestSource?.value,
                updateDateTime = reservation.updateDateTime
            )
        }
    }
}
