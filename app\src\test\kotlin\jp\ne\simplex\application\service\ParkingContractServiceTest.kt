package jp.ne.simplex.application.service

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.batch.BatchInterface
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.application.repository.db.ParkingContractPossibilityRepository
import jp.ne.simplex.application.repository.db.ParkingContractPossibilityRepositoryInterface
import jp.ne.simplex.application.repository.db.ParkingDetailsRepositoryInterface
import jp.ne.simplex.application.repository.db.VacantParkingListRepositoryInterface
import jp.ne.simplex.application.repository.external.dkportal.DKPortalRepositoryInterface
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_CONTRACT_POSSIBILITY
import jp.ne.simplex.exception.DBValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.mock.*
import jp.ne.simplex.shared.CoroutineHelper.Companion.runAsyncTasks
import jp.ne.simplex.shared.DSLContextEx.Companion.selectBy
import jp.ne.simplex.stub.*
import org.jooq.Configuration
import org.jooq.DSLContext
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class ParkingContractServiceTest : AbstractTestContainerTest() {

    override fun beforeEach() {}

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_CONTRACT_POSSIBILITY)
    }

    private fun parkingService(
        parkingRepository: MockParkingRepository = MockParkingRepository(),
        parkingDetailsRepository: ParkingDetailsRepositoryInterface = MockParkingDetailsRepository(),
        vacantParkingListRepository: VacantParkingListRepositoryInterface = MockVacantParkingListRepository()
    ): ParkingDetailsService {
        return ParkingDetailsService(
            parkingRepository = parkingRepository,
            parkingDetailsRepository = parkingDetailsRepository,
            vacantParkingListRepository = vacantParkingListRepository
        )
    }

    private fun parkingContractService(
        context: DSLContext = dslContext,
        parkingContractPossibilityRepository: ParkingContractPossibilityRepositoryInterface
        = MockParkingContractPossibilityRepository(),
        parkingDetailsService: ParkingDetailsService = parkingService(),
        parkingRepository: MockParkingRepository = MockParkingRepository(),
        dkPortalRepository: DKPortalRepositoryInterface = MockDKPortalRepository(),
    ): ParkingContractService {
        return ParkingContractService(
            context = context,
            parkingContractPossibilityRepository = parkingContractPossibilityRepository,
            parkingDetailsService = parkingDetailsService,
            parkingRepository = parkingRepository,
            dkPortalRepository = dkPortalRepository,
        )
    }

    @Nested
    @DisplayName("駐車場要問合せ設定更新の検証")
    inner class Scenario1 {
        // setup
        private val registerReq = stubUpdateParkingContractIsAutoJudge()
        private var dbUpdateCount = 0
        private var dkPortalApiCancelCallCount = 0

        @AfterEach
        fun tearDown() {
            dbUpdateCount = 0
            dkPortalApiCancelCallCount = 0
        }

        @Test
        @DisplayName("駐車場契約可否情報がない場合、DBの更新とDKポータルへの連携はされないこと")
        fun case1() {

            assertDoesNotThrow {
                parkingContractService(
                    parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                        findByIdFunc = { _ -> null },
                        updateFunc = { _, _ -> dbUpdateCount++ },
                    ),
                    dkPortalRepository = MockDKPortalRepository(
                        updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                    ),
                ).updateIsAutoJudge(stubJwtRequestUser(), registerReq)
            }

            assertEquals(dbUpdateCount, 0)
            assertEquals(dkPortalApiCancelCallCount, 0)
        }

        @Test
        @DisplayName("リクエストの要問合せフラグがDBの永続値と同じ場合、DBの更新とDKポータルへの連携はされないこと")
        fun case2() {

            assertDoesNotThrow {
                parkingContractService(
                    parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                        findByIdFunc = { _ -> stubParkingContractPossibility() },
                        updateFunc = { _, _ -> dbUpdateCount++ },
                    ),
                    dkPortalRepository = MockDKPortalRepository(
                        updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                    ),
                ).updateIsAutoJudge(
                    stubJwtRequestUser(),
                    stubUpdateParkingContractIsAutoJudge(
                        isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                    )
                )
            }

            assertEquals(dbUpdateCount, 0)
            assertEquals(dkPortalApiCancelCallCount, 0)
        }

        @Nested
        @DisplayName("リクエストの要問合せフラグがDBの永続値と異なる場合")
        inner class Scenario1 {

            @Test
            @DisplayName("更新前後でDKPortalでの表示内容が変わらない場合、DB更新はされるが、DKポータルへの連携はされないこと")
            fun case1() {

                assertDoesNotThrow {
                    parkingContractService(
                        parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                            findByIdFunc = { _ ->
                                stubParkingContractPossibility(
                                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                                    isAutoJudge = ContractPossibilityAutoJudge.MANUAL
                                )
                            },
                            updateFunc = { _, _ -> dbUpdateCount++ },
                        ),
                        dkPortalRepository = MockDKPortalRepository(
                            updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                        ),
                    ).updateIsAutoJudge(stubJwtRequestUser(), registerReq)
                }

                assertEquals(dbUpdateCount, 1)
                assertEquals(dkPortalApiCancelCallCount, 0)
            }

            @Test
            @DisplayName("更新前後でDKPortalでの表示内容が変わる場合、DB更新とDKポータルへの連携がされること")
            fun case2() {

                assertDoesNotThrow {
                    parkingContractService(
                        parkingContractPossibilityRepository =
                            MockParkingContractPossibilityRepository(
                                findByIdFunc = { _ ->
                                    stubParkingContractPossibility(
                                        isAutoJudge = ContractPossibilityAutoJudge.MANUAL
                                    )
                                },
                                updateFunc = { _, _ -> dbUpdateCount++ },
                            ),
                        dkPortalRepository = MockDKPortalRepository(
                            isNeedUpdateParkingLotFunc = { _, _ -> true },
                            updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                        ),
                    ).updateIsAutoJudge(stubJwtRequestUser(), registerReq)
                }

                assertEquals(dbUpdateCount, 1)

                // ここで非同期処理が完了するのを待つ
                Thread.sleep(1000)

                assertEquals(dkPortalApiCancelCallCount, 1)
            }
        }
    }

    @Nested
    @DisplayName("駐車場契約可否登録、更新時のレコードロックタイムアウトの検証")
    inner class Scenario2 {
        private val defaultOrderCode = Building.OrderCode.of("1000001")
        private lateinit var parkingContractPossibilityRepository: ParkingContractPossibilityRepository
        private lateinit var service: ParkingContractService

        @BeforeEach
        fun beforeEach() {
            dslContext.saveParkingContractPossibilityPojo(
                stubParkingContractPossibilityPojo(defaultOrderCode.value)
            )
            parkingContractPossibilityRepository =
                object : ParkingContractPossibilityRepository(dslContext) {
                    override fun findByIdForUpdate(
                        config: Configuration,
                        orderCode: Building.OrderCode,
                        waitSeconds: Int
                    ): ParkingContractPossibility? {
                        return super.findByIdForUpdate(config, orderCode, 1)
                    }
                }
            service =
                parkingContractService(parkingContractPossibilityRepository = parkingContractPossibilityRepository)
        }

        @Test
        @DisplayName("更新時にレコードロックでタイムアウトが発生した場合、DBValidationExceptionがthrowされること")
        fun case1() {
            // task1の実行後1秒後に、task2を実行する
            // task1は成功するが、task2はレコードロックで失敗する
            runAsyncTasks(
                task1 = {
                    Assertions.assertDoesNotThrow {
                        dslContext.transaction { config ->
                            service.updateParkingContractPossibility(
                                config,
                                BatchInterface.BATCH_USER,
                                defaultOrderCode,
                                emptyList(),
                                0,
                                0,
                            )
                            Thread.sleep(3000)
                        }
                    }
                },
                task2 = {
                    val err = assertThrows<DBValidationException> {
                        dslContext.transaction { config ->
                            service.updateParkingContractPossibility(
                                config,
                                BatchInterface.BATCH_USER,
                                defaultOrderCode,
                                emptyList(),
                                0,
                                0,
                            )
                        }
                    }
                    assertEquals(ErrorType.DB_UPDATE_FAILED, err.type)
                    assertEquals(
                        ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format().errorMessage,
                        err.detail.errorMessage
                    )
                },
                delayBetweenTasks = 1000,
            )
        }

        @Test
        @DisplayName("ほぼ同時にキー重複するデータを登録しようとした場合、後のほうでDBValidationExceptionがthrowされること")
        fun case2() {
            // task1の実行後100ミリ秒後に、task2を実行する
            // task1は成功するが、task2はキー重複で失敗する
            val orderCode = Building.OrderCode.of("1000002")
            val before = dslContext.selectBy(orderCode)
            assertNull(before)
            runAsyncTasks(
                task1 = {
                    Assertions.assertDoesNotThrow {
                        dslContext.transaction { config ->
                            service.updateParkingContractPossibility(
                                config,
                                BatchInterface.BATCH_USER,
                                orderCode,
                                emptyList(),
                                0,
                                0,
                            )
                            Thread.sleep(1000)
                        }
                    }
                },
                task2 = {
                    val err = assertThrows<DBValidationException> {
                        dslContext.transaction { config ->
                            service.updateParkingContractPossibility(
                                config,
                                BatchInterface.BATCH_USER,
                                orderCode,
                                emptyList(),
                                0,
                                0,
                            )
                        }
                    }
                    assertEquals(ErrorType.DB_UPDATE_FAILED, err.type)
                    assertEquals(
                        ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format().errorMessage,
                        err.detail.errorMessage
                    )
                },
                delayBetweenTasks = 100,
            )
            val result = dslContext.selectBy(orderCode)
            assertNotNull(result)
        }
    }

    @Nested
    @DisplayName("駐車場申込判定更新の検証")
    inner class Scenario3 {

        // setup
        private val orderCode = stubParkingContractPossibility().orderCode
        private var dbInsertCount = 0
        private var dbUpdateCount = 0
        private var dkPortalApiCancelCallCount = 0

        @AfterEach
        fun tearDown() {
            dbInsertCount = 0
            dbUpdateCount = 0
            dkPortalApiCancelCallCount = 0
        }

        @Nested
        @DisplayName("駐車場申込判定のレコードが存在しない場合")
        inner class Scenario3x1 {

            @Test
            @DisplayName("DB更新とDKポータルへの連携がされること")
            fun case1() {

                assertDoesNotThrow {
                    parkingContractService(
                        parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                            registerFunc = { _, _ -> dbInsertCount++ },
                            updateFunc = { _, _ -> dbUpdateCount++ },
                        ),
                        dkPortalRepository = MockDKPortalRepository(
                            updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                        ),
                    ).updateParkingContractPossibilityAndInformDKPortal(
                        stubJwtAuthInfo().getRequestUser(),
                        orderCode
                    ).let {
                        // updateParkingContractPossibilityAndInformDKPortal 関数内での後続の非同期処理完了を待つ
                        Thread.sleep(1000)
                    }
                }

                assertEquals(dbInsertCount, 1)
                assertEquals(dbUpdateCount, 0)
                assertEquals(dkPortalApiCancelCallCount, 1)
            }

            @Test
            @DisplayName("DKポータルへの連携が失敗してもDB更新は行われること")
            fun case2() {

                assertDoesNotThrow {
                    parkingContractService(
                        parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                            registerFunc = { _, _ -> dbInsertCount++ },
                            updateFunc = { _, _ -> dbUpdateCount++ },
                        ),
                        dkPortalRepository = MockDKPortalRepository(
                            updateParkingLotFunc = { _ -> throw RuntimeException() }
                        ),
                    ).updateParkingContractPossibilityAndInformDKPortal(
                        stubJwtAuthInfo().getRequestUser(),
                        orderCode
                    ).let {
                        // updateParkingContractPossibilityAndInformDKPortal 関数内での後続の非同期処理完了を待つ
                        Thread.sleep(1000)
                    }
                }

                assertEquals(dbInsertCount, 1)
                assertEquals(dbUpdateCount, 0)
            }
        }

        @Nested
        @DisplayName("駐車場申込判定のレコードは存在する場合")
        inner class Scenario3x2 {

            @Test
            @DisplayName("駐車場申込判定結果が更新前後で変わらない場合、DB更新もDKポータルへの連携もされないこと")
            fun case1() {

                assertDoesNotThrow {
                    parkingContractService(
                        parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                            findByIdFunc = { _ ->
                                ParkingContractPossibility(
                                    orderCode,
                                    ContractPossibility.IMPOSSIBLE,
                                    ContractPossibility.IMPOSSIBLE,
                                    ContractPossibilityAutoJudge.MANUAL
                                )
                            },
                            registerFunc = { _, _ -> dbInsertCount++ },
                            updateFunc = { _, _ -> dbUpdateCount++ },
                        ),
                        dkPortalRepository = MockDKPortalRepository(
                            updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                        ),
                    ).updateParkingContractPossibilityAndInformDKPortal(
                        stubJwtAuthInfo().getRequestUser(),
                        orderCode
                    ).let {
                        // updateParkingContractPossibilityAndInformDKPortal 関数内での後続の非同期処理完了を待つ
                        Thread.sleep(1000)
                    }
                }

                assertEquals(dbInsertCount, 0)
                assertEquals(dbUpdateCount, 0)
                assertEquals(dkPortalApiCancelCallCount, 0)
            }

            @Test
            @DisplayName("駐車場申込判定結果が更新前後で異なるが、DKポータルの表示が変わらない場合、DB更新は行われてDKポータルへの連携はされないこと")
            fun case2() {
                assertDoesNotThrow {
                    parkingContractService(
                        parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                            findByIdFunc = { _ ->
                                ParkingContractPossibility(
                                    orderCode,
                                    ContractPossibility.POSSIBLE,
                                    ContractPossibility.IMPOSSIBLE,
                                    ContractPossibilityAutoJudge.MANUAL
                                )
                            },
                            registerFunc = { _, _ -> dbInsertCount++ },
                            updateFunc = { _, _ -> dbUpdateCount++ },
                        ),
                        dkPortalRepository = MockDKPortalRepository(
                            updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                        ),
                    ).updateParkingContractPossibilityAndInformDKPortal(
                        stubJwtAuthInfo().getRequestUser(),
                        orderCode
                    ).let {
                        // updateParkingContractPossibilityAndInformDKPortal 関数内での後続の非同期処理完了を待つ
                        Thread.sleep(1000)
                    }
                }

                assertEquals(dbInsertCount, 0)
                assertEquals(dbUpdateCount, 1)
                assertEquals(dkPortalApiCancelCallCount, 0)
            }

            @Test
            @DisplayName("駐車場申込判定結果が更新前後で異なり、DKポータルの表示も変更の場合は、DB更新の更新とDKポータルへの連携がされること")
            fun case3() {
                assertDoesNotThrow {
                    parkingContractService(
                        parkingContractPossibilityRepository = MockParkingContractPossibilityRepository(
                            findByIdFunc = { _ ->
                                ParkingContractPossibility(
                                    orderCode,
                                    ContractPossibility.POSSIBLE,
                                    ContractPossibility.POSSIBLE,
                                    ContractPossibilityAutoJudge.AUTO
                                )
                            },
                            registerFunc = { _, _ -> dbInsertCount++ },
                            updateFunc = { _, _ -> dbUpdateCount++ },
                        ),
                        dkPortalRepository = MockDKPortalRepository(
                            isNeedUpdateParkingLotFunc = { _, _ -> true },
                            updateParkingLotFunc = { _ -> dkPortalApiCancelCallCount++ }
                        ),
                    ).updateParkingContractPossibilityAndInformDKPortal(
                        stubJwtAuthInfo().getRequestUser(),
                        orderCode
                    ).let {
                        // updateParkingContractPossibilityAndInformDKPortal 関数内での後続の非同期処理完了を待つ
                        Thread.sleep(1000)
                    }
                }

                assertEquals(dbInsertCount, 0)
                assertEquals(dbUpdateCount, 1)
                assertEquals(dkPortalApiCancelCallCount, 1)
            }
        }
    }
}

class GetParkingContractPossibilityTest : FunSpec({
    context("区画数による契約可否の判定検証 - 区画数による契約可否の判定の「区画数＞部屋数」パターン確認") {
        listOf(
            TestCase(
                case = "空き区画数＝0 特約有",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 10,
                vacancyParkingLotCount = 0,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = true,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.REQUIRED_CONFIRM,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＝0 特約無",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 10,
                vacancyParkingLotCount = 0,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＞空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 10,
                vacancyParkingLotCount = 4,
                roomCount = 5,
                vacancyRoomCount = 3,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.POSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＝空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 10,
                vacancyParkingLotCount = 4,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＜空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 10,
                vacancyParkingLotCount = 3,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
        ).forEach {
            test(it.case) {
                it.doTest()
            }
        }
    }

    context("区画数による契約可否の判定検証 - 区画数による契約可否の判定の「区画数＝部屋数」パターン確認") {
        listOf(
            TestCase(
                case = "空き区画数＝0 特約有",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 0,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = true,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.REQUIRED_CONFIRM,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＝0 特約無",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 0,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＞空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 4,
                roomCount = 5,
                vacancyRoomCount = 3,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.REQUIRED_CONFIRM,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＝空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 4,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＜空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 3,
                roomCount = 5,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
        ).forEach {
            test(it.case) {
                it.doTest()
            }
        }
    }

    context("区画数による契約可否の判定検証 - 区画数による契約可否の判定の「区画数＜部屋数」パターン確認") {
        listOf(
            TestCase(
                case = "空き区画数＝0 特約有",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 0,
                roomCount = 10,
                vacancyRoomCount = 4,
                specialContractFlag = true,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＝0 特約無",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 0,
                roomCount = 10,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＞空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 4,
                roomCount = 10,
                vacancyRoomCount = 3,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.REQUIRED_CONFIRM,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＝空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 4,
                roomCount = 10,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
            TestCase(
                case = "空き区画数＜空き部屋数",
                orderCode = DEFAULT_ORDER_CODE,
                parkingLotCount = 5,
                vacancyParkingLotCount = 3,
                roomCount = 10,
                vacancyRoomCount = 4,
                specialContractFlag = false,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
                expected = stubParkingContractPossibility(
                    DEFAULT_ORDER_CODE.value,
                    ContractPossibility.POSSIBLE,
                    ContractPossibility.IMPOSSIBLE,
                    ContractPossibilityAutoJudge.AUTO,
                )
            ),
        ).forEach {
            test(it.case) {
                it.doTest()
            }
        }
    }

}) {
    companion object {
        private val DEFAULT_ORDER_CODE = Building.OrderCode.of("1000001")
    }

    private class TestCase(
        val case: String,
        val orderCode: Building.OrderCode,
        val parkingLotCount: Int,
        val vacancyParkingLotCount: Int,
        val roomCount: Int,
        val vacancyRoomCount: Int,
        val specialContractFlag: Boolean,
        val isAutoJudge: ContractPossibilityAutoJudge,
        val expected: ParkingContractPossibility,
    ) {
        fun doTest() {
            ParkingContractService.getParkingContractPossibility(
                orderCode = this.orderCode,
                parkingLotCount = this.parkingLotCount,
                vacancyParkingLotCount = this.vacancyParkingLotCount,
                roomCount = this.roomCount,
                vacancyRoomCount = this.vacancyRoomCount,
                specialContractFlag = this.specialContractFlag,
                isAutoJudge = this.isAutoJudge,
            ).shouldBe(this.expected)
        }
    }
}
