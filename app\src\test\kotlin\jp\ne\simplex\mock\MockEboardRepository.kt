package jp.ne.simplex.mock

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.external.eboard.EboardRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo

class MockEboardRepository(
    val loginFunc: () -> Unit = {},

    val registerTemporaryReservationFunc: (parameter: RegisterTemporaryReservation) -> Unit = { _ -> },

    val cancelTemporaryReservationFunc: (parameter: CancelTemporaryReservation) -> Unit = { _ -> },

    val updateTemporaryReservationCommentFunc: (parameter: UpdateTemporaryReservationComment) -> Unit = { _ -> },

    val instructPublicFunc: (requestUser: AuthInfo.RequestUser, updatePublishStatusList: List<UpdatePropertyMaintenance.PublishStatus>) ->
    UpdatePropertyMaintenance.Result<Map<Property.Id, Property.UpState?>, List<Property.Id>> = { _, _ ->
        UpdatePropertyMaintenance.Result(emptyMap(), emptyList())
    },

    val reserveParkingFunc: (parkingReservationAction: ParkingReservationAction, parkingReservationInfo: ParkingReservationInfo) -> Unit = { _, _ -> },

    val registerParkingImageFunc: (
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile,
    ) -> Unit = { _, _, _ -> },
    val deleteParkingImageFunc: (
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
    ) -> Unit = { _, _ -> },
    val registerGarbageImageFunc: (
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile,
    ) -> Unit = { _, _, _ -> },
    val deleteGarbageImageFunc: (
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
    ) -> Unit = { _, _ -> },
) : EboardRepositoryInterface {
    override fun login() {
        return loginFunc()
    }

    override fun registerTemporaryReservation(rtr: RegisterTemporaryReservation) {
        return registerTemporaryReservationFunc(rtr)
    }

    override fun cancelTemporaryReservation(
        ctr: CancelTemporaryReservation,
        canceled: TemporaryReservationInfo
    ) {
        return cancelTemporaryReservationFunc(ctr)
    }

    override fun updateTemporaryReservationComment(req: UpdateTemporaryReservationComment) {
        return updateTemporaryReservationCommentFunc(req)
    }

    override fun instructPublic(
        requestUser: AuthInfo.RequestUser,
        updatePublishStatusList: List<UpdatePropertyMaintenance.PublishStatus>
    ): UpdatePropertyMaintenance.Result<Map<Property.Id, Property.UpState?>, List<Property.Id>> {
        return instructPublicFunc(requestUser, updatePublishStatusList)
    }

    override fun reserveParking(
        parkingReservationAction: ParkingReservationAction,
        parkingReservationInfo: ParkingReservationInfo
    ) {
        reserveParkingFunc(parkingReservationAction, parkingReservationInfo)
    }

    override fun registerParkingImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile,
    ) {
        return registerParkingImageFunc(requestUser, buildingCode, imageFile)
    }

    override fun deleteParkingImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
    ) {
        return deleteParkingImageFunc(requestUser, buildingCode)
    }

    override fun registerGarbageImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile
    ) {
        return registerGarbageImageFunc(requestUser, buildingCode, imageFile)
    }

    override fun deleteGarbageImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code
    ) {
        return deleteGarbageImageFunc(requestUser, buildingCode)
    }
}
