package jp.ne.simplex.application.model

import jp.ne.simplex.shared.enums.PerPage
import jp.ne.simplex.shared.enums.SortOrder
import java.time.LocalDate

// 先行公開の業務関連を表す際に使用するインターフェース
interface ExclusiveProperty {
    data class Id private constructor(val value: Long) {
        companion object {
            fun of(value: Long): Id {
                return Id(value)
            }

            fun create(sequence: Long): Id {
                // 現在時刻の unixtimeミリ秒 を取得
                val now = System.currentTimeMillis().toString()

                // 3桁の乱数を取得
                val random = String.format("%03d", (1..999).random())

                // 引数のシーケンス番号（0埋め2桁）を末尾に付与して、IDを生成
                return of((now + random + String.format("%02d", sequence)).toLong())
            }
        }
    }

    enum class CompanyType(val value: Int) {
        RealEstate(0), // 不動産会社

        Leasing(1), // 大東リーシング

        HouseCom(2); // ハウスコム

        companion object {
            fun fromValue(value: Int): CompanyType? {
                return CompanyType.entries.find { it.value == value }
            }
        }
    }

    // 先行公開先
    // Type = Type.RealEstate の場合は、eCode が必須
    // Type = Type.Leasing, Type.HouseCom の場合は、eCode は null
    data class ExclusiveTarget(
        val companyType: CompanyType,
        val eCode: Agent.ECode?,
    )

    data class ExclusiveTargetWithId(
        val id: Id,
        val target: ExclusiveTarget,
    ) {
        companion object {
            fun of(companyTypes: List<String>, eCodes: List<String>?): List<ExclusiveTargetWithId> {
                var index: Long = 0

                return companyTypes.flatMap { type ->
                    index++

                    when (val companyType =
                        CompanyType.fromValue(type.toInt())!!) {
                        CompanyType.Leasing,
                        CompanyType.HouseCom -> {
                            return@flatMap listOf(
                                ExclusiveTargetWithId(
                                    Id.create(index),
                                    ExclusiveTarget(companyType, null)
                                )
                            )
                        }

                        CompanyType.RealEstate -> {
                            return@flatMap eCodes?.map {
                                index++

                                ExclusiveTargetWithId(
                                    Id.create(index),
                                    ExclusiveTarget(
                                        companyType,
                                        Agent.ECode.of(it)
                                    )
                                )
                            } ?: emptyList()
                        }
                    }
                }
            }
        }
    }
}

// 先行公開のオブジェクトモデル
class ExclusivePropertyInfo(
    // 先行公開ID
    val id: ExclusiveProperty.Id,

    // 物件ID
    val propertyId: Property.Id,

    // 物件名称
    val buildingName: String?,

    // 部屋番号
    val roomNumber: Room.Number?,

    // 先行期間
    val exclusiveRange: DateRange,

    // 先行先
    val exclusiveTarget: ExclusiveProperty.ExclusiveTarget,

    // 先行先名
    val exclusiveTargetName: String?,

    // 早期終了フラグ
    val earlyClosureFlag: Boolean,

    //削除フラグ
    val deleteFlag: Boolean = false,

    // 作成日
    val createDate: LocalDate,

    // 作成者
    val creator: Employee.Name?,

    // 作成したユーザーの所属営業所
    val creatorAffiliationOfficeCode: Office.Code?,

    // 更新日
    val updateDate: LocalDate,

    // 更新者
    val updater: Employee.Name?,
) : ExclusiveProperty {

    fun isBeforePublished(today: LocalDate = LocalDate.now()): Boolean {
        return today.isBefore(exclusiveRange.from)
    }

    fun isAlreadyPublished(): Boolean {
        return exclusiveRange.isStarted(allowSameDate = true)
    }

    fun isAlreadyFinished(): Boolean {
        return exclusiveRange.isEnded(allowSameDate = false) || earlyClosureFlag
    }
}

class ExclusivePropertiesSearch(
    // ページ数
    val page: Int,

    // 各ページに表示する件数
    val perPage: PerPage,

    // ソート項目
    val sortBy: List<SortBy>,

    // ソート順
    val sortOrder: SortOrder,

    // IDリスト
    val idList: List<ExclusiveProperty.Id>? = null,

    // 建物CD
    val buildingCode: String? = null,

    // 部屋CD
    val roomCode: String? = null,

    // 先行期間From
    val exclusiveFrom: LocalDate? = null,

    // 先行期間To
    val exclusiveTo: LocalDate? = null,

    // 先行先
    val exclusiveTarget: String? = null,

    // 掲載状況
    val listingSituationTypeList: List<PublishStatus>,

    // 先行先種別
    val companyTypeList: List<ExclusiveProperty.CompanyType>? = null,

    // 物件名
    val propertyName: String? = null,

    // 作成日
    val createDate: LocalDate? = null,

    // 作成者
    val creator: String? = null,

    // 営業所
    val officeCode: String? = null,

    ) : ExclusiveProperty {
    enum class SortBy {
        DEFAULT_SORT,  // DEFAULT SORT
        BUILDING_CODE, // 建物CD
        ROOM_CODE, // 部屋CD
        PROPERTY_NAME, // 物件名
        EXCLUSIVE_DATE, // 先行期間
        EXCLUSIVE_END_DATE, // 先行期間終了日
        EXCLUSIVE_TARGET, // 先行先
        COMPANY_TYPE, // 先行先種別
        CREATION_DATE, // 作成日
        CREATOR, // 作成者
        UPDATE_DATE, // 更新日
        UPDATER; // 更新者

        companion object {
            fun fromValue(value: String?): SortBy? {
                return SortBy.entries.find { it.toString() == value }
            }
        }
    }

}

enum class PublishStatus(val value: Byte) {
    PreRelease(0), // 先行公開前

    InProgress(1), // 先行公開中

    Completed(2); // 先行公開終了

    companion object {
        fun fromValue(value: Byte): PublishStatus? {
            return PublishStatus.entries.find { it.value == value }
        }
    }
}

enum class ComeFromType(val value: Byte) {
    Cancel(1), // キャンセル

    Update(2), // 更新

    Default(0); // デフォルト

    companion object {
        fun fromValue(value: Byte): ComeFromType? {
            return ComeFromType.entries.find { it.value == value }
        }
    }
}

class ExclusivePropertiesSearchForBatch(
    // 掲載状況
    val listingSituationTypeList: List<PublishStatus>,
    // 先行先種別
    val companyTypeList: List<ExclusiveProperty.CompanyType>? = null
) : ExclusiveProperty

// 先行公開を操作する（新規・更新・削除）際に使用するインターフェース
sealed interface ExclusivePropertyAction : ExclusiveProperty {

    // DKリンク上では、先行公開の登録/更新は、一つの物件に対して複数の先行先を指定することができる
    // DKポータルに連携する際は、1リクエストで1物件1先行先のため、DKリンク上での扱い方と異なる
    // 業務ドメイン上は、RegisterExclusiveProperty/UpdateExclusiveProperty が業務とマッチしているが、
    // システム内で扱いは、以下の Record に変換した方が扱いやすいため、このオブジェクトを定義している
    class Record(
        val propertyId: Property.Id,
        val exclusiveRange: DateRange,
        val exclusiveTargetWithId: ExclusiveProperty.ExclusiveTargetWithId,
    )

    class Result<T>(
        val status: Status,
        val reason: StatusReason? = null,
        val failedList: List<T> = emptyList(),
    ) {
        companion object {
            fun <T> success(): Result<T> = Result(Status.SUCCESS)
        }

        fun isFailed(): Boolean = this.status == Status.FAILED
    }

    enum class Status { SUCCESS, FAILED; }

    enum class StatusReason {
        INVALID_ECODE,
        INVALID_EXCLUSIVE_RANGE,
        INVALID_ID_NOT_FOUND,
        INVALID_ID_COMPLETED,
        DB_UPDATE_FAILED,
        DB_DELETE_FAILED,
        EXCLUSIVE_TARGET_DUPLICATE,
    }
}

class RegisterExclusiveProperty(
    // 物件ID
    val propertyId: Property.Id,

    // 先行期間
    val exclusiveRange: DateRange,

    // 先行公開登録詳細情報
    val exclusiveTargetWithIds: List<ExclusiveProperty.ExclusiveTargetWithId>,
) : ExclusivePropertyAction {

    fun getRecords(): List<ExclusivePropertyAction.Record> {
        return exclusiveTargetWithIds.map {
            ExclusivePropertyAction.Record(propertyId, exclusiveRange, it)
        }
    }
}

class UpdateExclusiveProperty(
    // 先行公開ID
    val id: ExclusiveProperty.Id,

    // 先行期間
    val exclusiveRange: DateRange,

    // 先行公開登録詳細情報
    val exclusiveTargetWithIds: List<ExclusiveProperty.ExclusiveTargetWithId>,
) : ExclusivePropertyAction {

    fun getRecords(property: Property): List<ExclusivePropertyAction.Record> {
        return exclusiveTargetWithIds.map {
            ExclusivePropertyAction.Record(property.id, exclusiveRange, it)
        }
    }

    fun getExclusiveTargetList(): List<ExclusiveProperty.ExclusiveTarget> {
        return exclusiveTargetWithIds.map { it.target }
    }
}
