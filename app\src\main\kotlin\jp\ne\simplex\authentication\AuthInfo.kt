package jp.ne.simplex.authentication

import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.ExternalSystem
import jp.ne.simplex.application.model.Office

sealed class AuthInfo {

    fun getRequestUser(): RequestUser {
        return when (this) {
            is Jwt -> RequestUser(this.employeeCode.value)
            is ApiKey -> RequestUser(this.externalSystem.value)
            is Batch -> RequestUser(this.userCode)
        }
    }

    data class Jwt(
        val employeeCode: Employee.Code,
        val businessOfficeCode: Office.Code?,
        val companyCode: String?
    ) : AuthInfo()

    data class ApiKey(
        val externalSystem: ExternalSystem
    ) : AuthInfo()

    data class Batch(
        val userCode: String = "system"
    ) : AuthInfo()

    inner class RequestUser(val value: String)

    @JvmName("jwtEmployeeCode")
    fun getEmployeeCode(): Employee.Code? {
        return when (this) {
            is Jwt -> this.employeeCode
            else -> null
        }
    }
}

