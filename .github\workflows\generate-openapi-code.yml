name: Generate OpenAPI Code

on:
  push:
    branches:
      - main

  workflow_dispatch:
    inputs:
      generate_openapi:
        description: 'OpenAPIドキュメントを生成し、propetechリポジトリにプッシュする'
        type: boolean
        default: false

jobs:
  GenerateOpenAPI:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && inputs.generate_openapi)
    permissions:
      id-token: write
      contents: write
    steps:
      # GitHubAppのトークンを作成
      - name: Generate GitHub Apps token
        id: generate
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.REPOSITORY_READ_TOKEN_APP_ID }}
          private-key: ${{ secrets.REPOSITORY_READ_TOKEN_PRIVATE_KEY }}
          repositories: |
            propetech-handover
            propetech-server-handover
      # 自リポジトリからソースをダウンロード
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate.outputs.token }}
          repository: ${{ github.repository }}
      # Setup（Java/Gradleの設定及び、Jooq自動生成）
      - name: Setup
        uses: ./.github/workflows/composite/setup
      # OpenAPI仕様書の生成
      - name: Generate OpenAPI Docs
        run: ./gradlew generateOpenApiDocs
        shell: bash
      # 生成されたYAMLファイルをキャッシュに保存
      - name: Cache OpenAPI YAML
        uses: actions/cache@v3
        with:
          path: ./build/api-docs.yaml
          key: openapi-docs-${{ github.sha }}
      # GitHub APIを使用してpropetech-handoverリポジトリのワークフローをトリガー
      - name: Trigger generateCode workflow in propetech repository
        run: |
          curl -X POST \
            -H "Authorization: token ${{ steps.generate.outputs.token }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/dklink-project/propetech-handover/dispatches \
            -d '{"event_type":"generate-code-event","client_payload":{"ref":"${{ github.ref }}","sha":"${{ github.sha }}"}}'
        shell: bash
