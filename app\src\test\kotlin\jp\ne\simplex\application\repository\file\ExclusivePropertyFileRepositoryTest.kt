package jp.ne.simplex.application.repository.file

import jp.ne.simplex.application.model.*
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.io.BufferedWriter
import java.io.StringWriter
import java.time.LocalDate
import kotlin.test.assertEquals

class ExclusivePropertyFileRepositoryTest {
    private val repository = ExclusivePropertyFileRepository()

    @Nested
    @DisplayName("先行公開CSV出力の検証")
    inner class Scenario1 {

        @Test
        @DisplayName("CSV出力内容の確認")
        fun case1() {
            val list = listOf(
                ExclusivePropertyInfo(
                    id = ExclusiveProperty.Id.of(174539560948059901),
                    propertyId = Property.Id(
                        buildingCode = Building.Code.of("*********"),
                        roomCode = Room.Code.of("00010")
                    ),
                    buildingName = "テスト物件",
                    roomNumber = Room.Number.of("001"),
                    exclusiveRange = DateRange.of(
                        from = "20250601".yyyyMMdd(),
                        to = "20250610".yyyyMMdd()
                    ),
                    exclusiveTarget = ExclusiveProperty.ExclusiveTarget(
                        companyType = ExclusiveProperty.CompanyType.Leasing,
                        eCode = null,
                    ),
                    exclusiveTargetName = null,
                    earlyClosureFlag = false,
                    createDate = LocalDate.now(),
                    creator = null,
                    creatorAffiliationOfficeCode = null,
                    updateDate = LocalDate.now(),
                    updater = null,
                ),
                ExclusivePropertyInfo(
                    id = ExclusiveProperty.Id.of(174539560948059902),
                    propertyId = Property.Id(
                        buildingCode = Building.Code.of("*********"),
                        roomCode = Room.Code.of("00020")
                    ),
                    buildingName = "テスト物件",
                    roomNumber = Room.Number.of("002"),
                    exclusiveRange = DateRange.of(
                        from = "20250601".yyyyMMdd(),
                        to = "20250610".yyyyMMdd()
                    ),
                    exclusiveTarget = ExclusiveProperty.ExclusiveTarget(
                        companyType = ExclusiveProperty.CompanyType.Leasing,
                        eCode = null,
                    ),
                    exclusiveTargetName = null,
                    earlyClosureFlag = false,
                    createDate = LocalDate.now(),
                    creator = null,
                    creatorAffiliationOfficeCode = null,
                    updateDate = LocalDate.now(),
                    updater = null,
                ),
                ExclusivePropertyInfo(
                    id = ExclusiveProperty.Id.of(174539560948059903),
                    propertyId = Property.Id(
                        buildingCode = Building.Code.of("*********"),
                        roomCode = Room.Code.of("00030")
                    ),
                    buildingName = "テスト物件",
                    roomNumber = Room.Number.of("003"),
                    exclusiveRange = DateRange.of(
                        from = "20250601".yyyyMMdd(),
                        to = "20250610".yyyyMMdd()
                    ),
                    exclusiveTarget = ExclusiveProperty.ExclusiveTarget(
                        companyType = ExclusiveProperty.CompanyType.Leasing,
                        eCode = null,
                    ),
                    exclusiveTargetName = null,
                    earlyClosureFlag = false,
                    createDate = LocalDate.now(),
                    creator = null,
                    creatorAffiliationOfficeCode = null,
                    updateDate = LocalDate.now(),
                    updater = null,
                ),
            )

            val writer = StringWriter()
            BufferedWriter(writer).use { w ->
                repository.writeExclusivePropertyForECloud(w, list)
            }

            val expected =
                "\"174539560948059901\",\"1-*********-00010\",\"テスト物件 001\",\"20250601\",\"20250610\",\"1\",\"0\",\"\"\n" +
                        "\"174539560948059902\",\"1-*********-00020\",\"テスト物件 002\",\"20250601\",\"20250610\",\"1\",\"0\",\"\"\n" +
                        "\"174539560948059903\",\"1-*********-00030\",\"テスト物件 003\",\"20250601\",\"20250610\",\"1\",\"0\",\"\"\n"
            assertEquals(expected, writer.toString())
        }
    }
}