package jp.ne.simplex.openapi

import kotlin.reflect.KClass

@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class ApiDefinition(
    val summary: String,
    val description: String = "",
    val responseHeaderName: String = "",
    val responseHeaderDescription: String = "",
    val responseHeaderClass: KClass<*> = Nothing::class,
    val responseHeaderExample: String = "",
)
