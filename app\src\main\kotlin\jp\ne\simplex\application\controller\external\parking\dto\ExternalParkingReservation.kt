package jp.ne.simplex.application.controller.external.parking.dto

import jp.ne.simplex.application.model.ParkingReservation

enum class ExternalReservationType(val value: Int) {
    APPLICATION(0), // 申込
    WORK(1), // 作業
    REPLACE(2), // 場所変更
    ONE_DAY(3); // 1日利用

    companion object {

        fun fromValue(value: Int): ExternalReservationType? {
            return ExternalReservationType.entries.find { it.value == value }
        }

        // ExternalReservationType から ReservationType へのマッピング
        fun ExternalReservationType.toReservationType(): ParkingReservation.Type {
            return when (this) {
                APPLICATION -> ParkingReservation.Type.AUTO_APPLICATION
                WORK -> ParkingReservation.Type.WORK
                REPLACE -> ParkingReservation.Type.REPLACE
                ONE_DAY -> ParkingReservation.Type.ONE_DAY
            }
        }

        // ReservationType から ExternalReservationType へのマッピング
        fun fromReservationType(reservationType: ParkingReservation.Type): ExternalReservationType {
            return when (reservationType) {
                ParkingReservation.Type.AUTO_APPLICATION,
                ParkingReservation.Type.MANUAL_APPLICATION -> APPLICATION

                ParkingReservation.Type.WORK -> WORK
                ParkingReservation.Type.REPLACE -> REPLACE
                ParkingReservation.Type.ONE_DAY -> ONE_DAY
            }
        }
    }
}
