name: Deploy Docker Images

on:
  # GitHub上から手動実行するとき
  workflow_dispatch:
    inputs:
      environment:
        description: 'デプロイ先環境'
        required: true
        default: 'it'
        type: environment
      deploy_app:
        description: 'Appイメージをビルド・デプロイする'
        required: true
        type: boolean
        default: false
      app_tag_version:
        description: 'Appイメージのタグのバージョン部分'
        required: false
        type: string
      deploy_batch:
        description: 'Batchイメージをビルド・デプロイする'
        required: true
        type: boolean
        default: false
      batch_tag_version:
        description: 'Batchイメージのタグのバージョン部分'
        required: false
        type: string

jobs:
  DeployImages:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 30
    permissions:
      id-token: write
      contents: write
    steps:
      # 入力チェック
      - name: Check inputs
        run: |
          if [[ "${{ inputs.deploy_app }}" == "false" && "${{ inputs.deploy_batch }}" == "false" ]]; then
            echo "少なくとも一つのイメージタイプを選択してください"
            exit 1
          fi

          if [[ "${{ inputs.deploy_app }}" == "true" && -z "${{ inputs.app_tag_version }}" ]]; then
            echo "Appイメージを選択した場合はバージョンを指定してください"
            exit 1
          fi

          if [[ "${{ inputs.deploy_batch }}" == "true" && -z "${{ inputs.batch_tag_version }}" ]]; then
            echo "Batchイメージを選択した場合はバージョンを指定してください"
            exit 1
          fi
        shell: bash

      # イメージのタグの生成
      - name: Create Image Tags
        run: |
          if [[ "${{ inputs.deploy_app }}" == "true" ]]; then
            echo "APP_IMAGE_TAG=app-${{ inputs.app_tag_version }}" >> $GITHUB_ENV
          fi

          if [[ "${{ inputs.deploy_batch }}" == "true" ]]; then
            echo "BATCH_IMAGE_TAG=batch-${{ inputs.batch_tag_version }}" >> $GITHUB_ENV
          fi
        shell: bash

      # AWS認証
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: arn:aws:iam::${{ vars.ACCOUNT_ID }}:role/GitHubActions-${{ vars.DEPLOY_ENV }}-ECRDeployRole

      # ECRログイン
      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin ${{ vars.APP_ECR_URI }}
        shell: bash

      # イメージ存在確認
      - name: Check if Images Exist
        run: |
          repo_name=$(echo ${{ vars.APP_ECR_URI }} | awk -F'/' '{print $2}')

          if [[ "${{ inputs.deploy_app }}" == "true" ]]; then
            if aws ecr describe-images --repository-name $repo_name --image-ids imageTag=${{ env.APP_IMAGE_TAG }} >/dev/null 2>&1; then
              echo "Appイメージのタグ '${{ env.APP_IMAGE_TAG }}' は既に存在します"
              exit 1
            fi
          fi

          if [[ "${{ inputs.deploy_batch }}" == "true" ]]; then
            if aws ecr describe-images --repository-name $repo_name --image-ids imageTag=${{ env.BATCH_IMAGE_TAG }} >/dev/null 2>&1; then
              echo "Batchイメージのタグ '${{ env.BATCH_IMAGE_TAG }}' は既に存在します"
              exit 1
            fi
          fi
        shell: bash

      # GitHubAppのトークンを作成
      - name: Generate GitHub Apps token
        id: generate
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.REPOSITORY_READ_TOKEN_APP_ID }}
          private-key: ${{ secrets.REPOSITORY_READ_TOKEN_PRIVATE_KEY }}
          repositories: |
            propetech-handover
            propetech-server-handover

      # 自リポジトリからソースをダウンロード
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate.outputs.token }}
          repository: ${{ github.repository }}

      # propetech リポジトリからソースをダウンロード
      - name: Clone dklink-project/propetech-handover repository
        env:
          GITHUB_TOKEN: ${{ steps.generate.outputs.token }}
        run: |
          cd ../
          base_branch=${{ github.ref_name }}
          echo "base_branch: $base_branch"
          target_branch="main"
          echo "target_branch: $target_branch"
          git clone --branch $target_branch https://x-access-token:${GITHUB_TOKEN}@github.com/dklink-project/propetech-handover.git
        shell: bash

      # Setup（Java/Gradleの設定及び、Jooq自動生成）
      - name: Setup
        uses: ./.github/workflows/composite/setup

      # App Jarファイル生成
      - name: Generate App .jar file
        if: inputs.deploy_app == true
        run: ./gradlew build -x test -x :db:build -x :db-strategy:build --stacktrace

      # Batch Jarファイル生成
      - name: Generate Batch .jar file
        if: inputs.deploy_batch == true
        run: ./gradlew buildBatch --stacktrace

      # QEMUのセットアップ
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/arm64

      - name: Copy buildkitd.toml
        run: |
          sudo mkdir -p /etc/buildkit
          sudo cp "$GITHUB_WORKSPACE/.github/etc/buildkit/buildkitd.toml" /etc/buildkit/buildkitd.toml
        shell: bash

      # Buildxのセットアップ
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-config: /etc/buildkit/buildkitd.toml
          driver-opts: |
            image=moby/buildkit:buildx-stable-1

      # App Docker Build & Push
      - name: App Docker Build and Push
        if: inputs.deploy_app == true
        run: |
          docker buildx build \
          --platform linux/arm64 \
          -t ${{ vars.APP_ECR_URI }}:${{ env.APP_IMAGE_TAG }} \
          -f app/Dockerfile app \
          --network=host \
          --load

          docker push ${{ vars.APP_ECR_URI }}:${{ env.APP_IMAGE_TAG }}
        shell: bash

      # Batch Docker Build & Push
      - name: Batch Docker Build and Push
        if: inputs.deploy_batch == true
        run: |
          docker buildx build \
          --platform linux/arm64 \
          -t ${{ vars.APP_ECR_URI }}:${{ env.BATCH_IMAGE_TAG }} \
          -f app/batch-Dockerfile app \
          --network=host \
          --load

          docker push ${{ vars.APP_ECR_URI }}:${{ env.BATCH_IMAGE_TAG }}
        shell: bash

      # AWS認証(Setup内でCache利用で別のRoleが付与されるため再度ECRDeployRoleを付与
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: arn:aws:iam::${{ vars.ACCOUNT_ID }}:role/GitHubActions-${{ vars.DEPLOY_ENV }}-ECRDeployRole

      # App用タグ管理用SSMパラメータストアの値を更新
      - name: Update App SSM Parameter
        if: inputs.deploy_app == true
        run: |
          aws ssm put-parameter --name ${{ vars.APP_IMAGE_TAG_SSM_PARAMETER_NAME }} --value ${{ env.APP_IMAGE_TAG }} --type "String" --overwrite
        shell: bash

      # Batch用タグ管理用SSMパラメータストアの値を更新
      - name: Update Batch SSM Parameter
        if: inputs.deploy_batch == true
        run: |
          aws ssm put-parameter --name ${{ vars.BATCH_IMAGE_TAG_SSM_PARAMETER_NAME }} --value ${{ env.BATCH_IMAGE_TAG }} --type "String" --overwrite
        shell: bash
