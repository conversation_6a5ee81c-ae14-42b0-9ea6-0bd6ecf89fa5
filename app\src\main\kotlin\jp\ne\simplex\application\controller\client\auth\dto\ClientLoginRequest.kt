package jp.ne.simplex.application.controller.client.auth.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.model.LoginInfo
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.log.MaskTarget
import jp.ne.simplex.log.MaskValue

@MaskTarget
data class ClientLoginRequest(
    @JsonProperty("employeeId")
    @field:Schema(example = "000011")
    val employeeId: String,

    @MaskValue
    @JsonProperty("password")
    @field:Schema(example = "000011")
    val password: String,
) {

    fun toServiceInterface(): LoginInfo {
        try {
            return LoginInfo(
                employeeCode = Employee.Code(employeeId),
                password = password
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
