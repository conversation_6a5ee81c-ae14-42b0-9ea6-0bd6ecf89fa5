package jp.ne.simplex.application.controller.client.shared

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Company

/** クライアントと Branch をやり取りするための共通DTO */
data class ClientBranchDto(
    @JsonProperty("branchCode")
    @field:Schema(description = "支店コード", example = "753")
    val branchCode: String,

    @JsonProperty("branchName")
    @field:Schema(description = "支店名", example = "〇〇支店")
    val branchName: String,

    @JsonProperty("companyCode")
    @field:Schema(description = "所属会社コード", example = "001")
    val companyCode: String,
) {

    companion object {

        fun of(branch: Branch): ClientBranchDto {
            return ClientBranchDto(
                branchCode = branch.code.getPrefix(),
                branchName = branch.name.value,
                companyCode = branch.company.code,
            )
        }
    }

    @JsonIgnore
    fun getBranch(): Branch {
        return Branch(
            Branch.Code.of(branchCode),
            Branch.Name.of(branchName),
            Company.of(companyCode)
        )
    }
}
