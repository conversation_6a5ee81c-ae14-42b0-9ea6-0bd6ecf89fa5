package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.db.jooq.gen.tables.references.VACANT_PARKING_LIST
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class VacantParkingListRepository(private val context: DSLContext) :
    VacantParkingListRepositoryInterface {

    override fun delete() {
        context.delete(VACANT_PARKING_LIST).execute()
    }

    override fun save(parkingLotId: ParkingLot.Id, parkingLotNumber: String?) {

        val currentDateTime = LocalDateTime.now()

        context.insertInto(VACANT_PARKING_LIST)
            .set(VACANT_PARKING_LIST.CREATION_DATE, currentDateTime.yyyyMMdd().toInt())
            .set(VACANT_PARKING_LIST.CREATION_TIME, currentDateTime.HHmmss().toInt())
            .set(VACANT_PARKING_LIST.CREATOR, "Batch")
            .set(VACANT_PARKING_LIST.UPDATE_DATE, 0)
            .set(VACANT_PARKING_LIST.UPDATE_TIME, 0)
            .set(VACANT_PARKING_LIST.UPDATER, "")
            .set(VACANT_PARKING_LIST.BUILDING_CD, parkingLotId.buildingCode.value)
            .set(VACANT_PARKING_LIST.PARKING_CD, parkingLotId.parkingLotCode.value)
            .set(VACANT_PARKING_LIST.PARKING_NO, parkingLotNumber)
            .execute()
    }
}

interface VacantParkingListRepositoryInterface {
    /** 空き駐車場一覧DBクリア(sql8) */
    fun delete()

    /** 空き駐車場一覧DB挿入(sql9) */
    fun save(parkingLotId: ParkingLot.Id, parkingLotNumber: String?)
}
