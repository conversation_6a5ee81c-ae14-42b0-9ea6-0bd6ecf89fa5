package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException

class TenantContract {
    data class Number private constructor(val value: String) {
        companion object {
            private const val LENGTH = 8
            fun of(value: String): Number {
                return if (value.length == LENGTH) Number(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("テナント契約番号", LENGTH)
                )
            }
        }
    }

    data class ChangeSeq(val value: String)
}
