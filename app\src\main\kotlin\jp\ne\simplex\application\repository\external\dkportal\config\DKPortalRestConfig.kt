package jp.ne.simplex.application.repository.external.dkportal.config

import com.fasterxml.jackson.annotation.JsonIgnore
import jp.ne.simplex.application.repository.external.ExternalApiGraphqlClient
import jp.ne.simplex.application.repository.external.ExternalApiLoggingInterceptor
import jp.ne.simplex.application.repository.external.ExternalApiRequest
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.log.LogType
import org.apache.hc.client5.http.classic.HttpClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestClient

@Configuration
class DKPortalRestConfig(
    @Value("\${external.dkportal.endpoint}")
    private val endpoint: String,
) {
    @Bean("dkportalGraphqlClient")
    fun graphqlClient(
        builder: RestClient.Builder = RestClient.builder(),
        httpClient: HttpClient? = null,
    ): ExternalApiGraphqlClient<DKPortalRequest, DKPortalResponse> {
        return ExternalApiGraphqlClient
            .builder<DKPortalRequest, DKPortalResponse>(builder)
            .endpoint(endpoint)
            .requestFactory(httpClient)
            .requestInterceptor(ExternalApiLoggingInterceptor(LogType.DK_PORTAL))
            .errorHandler(DKPortalDefaultErrorHandler())
            .build()
    }
}

/** DKPortalRestClientを使用してHTTP通信する際の Request Interface */
interface DKPortalRequest : ExternalApiRequest {

    override fun getErrorType(): ErrorType {
        return ErrorType.DK_PORTAL_API_ERROR
    }

    @JsonIgnore
    fun getDKPortalOperationName(): DKPortalOperationName
}

/** DKPortalRestClientを使用してHTTP通信する際の Response body Interface */
interface DKPortalResponse

/** DKPortalGraphqlClientを使用してHTTP通信する際のオペレーション名一覧  */
enum class DKPortalOperationName(val value: String) {
    /** 駐車場検索用情報連携 */
    UPDATE_PARKING_LOT("updateParkingLot"),

    /** AD・FF/敷金・礼金更新 */
    UPDATE_ADFF_AND_DEPOSIT_KEY_MONEY("updateAdFfAndDepositKeyMoney"),

    /** 部屋情報（物件掲載状態）更新 */
    UPDATE_ROOM_STATUS("updateRoomStatus"),

    /** 居住用先行物件登録 */
    CREATE_EXCLUSIVE_HOUSING("createExclusiveHousing"),

    /** 事業用先行物件登録 */
    CREATE_EXCLUSIVE_BUSINESS("createExclusiveBusiness"),

    /** 居住用先行物件編集 */
    UPDATE_EXCLUSIVE_HOUSING("updateExclusiveHousing"),

    /** 事業用先行物件編集 */
    UPDATE_EXCLUSIVE_BUSINESS("updateExclusiveBusiness"),

    /** 居住用先行物件削除 */
    DELETE_EXCLUSIVE_HOUSING("deleteExclusiveHousing"),

    /** 事業用先行物件削除 */
    DELETE_EXCLUSIVE_BUSINESS("deleteExclusiveBusiness"),
    ;
}
