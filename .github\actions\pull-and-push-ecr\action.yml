name: pull_and_push_ecr

inputs:
  prd_account_id:
    required: true
    type: string
  stg_account_id:
    required: true
    type: string
  prd_deploy_env:
    required: true
    type: string
  stg_deploy_env:
    required: true
    type: string
  prd_ecr_repository:
    required: true
    type: string
  stg_ecr_repository:
    required: true
    type: string
  use_arm64:
    required: true
    type: boolean
    default: false
  ssm_parameter_name:
    required: true
    type: string
  stg_ssm_parameter_name:
    required: true
    type: string

permissions:
  contents: read
  id-token: write
  actions: read

runs:
  using: 'composite'
  steps:
    - name: Configure STG AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-region: ap-northeast-1
        role-to-assume: arn:aws:iam::${{ inputs.stg_account_id }}:role/GitHubActions-${{ inputs.stg_deploy_env }}-ECRDeployRole

    - name: Read Version from SSM Parameter
      id: read_version
      run: |
        version=$(aws ssm get-parameter --name ${{ inputs.stg_ssm_parameter_name }} --query "Parameter.Value" --output text)
        if [ $? -eq 0 ]; then
          echo "version=$version" >> $GITHUB_ENV
        else
          echo "Failed to read version from SSM parameter: ${{ inputs.stg_ssm_parameter_name }}"
          exit 1
        fi
      shell: bash

    - name: Login to Amazon ECR
      run: |
        aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin ${{ inputs.stg_ecr_repository }}
      shell: bash

    - name: Pull Docker Image from STG
      run: |
        docker pull ${{ inputs.stg_ecr_repository }}:${{ env.version }}
      shell: bash

    - name: Tag Docker Image for PRD
      run: |
        docker tag ${{ inputs.stg_ecr_repository }}:${{ env.version }} ${{ inputs.prd_ecr_repository }}:${{ env.version }}
      shell: bash

    - name: Configure PRD AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-region: ap-northeast-1
        role-to-assume: arn:aws:iam::${{ inputs.prd_account_id }}:role/GitHubActions-${{ inputs.prd_deploy_env }}-ECRDeployRole

    - name: Login to Amazon ECR
      run: |
        aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin ${{ inputs.prd_ecr_repository }}
      shell: bash

    - name: Check if Image Exists
      id: check_image
      run: |
        repo_name=$(echo ${{ inputs.prd_ecr_repository }} | awk -F'/' '{print $2}')
        if aws ecr describe-images --repository-name $repo_name --image-ids imageTag=${{ env.version }} >/dev/null 2>&1; then
          echo "exists=true" >> $GITHUB_ENV
        else
          echo "exists=false" >> $GITHUB_ENV
        fi
      shell: bash

    - name: Push Docker Image
      if: env.exists == 'false'
      run: |
        docker push ${{ inputs.prd_ecr_repository }}:${{ env.version }}
      shell: bash

    - name: Update PRD SSM Parameter
      if: env.exists == 'false'
      run: |
        aws ssm put-parameter --name ${{ inputs.ssm_parameter_name }} --value ${{ env.version }} --type "String" --overwrite
      shell: bash

    - name: Skip Push Notification
      if: env.exists == 'true'
      run: echo "Image with tag '${{ env.version }}' already exists. Skipping build and push."
      shell: bash
