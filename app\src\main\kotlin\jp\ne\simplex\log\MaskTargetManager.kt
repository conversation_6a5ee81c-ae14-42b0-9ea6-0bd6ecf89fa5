package jp.ne.simplex.log

import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider
import org.springframework.core.type.filter.AnnotationTypeFilter

class MaskTargetManager {
    companion object {
        private const val BASE_PACKAGE = "jp.ne.simplex"
    }

    private var maskFieldNames: List<String>? = null

    fun getMaskFieldNames(): List<String> {
        return maskFieldNames ?: load()
    }

    private fun load(): List<String> {
        val classProvider = ClassPathScanningCandidateComponentProvider(false)
        classProvider.addIncludeFilter(AnnotationTypeFilter(MaskTarget::class.java))

        val candidateComponents = classProvider.findCandidateComponents(BASE_PACKAGE)

        val fieldNames = mutableListOf<String>()
        candidateComponents.forEach {
            val targetClass = Class.forName(it.beanClassName)
            targetClass.declaredFields.forEach { field ->
                if (field.isAnnotationPresent(MaskValue::class.java)) {
                    fieldNames.add(field.name)
                }
            }
        }

        maskFieldNames = fieldNames.distinct()

        return fieldNames
    }
}
