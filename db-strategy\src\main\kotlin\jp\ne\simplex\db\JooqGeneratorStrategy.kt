package jp.ne.simplex.db

import org.jooq.codegen.DefaultGeneratorStrategy
import org.jooq.codegen.GeneratorStrategy
import org.jooq.meta.Definition

open class JooqGeneratorStrategy : DefaultGeneratorStrategy() {

    override fun getJavaClassName(definition: Definition, mode: GeneratorStrategy.Mode): String {

        val name = super.getJavaClassName(definition, mode)

        return when (mode) {
            GeneratorStrategy.Mode.POJO -> "${name}Pojo"
            GeneratorStrategy.Mode.DEFAULT -> "${name}Table"
            else -> name
        }
    }
}