import {
  GetSecretValueCommand,
  SecretsManagerClient,
} from '@aws-sdk/client-secrets-manager';

export async function getSecretKey(secretName: string) {
  const region = process.env.REGION ?? 'local';

  const clientOptions = region === 'local' 
  ? { endpoint: 'http://localhost:4566' } 
  : { region: region };

  const client = new SecretsManagerClient(clientOptions);
  const secretValue = await client.send(
    new GetSecretValueCommand({
      SecretId: secretName,
    }),
  );
  if (secretValue.SecretString == null) {
    throw new Error(`Failed to get secret. secretName: ${secretName}`);
  }
  return JSON.parse(secretValue.SecretString);
}
