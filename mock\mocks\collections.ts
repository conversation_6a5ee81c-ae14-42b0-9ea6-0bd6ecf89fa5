const collections = [
  {
    id: 'base',
    routes: [
      'eboard_token_dp_issue:base',
      'eboard_token_verification_interceptor:base',
      'eboard_instruct_public:base',
      'eboard_update_kariosae:base',
      'eboard_maintenance_price_bikou:base',
      'eboard_update_parking_image:base',
      'eboard_update_garbage_image:base',
      'eboard_reserve_parking:base',
      'dkportal_update_parking_lot:base',
      'dkportal_update_adff_and_deposit_key_money:base',
      'dkportal_update_room_status:base',
      'dkportal_create_exclusive:base',
      'dkportal_update_exclusive:base',
      'dkportal_delete_exclusive:base',
    ],
  },
  {
    id: 'error',
    routes: [
      'eboard_token_dp_issue:error',
      'eboard_token_verification_interceptor:error',
      'eboard_instruct_public:error',
      'eboard_update_kariosae:error',
      'eboard_maintenance_price_bikou:error',
      'eboard_update_parking_image:error',
      'eboard_update_garbage_image:error',
      'eboard_reserve_parking:error',
      'dkportal_update_parking_lot:error',
      'dkportal_update_adff_and_deposit_key_money:error',
      'dkportal_update_room_status:error',
      'dkportal_create_exclusive:error',
      'dkportal_update_exclusive:error',
      'dkportal_delete_exclusive:error',
    ],
  },
];

export default collections;
