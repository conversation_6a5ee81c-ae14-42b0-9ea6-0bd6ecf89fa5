package jp.ne.simplex.application.service

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.application.model.ParkingVehicleInfo
import jp.ne.simplex.application.repository.db.ParkingDetailsRepository
import jp.ne.simplex.application.repository.db.ParkingRepository
import jp.ne.simplex.application.repository.db.ParkingReservationRepository
import jp.ne.simplex.db.jooq.gen.tables.pojos.DkLinkControlPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingAdditionalInfoMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.TenantContractBulkCollectionFilePojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ThirtyFiveYearBulkBuildingFileContPojo
import jp.ne.simplex.db.jooq.gen.tables.records.DkLinkControlRecord
import jp.ne.simplex.db.jooq.gen.tables.records.ParkingAdditionalInfoMasterRecord
import jp.ne.simplex.db.jooq.gen.tables.records.TenantContractBulkCollectionFileRecord
import jp.ne.simplex.db.jooq.gen.tables.records.ThirtyFiveYearBulkBuildingFileContRecord
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.mock.MockVacantParkingListRepository
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class ParkingDetailsServiceTest : AbstractTestContainerTest() {

    companion object {
        private val orderCode = Building.OrderCode.of("0000001")
        private val BUILDING_CODE = "${orderCode.value}01"
        private val BUILDING_CODE_2 = "${orderCode.value}02"
        private val BUILDING_CODE_3 = "${orderCode.value}03"
        private val BUILDING_CODE_4 = "${orderCode.value}04"
    }

    private lateinit var pdService: ParkingDetailsService

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(
            PARKING,
            BUILDING_MASTER,
            PARKING_RESERVATION,
            ADDRESS_MASTER,
            PARKING_ENABLE,
            PARKING_ADDITIONAL_INFO_MASTER,
            TENANT_CONTRACT,
            CUSTOMER,
            CONTRACT,
            THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT,
            DK_LINK_CONTROL,
            PARKING_VEHICLE_INFO_FILE,
            LATEST_RENT_EVALUATION,
            TENANT_CONTRACT_BULK_COLLECTION_FILE,
            ROOM_MASTER,
            TENANT
        )
    }

    override fun beforeEach() {
        val parkingRepository = ParkingRepository(dslContext)
        pdService = ParkingDetailsService(
            parkingRepository = parkingRepository,
            parkingDetailsRepository = ParkingDetailsRepository(
                dslContext,
                parkingRepository,
                ParkingReservationRepository(dslContext)
            ),
            vacantParkingListRepository = MockVacantParkingListRepository()
        )
        // 共通マスタデータの登録
        dslContext.saveParkingPojo(
            stubParkingPojo(
                buildingCode = BUILDING_CODE,
                parkingLotCode = "101",
                logicalDeleteFlag = 0,
                parkingLotNumber = "1",
                bulkLeaseFlag = 5,
            ),
            stubParkingPojo(
                buildingCode = BUILDING_CODE_2,
                parkingLotCode = "201",
                parkingCategory = "1",
                logicalDeleteFlag = 0,
                parkingLotNumber = "2",
            ),
        )
        dslContext.saveBuildingMasterPojo(
            stubBuildingMasterPojo(
                buildingCode = BUILDING_CODE,
                buildingName = "テスト建物名1",
                prefectureCode = "01",
                cityCode = "00",
                townCode = "000000",
                addressDetail = "テスト建物1の所在地",
                bulkLeaseFlag = 4,
                landlordCode = "********",
                completionDeliveryDate = 20250101,
            ),
            stubBuildingMasterPojo(
                buildingCode = BUILDING_CODE_2,
                buildingName = "テスト建物名2",
            ),
        )
        dslContext.saveCustomerPojo(
            stubCustomerPojo(
                integratedClientCode = "********00",
                clientNameKanji = "家主名",
            ),
        )
        dslContext.saveAddressMasterPojo(
            stubAddressMasterPojo(
                addressCode = "0100000000",
                postalCode = "1234567",
                prefectureKanjiName = "東京都",
                cityKanjiName = "世田谷区",
                townKanjiName = "三軒茶屋",
            ),
        )
        dslContext.saveContractPojo(
            stubContractPojo(
                effectiveStartDate = 0,
                effectiveEndDate = 99999999,
                buildingCd = BUILDING_CODE,
                initialSetupSign = "1",
                contractType = "08000",
                logicalDeleteSign = "0"
            ),
        )
        dslContext.save(
            THIRTY_FIVE_YEAR_BULK_BUILDING_FILE_CONT,
            recordConstructor = { p: ThirtyFiveYearBulkBuildingFileContPojo ->
                ThirtyFiveYearBulkBuildingFileContRecord(
                    p
                )
            },
            pojos = listOf(
                ThirtyFiveYearBulkBuildingFileContPojo(
                    buildingCd = BUILDING_CODE,
                    effectiveDate = 0,
                    bulkLeaseType = 3,
                    logicalDeleteSign = "0"
                ),
            )
        )
        dslContext.saveParkingEnablePojo(
            // レコードがない場合は利用可能と判断される
//            stubParkingEnablePojo(
//                buildingCode = BUILDING_CODE,
//                parkingLotCode = "101",
//                parkingLotEnable = "1",
//                deleteFlag = "0",
//            ),
            stubParkingEnablePojo(
                buildingCode = BUILDING_CODE_2,
                parkingLotCode = "201",
                parkingLotEnable = "1",
                deleteFlag = "0",
            ),
        )
    }

    @Nested
    @DisplayName("getParkingList:駐車場詳細リスト取得・クライアント")
    inner class Scenario1 {
        @BeforeEach
        fun setup() {
            //WELCOME_PARK予約の場合は空き判定される
            dslContext.saveParkingReservationPojo(
                stubParkingReservationPojo(
                    parkingReservationId = "012345678901234567890123456789000001",
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = "101",
                    reserveStatus = ParkingReservation.Status.RESERVATION,
                    requestSource = ParkingReservation.RequestSource.WELCOME_PARK,
                ),
            )
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000001",
                    tenantCode = "B12483019",
                    currentStateDivision = "35",
                    modificationStateDivision = "35",
                    moveOutDate = 0,
                    logicalDeleteSign = 0
                ),
            )
            dslContext.saveParkingVehicleInfoFilePojo(
                stubParkingVehicleInfoFilePojo(
                    tenantContractNumber = "10000001",
                    landTransportName = "世田谷",
                    type = "330",
                    businessCategory = "い",
                    leftNumber = "12",
                    rightNumber = "34",
                    manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA,
                    carModelName = "レクサス",
                    lightVehicleSign = "1",
                    parkingCertIssueSign = ParkingVehicleInfo.ParkingCertIssueSign.ISSUED.byte.toString(),
                    parkingCertComment = "発給あり",
                    tandemSign = "1",
                ),
            )
            dslContext.saveTenantPojo(
                stubTenantPojo(
                    tenantCode = "B12483010",
                    tenantNameKanji = "駐車場契約者",
                ),
            )
            dslContext.save(
                table = DK_LINK_CONTROL,
                recordConstructor = { p: DkLinkControlPojo -> DkLinkControlRecord(p) },
                pojos = listOf(
                    DkLinkControlPojo(
                        key = "ALLOW_ALL_PARKING_LOT_AVAILABILITY_EDIT",
                        value = "1",
                    )
                )
            )
            // 部屋契約情報
            dslContext.save(
                table = TENANT_CONTRACT_BULK_COLLECTION_FILE,
                recordConstructor = { p: TenantContractBulkCollectionFilePojo ->
                    TenantContractBulkCollectionFileRecord(
                        p
                    )
                },
                pojos = listOf(
                    TenantContractBulkCollectionFilePojo(
                        tenantContractNumber = "30000001",
                        parkingTenantContract = "10000001",
                        parentTenantBuildingCd = BUILDING_CODE_2,
                        parentTenantRoomCd = "02010",
                        deletionDate = 0
                    )
                )
            )
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    contractNumber = "30000001",
                    tenantCode = "B22483019",
                    tenantName = "部屋入居者",
                ),
            )
            dslContext.saveTenantPojo(
                stubTenantPojo(
                    tenantCode = "B22483010",
                    tenantNameKanji = "部屋契約者"
                ),
            )
            dslContext.saveRoomMasterPojo(
                stubRoomMasterPojo(
                    buildingCode = BUILDING_CODE_2,
                    roomCode = "02010",
                    roomNumber = "0203",
                ),
            )
        }

        @Test
        @DisplayName("受注コードに紐づく建物棟2つの、空き（予約有り）と入居中の駐車場区画リストを取得する。")
        fun test1() {
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(2, parkingList.size)

                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertEquals("テスト建物名1", parkingList[0].building.name.value)
                assertEquals("1234567", parkingList[0].building.postalCode)
                assertEquals(
                    "東京都 世田谷区 三軒茶屋 テスト建物1の所在地",
                    parkingList[0].building.location
                )
                assertEquals(
                    Building.SubleaseType.NEW_IKKATSU_PARKING.value,
                    parkingList[0].building.buildingContractForm
                )
                assertEquals("家主名", parkingList[0].building.landlordName)
                assertEquals("20250101", parkingList[0].building.completionDeliveryDate?.yyyyMMdd())
                assertEquals(true, parkingList[0].allowAllParkingLotAvailabilityEdit)
                assertEquals(1, parkingList[0].parkingLotList.size)
                assertEquals("101", parkingList[0].parkingLotList[0].id.parkingLotCode.value)
                assertEquals("1", parkingList[0].parkingLotList[0].localDisplayNumber)
                assertEquals(
                    ParkingLot.StatusDivision.VACANT,
                    parkingList[0].parkingLotList[0].parkingStatusDivision
                )
                assertEquals(
                    ParkingLot.VacancyStatus.POSSIBLE,
                    parkingList[0].parkingLotList[0].vacancyParkingStatus
                )
                assertEquals(true, parkingList[0].parkingLotList[0].isAvailable)
                assertEquals(
                    Building.SubleaseType.NEW_KANRIDAITATE_PARKING.value,
                    parkingList[0].parkingLotList[0].contractForm
                )
                assertEquals(1, parkingList[0].parkingLotList[0].reservationList.size)
                assertNull(parkingList[0].parkingLotList[0].tenant)

                assertEquals(BUILDING_CODE_2, parkingList[1].building.code.value)
                assertEquals(1, parkingList[1].parkingLotList.size)
                assertEquals("201", parkingList[1].parkingLotList[0].id.parkingLotCode.value)
                assertEquals("2", parkingList[1].parkingLotList[0].localDisplayNumber)
                assertEquals(
                    ParkingLot.StatusDivision.OCCUPIED,
                    parkingList[1].parkingLotList[0].parkingStatusDivision
                )
                assertEquals(
                    ParkingLot.VacancyStatus.IMPOSSIBLE,
                    parkingList[1].parkingLotList[0].vacancyParkingStatus
                )
                assertEquals(true, parkingList[1].parkingLotList[0].isAvailable)
                assertEquals(0, parkingList[1].parkingLotList[0].reservationList.size)
                assertEquals(
                    "10000001",
                    parkingList[1].parkingLotList[0].tenant?.tenantContractNumber
                )
                assertEquals(
                    "10000001",
                    parkingList[1].parkingLotList[0].tenant?.originalContractNumber
                )
                assertEquals(
                    "駐車場契約者",
                    parkingList[1].parkingLotList[0].tenant?.tenantContractName
                )
                assertEquals("世田谷", parkingList[1].parkingLotList[0].tenant?.landTransportName)
                // 部屋契約情報
                assertEquals(
                    BUILDING_CODE_2,
                    parkingList[1].parkingLotList[0].linkedPropertyId?.buildingCode?.value
                )
                assertEquals(
                    "02010",
                    parkingList[1].parkingLotList[0].linkedPropertyId?.roomCode?.value
                )
                assertEquals(
                    "203",
                    parkingList[1].parkingLotList[0].linkedRoomNumber?.getFormattedValue()
                )
                assertEquals(
                    "部屋契約者",
                    parkingList[1].parkingLotList[0].tenant?.propertyTenantContractName
                )
                assertEquals(
                    "部屋入居者",
                    parkingList[1].parkingLotList[0].tenant?.propertyTenantName
                )
            }
        }

        @Test
        @DisplayName("受注コードに紐づく建物棟3つの、駐車場区画リストを取得する。")
        fun test2() {
            dslContext.saveBuildingMasterPojo(
                stubBuildingMasterPojo(
                    buildingCode = BUILDING_CODE_3,
                    buildingName = "テスト建物名3",
                    addressDetail = "テスト建物3の所在地",
                ),
                stubBuildingMasterPojo(
                    buildingCode = BUILDING_CODE_4,
                    buildingName = "テスト建物名4",
                    addressDetail = "テスト建物4の所在地",
                ),
            )
            dslContext.saveParkingPojo(
                stubParkingPojo(
                    buildingCode = BUILDING_CODE_4,
                    parkingLotCode = "401",
                    transferredBuildingCode = BUILDING_CODE_3,
                ),
            )
            dslContext.saveParkingInfoMasterPojo(
                stubParkingInfoMasterPojo(
                    buildingCode = BUILDING_CODE_4,
                    parkingCode = "401",
                ),
            )
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(4, parkingList.size)

                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertEquals(1, parkingList[1].parkingLotList.size)

                assertEquals(BUILDING_CODE_2, parkingList[1].building.code.value)
                assertEquals(1, parkingList[1].parkingLotList.size)

                assertEquals(BUILDING_CODE_3, parkingList[2].building.code.value)
                assertEquals(0, parkingList[2].parkingLotList.size) // 区画を最初からもっていない

                assertEquals(BUILDING_CODE_4, parkingList[3].building.code.value)
                assertEquals(0, parkingList[3].parkingLotList.size) // 区画を持っていたがなくなった
            }
        }

    }

    @Nested
    @DisplayName("toEboardParkingPojo:駐車場詳細リスト取得・テナント契約処理")
    inner class Scenario2 {
        @BeforeEach
        fun setup() {
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    contractNumber = "20000001",
                    buildingCode = BUILDING_CODE_2,
                    roomCode = "02010",
                ),
            )
        }

        @Test
        @DisplayName("handlePreApplicationAggregate:入居前合算の処理有り駐車場区画リストを取得する。")
        fun test1() {
            dslContext.saveTenantContractPojo(
                //　入居前合算先
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = "101",
                    contractNumber = "10000001",
                    currentStateDivision = "20",
                    modificationStateDivision = "00",
                    moveOutDate = 0,
                    logicalDeleteSign = 1,
                    tenantName = "テナント名1",
                    aggregateContractNumber = "10000000",
                ),
                //　入居前合算元
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = "101",
                    contractNumber = "10000000",
                    currentStateDivision = "35",
                    modificationStateDivision = "35",
                    moveOutDate = 0,
                    moveInStartProcessedSign = 0,
                    logicalDeleteSign = 0,
                    tenantName = "テナント名1",
                ),
            )
            pdService.getParkingList(orderCode).let { parkingList ->
                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertEquals(1, parkingList[0].parkingLotList.size)
                assertEquals("101", parkingList[0].parkingLotList[0].id.parkingLotCode.value)
                assertEquals(
                    ParkingLot.StatusDivision.COLLECTING,
                    parkingList[0].parkingLotList[0].parkingStatusDivision
                )
                assertEquals(
                    ParkingLot.VacancyStatus.IMPOSSIBLE,
                    parkingList[0].parkingLotList[0].vacancyParkingStatus
                )
            }
        }

        @Test
        @DisplayName("handlePreviousContractRetrieval:前テ契の取得処理なし駐車場区画リストを取得する。")
        fun test2() {
            dslContext.saveTenantContractPojo(
                //　現在テナント契約
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000012",
                    currentStateDivision = "20",
                    modificationStateDivision = "01",
                    moveOutDate = 20250501, // 常にVACANTとなる
                    moveInScheduledDate = 20250430,
                    logicalDeleteSign = 0,
                    tenantName = "テナント名2",
                ),
            )
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(BUILDING_CODE_2, parkingList[1].building.code.value)
                assertEquals(1, parkingList[1].parkingLotList.size)
                assertNull(
                    parkingList[1].parkingLotList[0].linkedPropertyId?.buildingCode?.value
                )
                assertNull(
                    parkingList[1].parkingLotList[0].linkedPropertyId?.roomCode?.value
                )
                assertNull(
                    parkingList[1].parkingLotList[0].linkedRoomNumber?.getFormattedValue()
                )
                assertNull(parkingList[1].parkingLotList[0].tenant?.tenantContractNumber)
                assertNull(parkingList[1].parkingLotList[0].tenant?.originalContractNumber)
                assertEquals(
                    ParkingLot.StatusDivision.VACANT,
                    parkingList[1].parkingLotList[0].parkingStatusDivision
                )
                assertEquals(
                    ParkingLot.VacancyStatus.POSSIBLE,
                    parkingList[1].parkingLotList[0].vacancyParkingStatus
                )
            }
        }

        @Test
        @DisplayName("handlePreviousContractRetrieval:前テ契の取得処理有りかつ前テ契の入居前合算処理判定なし駐車場区画リストを取得する。")
        fun test3() {
            dslContext.saveTenantContractPojo(
                //　現在テナント契約
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000012",
                    currentStateDivision = "20",
                    modificationStateDivision = "01",
                    moveOutDate = 20250501, // 常にVACANTとなる
                    moveInScheduledDate = 20250601, // 無効なテ契約
                    logicalDeleteSign = 0,
                    tenantName = "テナント名2",
                ),
                //　前テナント契約
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000011",
                    currentStateDivision = "35",
                    modificationStateDivision = "35",
                    moveOutDate = 0,
                    moveInStartProcessedSign = 0,
                    logicalDeleteSign = 0,
                    tenantName = "テナント名2",
                ),
            )
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(BUILDING_CODE_2, parkingList[1].building.code.value)
                assertEquals(1, parkingList[1].parkingLotList.size)
                // VACANTでないこと
                assertEquals(
                    ParkingLot.StatusDivision.COLLECTING,
                    parkingList[1].parkingLotList[0].parkingStatusDivision
                )
                assertEquals(
                    ParkingLot.VacancyStatus.IMPOSSIBLE,
                    parkingList[1].parkingLotList[0].vacancyParkingStatus
                )
            }
        }

        @Test
        @DisplayName("handlePreviousContractRetrieval:前テ契の取得処理有りかつ前テ契の入居前合算処理判定あり駐車場区画リストを取得する。")
        fun test4() {
            dslContext.saveTenantContractPojo(
                //　現在テナント契約
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000012",
                    currentStateDivision = "20",
                    modificationStateDivision = "01",
                    moveOutDate = 20250501, // 常にVACANTとなる
                    moveInScheduledDate = 20250601, // 無効なテ契約
                    logicalDeleteSign = 0,
                    tenantName = "テナント名2",
                ),
                //　前テナント契約 = 入居前合算先
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000011",
                    currentStateDivision = "35",
                    modificationStateDivision = "00",
                    moveOutDate = 0,
                    moveInStartProcessedSign = 0,
                    logicalDeleteSign = 1,
                    tenantName = "テナント名2",
                    aggregateContractNumber = "10000010",
                ),
                //　入居前合算元
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000010",
                    currentStateDivision = "25",
                    modificationStateDivision = "01",
                    moveOutDate = 0,
                    moveInStartProcessedSign = 0,
                    logicalDeleteSign = 0,
                    tenantName = "テナント名1",
                ),
            )
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(BUILDING_CODE_2, parkingList[1].building.code.value)
                assertEquals(1, parkingList[1].parkingLotList.size)
                // COLLECTINGでないこと
                assertEquals(
                    ParkingLot.StatusDivision.CONFIRMED,
                    parkingList[1].parkingLotList[0].parkingStatusDivision
                )
                assertEquals(
                    ParkingLot.VacancyStatus.IMPOSSIBLE,
                    parkingList[1].parkingLotList[0].vacancyParkingStatus
                )
            }
        }

        @Test
        @DisplayName("handleTenantRoomCodeAndNumber:テナント契約一括残集ファイルあり駐車場区画リストを取得する。")
        fun test5() {
            dslContext.saveTenantContractPojo(
                //　現在テナント契約
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = "101",
                    contractNumber = "10000001",
                    currentStateDivision = "20",
                    modificationStateDivision = "01",
                    moveOutDate = 0,
                    logicalDeleteSign = 0,
                    tenantName = "テナント名1",
                ),
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000012",
                    currentStateDivision = "20",
                    modificationStateDivision = "01",
                    moveOutDate = 20250501, // 常にVACANTとなる
                    moveInScheduledDate = 20250430,
                    logicalDeleteSign = 0,
                    tenantName = "テナント名2",
                ),
            )
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertEquals(1, parkingList[0].parkingLotList.size)
                // BUILDING_CODE_2でない
                assertNull(
                    parkingList[0].parkingLotList[0].linkedPropertyId?.buildingCode?.value
                )
                // 02010でない
                assertNull(
                    parkingList[0].parkingLotList[0].linkedPropertyId?.roomCode?.value
                )

                assertEquals(BUILDING_CODE_2, parkingList[1].building.code.value)
                assertEquals(1, parkingList[1].parkingLotList.size)
                // BUILDING_CODE_2でない
                assertNull(
                    parkingList[1].parkingLotList[0].linkedPropertyId?.buildingCode?.value
                )
                // 02010でない
                assertNull(
                    parkingList[1].parkingLotList[0].linkedPropertyId?.roomCode?.value
                )
                assertNull(
                    parkingList[1].parkingLotList[0].tenant
                )
            }
        }
    }

    @Nested
    @DisplayName("toEboardHandleConsolidatedRentEvaluation:駐車場詳細リスト取得・建物合算処理")
    inner class Scenario3 {
        @BeforeEach
        fun setup() {
            dslContext.saveParkingPojo(
                stubParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = "102",
                    logicalDeleteFlag = 1,
                    parkingLotNumber = "2",
                    consolidatedBuildingCode = BUILDING_CODE,
                    consolidatedParkingCode = "101",
                ),
            )
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = "101",
                    contractNumber = "10000001",
                    currentStateDivision = "35",
                    modificationStateDivision = "35",
                    moveOutDate = 0,
                    logicalDeleteSign = 0
                ),
            )
            dslContext.saveLatestRentEvaluationPojo(
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "00000001",
                    latestRentAssessmentHistory = "00",
                    buildingCode = BUILDING_CODE,
                    propertyCode = "101",
                    roomParkingDivision = "2",
                    keyMoneyAmount = 10000,
                    depositAmount = 20000,
                    parkingFee = 30000,
                    standardRentForCoop = 10001,
                ),
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "00000002",
                    latestRentAssessmentHistory = "00",
                    buildingCode = BUILDING_CODE,
                    propertyCode = "102",
                    roomParkingDivision = "2",
                    keyMoneyAmount = 5000,
                    depositAmount = 10000,
                    parkingFee = 15000,
                    taxDivision = "1",
                    keyMoneyInoutDivision = "1",
                    parkingFeeInoutDivision = "1",
                    brokerApplicationCollectionDivision = "1",
                ),
            )
        }

        @Test
        @DisplayName("toEboardHandleConsolidatedRentEvaluation:合算先から合算元の礼金、敷金、駐車料を減算する。")
        fun test1() {
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertEquals(2, parkingList[0].parkingLotList.size)
                assertEquals("101", parkingList[0].parkingLotList[0].id.parkingLotCode.value)
                assertEquals(
                    ParkingLot.StatusDivision.OCCUPIED,
                    parkingList[0].parkingLotList[0].parkingStatusDivision
                )
                // 30000でない
                assertEquals(15000, parkingList[0].parkingLotList[0].parkingFee)
                assertEquals(1, parkingList[0].parkingLotList[0].parkingFeeInTax) // 合算先が適用される
                assertEquals("102", parkingList[0].parkingLotList[1].id.parkingLotCode.value)
                // VACANTでない
                assertEquals(
                    ParkingLot.StatusDivision.OCCUPIED,
                    parkingList[0].parkingLotList[1].parkingStatusDivision
                )
                assertEquals(15000, parkingList[0].parkingLotList[1].parkingFee)
                assertEquals(1, parkingList[0].parkingLotList[1].parkingFeeInTax)
            }
        }
    }

    @Nested
    @DisplayName("toEboardFilteredParkingList:駐車場詳細リスト取得・区画除外処理")
    inner class Scenario4 {
        @BeforeEach
        fun setup() {
            dslContext.saveParkingPojo(
                stubParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = "102",
                    logicalDeleteFlag = 0,
                    parkingLotNumber = "3",
                    transferredBuildingCode = BUILDING_CODE_2,
                ),
                stubParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = "***",
                    logicalDeleteFlag = 0,
                    parkingLotNumber = "ﾀﾞﾐｰ",
                ),
            )
        }

        @Test
        @DisplayName("toEboardFilteredParkingList:付替元建物コードがある場合は除外される。")
        fun test1() {
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertEquals(1, parkingList[0].parkingLotList.size)
            }
        }

        @Test
        @DisplayName("toEboardFilteredParkingList:駐車場コードが***である場合は除外される。")
        fun test2() {
            pdService.getParkingList(orderCode, true).let { parkingList ->
                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertEquals(1, parkingList[0].parkingLotList.size)
            }
        }
    }

    @Nested
    @DisplayName("getParkingLot:駐車場詳細リスト取得・駐車場区画取得")
    inner class Scenario5 {
        @BeforeEach
        fun setup() {
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = "101",
                    contractNumber = "10000001",
                    currentStateDivision = "35", // 退去予定
                    modificationStateDivision = "40", // 退去予定
                    moveOutDate = 0,
                    vacateScheduledDate = 20250101,
                    logicalDeleteSign = 0
                ),
            )
            dslContext.saveParkingVehicleInfoFilePojo(
                stubParkingVehicleInfoFilePojo(
                    tenantContractNumber = "10000001",
                    landTransportName = "世田谷",
                    type = "330",
                    businessCategory = "い",
                    leftNumber = "12",
                    rightNumber = "34",
                    manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA,
                    carModelName = "レクサス",
                    lightVehicleSign = "1",
                    parkingCertIssueSign = ParkingVehicleInfo.ParkingCertIssueSign.ISSUED.byte.toString(),
                    parkingCertComment = "発給あり",
                    tandemSign = "1",
                ),
            )
            dslContext.saveLatestRentEvaluationPojo(
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "00000001",
                    latestRentAssessmentHistory = "00",
                    buildingCode = BUILDING_CODE,
                    propertyCode = "101",
                    roomParkingDivision = "2",
                    keyMoneyAmount = 10000,
                    depositAmount = 20000,
                    parkingFee = 30000,
                    standardRentForCoop = 10001,
                ),
            )
        }

        @Test
        @DisplayName("建物・区画コードに紐づく、退去予定かつ退去予定日のある駐車場区画を取得する。")
        fun test1() {
            pdService.getParkingLot(
                ParkingLot.Id(
                    Building.Code.of(BUILDING_CODE),
                    ParkingLot.Code.of("101")
                )
            ).let {
                assertNotNull(it)
                assertEquals(BUILDING_CODE, it.id.buildingCode.value)
                assertEquals("101", it.id.parkingLotCode.value)
                assertEquals(ParkingLot.StatusDivision.PLANNED_MOVE_OUT, it.parkingStatusDivision)
                assertEquals("20250101", it.expectedMoveOutDate?.yyyyMMdd())
                assertEquals("10000001", it.tenant?.tenantContractNumber)
                assertNull(it.linkedPropertyId)
                assertEquals(30000, it.parkingFee)
                assertEquals(0, it.parkingFeeInTax)
            }
        }

        @Test
        @DisplayName("建物・区画コードに紐づく、合算元のある駐車場区画(合算先が付替元建物コードなし)を取得する。")
        fun test2() {
            dslContext.saveParkingPojo(
                stubParkingPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingLotCode = "202",
                    logicalDeleteFlag = 1,
                    parkingLotNumber = "3",
                    consolidatedBuildingCode = BUILDING_CODE,
                    consolidatedParkingCode = "101",
                ),
            )
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "202",
                    contractNumber = "10000002",
                    currentStateDivision = "35",
                    modificationStateDivision = "40",
                    moveOutDate = 20250501, // 常にVACANTとなる
                    moveInScheduledDate = 20250430,
                    vacateScheduledDate = 0,
                    logicalDeleteSign = 0
                ),
            )
            dslContext.saveParkingVehicleInfoFilePojo(
                stubParkingVehicleInfoFilePojo(
                    tenantContractNumber = "10000002",
                    landTransportName = "品川",
                ),
            )
            dslContext.saveLatestRentEvaluationPojo(
                stubLatestRentEvaluationPojo(
                    assessmentReviewNumber = "00000002",
                    latestRentAssessmentHistory = "00",
                    buildingCode = BUILDING_CODE_2,
                    propertyCode = "202",
                    roomParkingDivision = "2",
                    keyMoneyAmount = 5000,
                    depositAmount = 10000,
                    parkingFee = 15000,
                    taxDivision = "1",
                    keyMoneyInoutDivision = "1",
                    parkingFeeInoutDivision = "1",
                    brokerApplicationCollectionDivision = "1",
                ),
            )
            // 全部で3区画取れる
            assertEquals(
                3, pdService.getParkingList(orderCode, true).fold(
                    initial = 0,
                    operation = { acc, parking -> acc + parking.parkingLotList.size })
            )

            // 合算先の区画を取得
            pdService.getParkingLot(
                ParkingLot.Id(
                    Building.Code.of(BUILDING_CODE),
                    ParkingLot.Code.of("101")
                )
            ).let {
                assertNotNull(it)
                assertEquals(BUILDING_CODE, it.id.buildingCode.value)
                assertEquals("101", it.id.parkingLotCode.value)
                assertEquals(ParkingLot.StatusDivision.PLANNED_MOVE_OUT, it.parkingStatusDivision)
                assertEquals("20250101", it.expectedMoveOutDate?.yyyyMMdd())
                assertEquals(15000, it.parkingFee) // 合算元の駐車料が減算される
                assertEquals(1, it.parkingFeeInTax) //合算元の税込みフラグが適用される
                assertEquals("10000001", it.tenant?.tenantContractNumber)
                assertNull(it.linkedPropertyId)
                assertEquals("世田谷", it.tenant?.landTransportName)
            }

            // 合算元の区画を取得
            pdService.getParkingLot(
                ParkingLot.Id(
                    Building.Code.of(BUILDING_CODE_2),
                    ParkingLot.Code.of("202")
                )
            ).let {
                assertNotNull(it)
                assertEquals(BUILDING_CODE_2, it.id.buildingCode.value)
                assertEquals("202", it.id.parkingLotCode.value)
                assertEquals(
                    ParkingLot.StatusDivision.PLANNED_MOVE_OUT,
                    it.parkingStatusDivision
                ) //空きとはならず合算先がセットされる
                assertEquals("20250101", it.expectedMoveOutDate?.yyyyMMdd()) //合算先がセットされる
                assertEquals(15000, it.parkingFee)
                assertEquals(1, it.parkingFeeInTax)
                assertEquals("10000002", it.tenant?.tenantContractNumber) //合算先はセットされない
                assertEquals("10000002", it.tenant?.originalContractNumber) //合算先はセットされない
                assertNull(it.linkedPropertyId) //合算先はセットされない
                assertEquals("品川", it.tenant?.landTransportName)//空きでクリアされず、かつ合算先はセットされない
            }
        }

        @Test
        @DisplayName("建物・区画コードに紐づく、合算元のある駐車場区画(合算先が付替元建物コードあり)を取得する。")
        fun test3() {
            dslContext.saveParkingPojo(
                stubParkingPojo(
                    buildingCode = BUILDING_CODE,
                    parkingLotCode = "102",
                    logicalDeleteFlag = 0,
                    parkingLotNumber = "3",
                    transferredBuildingCode = BUILDING_CODE_2,
                ),
                stubParkingPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingLotCode = "202",
                    logicalDeleteFlag = 1,
                    parkingLotNumber = "3",
                    consolidatedBuildingCode = BUILDING_CODE,
                    consolidatedParkingCode = "102",
                ),
            )
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE,
                    parkingCode = "102",
                    contractNumber = "10000011",
                    currentStateDivision = "25",
                    modificationStateDivision = "25",
                    moveOutDate = 0,
                    tenantName = "テナント名1",
                    logicalDeleteSign = 0
                ),
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "202",
                    contractNumber = "10000012",
                    currentStateDivision = "35",
                    modificationStateDivision = "40",
                    moveOutDate = 20250501, // 常にVACANTとなる
                    moveInScheduledDate = 20250430,
                    vacateScheduledDate = 0,
                    logicalDeleteSign = 0
                ),
            )
            dslContext.saveParkingVehicleInfoFilePojo(
                stubParkingVehicleInfoFilePojo(
                    tenantContractNumber = "10000012",
                    landTransportName = "品川",
                ),
            )
            dslContext.save(
                table = PARKING_ADDITIONAL_INFO_MASTER,
                recordConstructor = { p: ParkingAdditionalInfoMasterPojo ->
                    ParkingAdditionalInfoMasterRecord(p)
                },
                pojos = listOf(
                    ParkingAdditionalInfoMasterPojo(
                        buildingCode = BUILDING_CODE,
                        parkingCode = "102",
                        roomBuildingCode = BUILDING_CODE,
                        roomRoomCode = "01020",
                    )
                )
            )

            // 全部で3区画取れる
            assertEquals(
                3, pdService.getParkingList(orderCode, true).fold(
                    initial = 0,
                    operation = { acc, parking -> acc + parking.parkingLotList.size })
            )

            // 合算先の区画は付替元建物コードありなのでとれない
            assertNull(
                pdService.getParkingLot(
                    ParkingLot.Id(
                        Building.Code.of(BUILDING_CODE),
                        ParkingLot.Code.of("102")
                    )
                )
            )

            // 合算元の区画を取得
            pdService.getParkingLot(
                ParkingLot.Id(
                    Building.Code.of(BUILDING_CODE_2),
                    ParkingLot.Code.of("202")
                )
            ).let {
                assertNotNull(it)
                assertEquals(BUILDING_CODE_2, it.id.buildingCode.value)
                assertEquals("202", it.id.parkingLotCode.value)
                assertEquals(
                    ParkingLot.StatusDivision.CONFIRMED,
                    it.parkingStatusDivision
                ) //空きとはならず合算先がセットされる
                assertEquals("10000011", it.tenant?.tenantContractNumber) //合算先がセットされる
                assertEquals("10000012", it.tenant?.originalContractNumber) //合算先はセットされない
                assertEquals("01020", it.linkedPropertyId?.roomCode?.value) //合算先がセットされる
                assertEquals("品川", it.tenant?.landTransportName)//空きでクリアされず、かつ合算先はセットされない
            }
        }

    }

    @Nested
    @DisplayName("getParkingList:駐車場詳細リスト取得・外部API")
    inner class Scenario6 {
        @BeforeEach
        fun setup() {
            dslContext.saveTenantContractPojo(
                stubTenantContractPojo(
                    buildingCode = BUILDING_CODE_2,
                    parkingCode = "201",
                    contractNumber = "10000001",
                    tenantCode = "B12483019",
                    currentStateDivision = "35",
                    modificationStateDivision = "35",
                    moveOutDate = 0,
                    logicalDeleteSign = 0
                ),
            )
            dslContext.save(
                table = PARKING_ADDITIONAL_INFO_MASTER,
                recordConstructor = { p: ParkingAdditionalInfoMasterPojo ->
                    ParkingAdditionalInfoMasterRecord(p)
                },
                pojos = listOf(
                    ParkingAdditionalInfoMasterPojo(
                        buildingCode = BUILDING_CODE,
                        parkingCode = "101",
                        roomBuildingCode = "000099901",
                        roomRoomCode = "09090",
                    ),
                    ParkingAdditionalInfoMasterPojo(
                        buildingCode = BUILDING_CODE_2,
                        parkingCode = "201",
                        roomBuildingCode = "000099901",
                        roomRoomCode = "09090",
                    )
                )
            )
            dslContext.saveRoomMasterPojo(
                stubRoomMasterPojo(
                    buildingCode = "000099901",
                    roomCode = "09090",
                    roomNumber = "0909",
                ),
            )
        }

        @Test
        @DisplayName("部屋テナント契約は駐車場付加情報DBから取得する")
        fun test1() {
            pdService.getParkingList(orderCode, false).let { parkingList ->
                assertEquals(2, parkingList.size)
                assertEquals(BUILDING_CODE, parkingList[0].building.code.value)
                assertNull(parkingList[0].parkingLotList[0].linkedPropertyId) //空きのため空値

                assertEquals(BUILDING_CODE_2, parkingList[1].building.code.value)
                assertEquals("201", parkingList[1].parkingLotList[0].id.parkingLotCode.value)
                assertEquals(
                    "000099901",
                    parkingList[1].parkingLotList[0].linkedPropertyId?.buildingCode?.value
                )
                assertEquals(
                    "09090",
                    parkingList[1].parkingLotList[0].linkedPropertyId?.roomCode?.value
                )
                assertEquals(
                    "909",
                    parkingList[1].parkingLotList[0].linkedRoomNumber?.getFormattedValue()
                )
            }
        }

        @Test
        @DisplayName("複数棟ある場合に区画のない棟は除外される")
        fun test2() {
            dslContext.saveBuildingMasterPojo(
                stubBuildingMasterPojo(
                    buildingCode = BUILDING_CODE_3,
                    buildingName = "テスト建物名3",
                    addressDetail = "テスト建物3の所在地",
                ),
            )
            assertEquals(2, pdService.getParkingList(orderCode, false).size)
        }

    }
}
