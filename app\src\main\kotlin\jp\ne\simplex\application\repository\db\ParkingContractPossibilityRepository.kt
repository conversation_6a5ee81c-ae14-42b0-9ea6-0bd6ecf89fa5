package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.repository.db.extension.ParkingContractPossibilityEx.Companion.toParkingContractPossibility
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingContractPossibilityPojo
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_CONTRACT_POSSIBILITY
import jp.ne.simplex.exception.DBValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.Configuration
import org.jooq.DSLContext
import org.jooq.Field
import org.springframework.dao.CannotAcquireLockException
import org.springframework.dao.DuplicateKeyException
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class ParkingContractPossibilityRepository(private val context: DSLContext) :
    ParkingContractPossibilityRepositoryInterface {
    override fun findBy(orderCode: Building.OrderCode): ParkingContractPossibility? {
        return context.selectFrom(PARKING_CONTRACT_POSSIBILITY)
            .where(PARKING_CONTRACT_POSSIBILITY.ORDER_CODE.eq(orderCode.value))
            .and(PARKING_CONTRACT_POSSIBILITY.DELETE_FLAG.eq("0"))
            .fetchOneInto(ParkingContractPossibilityPojo::class.java)
            ?.toParkingContractPossibility()
    }

    @Throws(DBValidationException::class)
    override fun findByIdForUpdate(
        config: Configuration,
        orderCode: Building.OrderCode,
        waitSeconds: Int
    ): ParkingContractPossibility? {
        try {
            return config.dsl().selectFrom(PARKING_CONTRACT_POSSIBILITY)
                .where(PARKING_CONTRACT_POSSIBILITY.ORDER_CODE.eq(orderCode.value))
                .and(PARKING_CONTRACT_POSSIBILITY.DELETE_FLAG.eq("0"))
                .forUpdate()
                .wait(waitSeconds)
                .fetchOneInto(ParkingContractPossibilityPojo::class.java)
                ?.toParkingContractPossibility()
        } catch (ex: CannotAcquireLockException) {
            throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format(), ex)
        }
    }

    @Throws(DBValidationException::class)
    override fun register(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility
    ) {
        // @formatter:off
        val currentDateTime = LocalDateTime.now()
        try {
            config.dsl().insertInto(PARKING_CONTRACT_POSSIBILITY)
                .set(PARKING_CONTRACT_POSSIBILITY.ORDER_CODE, param.orderCode.value)
                .set(PARKING_CONTRACT_POSSIBILITY.IS_FIRST_PARKING_CONTRACT_POSSIBLE, param.firstParkingContractPossibility.value)
                .set(PARKING_CONTRACT_POSSIBILITY.IS_SECOND_PARKING_CONTRACT_POSSIBLE, param.secondParkingContractPossibility.value)
                .set(PARKING_CONTRACT_POSSIBILITY.IS_AUTO_JUDGE, param.isAutoJudge.value)
                .set(createInputMetaData(requestUser, currentDateTime))
                .set(createUpdateMetaData(requestUser, currentDateTime))
                .set(PARKING_CONTRACT_POSSIBILITY.DELETE_FLAG, "0")
                .execute()
        } catch (ex: DuplicateKeyException) {
            throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format(), ex)
        }
        // @formatter:on
    }

    override fun update(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility
    ) {
        // @formatter:off
        val currentDateTime = LocalDateTime.now()

        config.dsl().update(PARKING_CONTRACT_POSSIBILITY)
            .set(PARKING_CONTRACT_POSSIBILITY.ORDER_CODE, param.orderCode.value)
            .set(PARKING_CONTRACT_POSSIBILITY.IS_FIRST_PARKING_CONTRACT_POSSIBLE, param.firstParkingContractPossibility.value)
            .set(PARKING_CONTRACT_POSSIBILITY.IS_SECOND_PARKING_CONTRACT_POSSIBLE, param.secondParkingContractPossibility.value)
            .set(PARKING_CONTRACT_POSSIBILITY.IS_AUTO_JUDGE, param.isAutoJudge.value)
            .set(createUpdateMetaData(requestUser, currentDateTime))
            .where(PARKING_CONTRACT_POSSIBILITY.ORDER_CODE.eq(param.orderCode.value))
            .execute()
        // @formatter:on
    }

    private fun createInputMetaData(
        requestUser: AuthInfo.RequestUser, dateTime: LocalDateTime
    ): Map<Field<*>, Any?> {
        return mapOf<Field<*>, Any?>(
            PARKING_CONTRACT_POSSIBILITY.CREATION_DATE to dateTime.yyyyMMdd().toInt(),
            PARKING_CONTRACT_POSSIBILITY.CREATION_TIME to dateTime.HHmmss().toInt(),
            PARKING_CONTRACT_POSSIBILITY.CREATOR to requestUser.value,
        )
    }

    private fun createUpdateMetaData(
        requestUser: AuthInfo.RequestUser, dateTime: LocalDateTime
    ): Map<Field<*>, Any?> {
        return mapOf<Field<*>, Any?>(
            PARKING_CONTRACT_POSSIBILITY.UPDATE_DATE to dateTime.yyyyMMdd().toInt(),
            PARKING_CONTRACT_POSSIBILITY.UPDATE_TIME to dateTime.HHmmss().toInt(),
            PARKING_CONTRACT_POSSIBILITY.UPDATER to requestUser.value,
        )
    }
}

interface ParkingContractPossibilityRepositoryInterface {
    /** 駐車場契約可否取得(ID指定) */
    fun findBy(orderCode: Building.OrderCode): ParkingContractPossibility?

    /** 駐車場契約可否取得(ID指定)。存在する場合はレコードロック */
    fun findByIdForUpdate(
        config: Configuration,
        orderCode: Building.OrderCode,
        waitSeconds: Int = 10
    ): ParkingContractPossibility?

    /** 駐車場契約可否情報登録 */
    fun register(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility
    )

    /** 駐車場契約可否情報更新 */
    fun update(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility
    )
}
