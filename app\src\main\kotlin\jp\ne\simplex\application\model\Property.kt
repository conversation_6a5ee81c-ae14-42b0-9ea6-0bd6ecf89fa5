package jp.ne.simplex.application.model

import jp.ne.simplex.application.model.TemporaryReservationInfo.CancelledTemporaryReservationInfo
import java.time.LocalDate

class Property(
    val id: Id,
    // 部屋番号
    val roomNumber: Room.Number?,
    // 建物種別
    val buildingType: Building.Type?,
    // 斡旋申込内容照会（申込み状況）
    val recordStatusType: RecordStatusType?,
    // 空き家HP用.客付完了フラグ
    val customerCompletionFlag: Boolean,
    // 入居申込日
    val moveInApplicationDate: LocalDate?,
    // 方位
    val direction: String?,
    // 空き家HP用.変更区分
    val changeDivision: String?,
    val applicationScheduledDate: LocalDate?,
    val marketingBranchOfficeCode: Office.Code?
) {
    data class Id(
        /** 対象の建物コード **/
        val buildingCode: Building.Code,

        /** 対象の部屋コード **/
        val roomCode: Room.Code,
    )

    /** 物件種別 */
    enum class Type {
        /** 居住用 */
        RESIDENTIAL,

        /** 事業用 */
        COMMERCIAL,
        ;

        companion object {
            fun of(buildingType: Building.Type): Type {
                if (Building.Type.getResidentialTypes().contains(buildingType)) {
                    return RESIDENTIAL
                }
                return COMMERCIAL
            }
        }
    }

    enum class RecordStatusType(val code: String) {
        REFERRAL_FORM_OUTPUT("01"),            // 斡旋申込書出力
        REFERRAL_FORM_APPROVAL("02"),          // 斡旋申込書承認
        REFERRAL_FORM_COLLECTION("03"),        // 斡旋申込書回収
        ASSIGNED_TENANT_CONTRACT_NUM("10"),    // テ契採番済
        FORM_REGISTERED("20"),                 // 申込登録済
        DEPOSIT_CHANGED("30"),                 // 手付変更済
        REMAINING_COLLECTION_CONFIRMATION_INPUT("40"), // 残集確定入力済
        REMAINING_COLLECTION_INPUT("50"),      // 残集入力済
        OCCUPIED("60"),                        // 入居中
        HANDOVER("65"),                        // 明渡
        MOVED_OUT("70"),                       // 退去済
        CANCEL_REQUEST("80"),                  // キャンセル申請
        CANCEL_APPROVAL("85"),                 // キャンセル承認
        DELETED("90"),                         // 削除
        NONE("--")                             // 未設定
    }

    /** 物件の掲載状態 */
    enum class UpState {
        /** 準備中 */
        PREPARING,

        /** 募集中 */
        RECRUITING,

        /** 仮押さえ中 */
        TEMPORARY_RESERVED,

        /** 入居済み */
        ALREADY_MOVED_IN,
        ;
    }

    fun getType(): Type {
        // 事業用は、建物種別未設定の場合はあるため、null の場合は事業用と判断する
        return buildingType?.let { Type.of(buildingType) } ?: Type.COMMERCIAL
    }

    fun isTemporaryReservationAllowed(): Boolean {
        return !listOf(
            RecordStatusType.FORM_REGISTERED,
            RecordStatusType.DEPOSIT_CHANGED,
            RecordStatusType.REMAINING_COLLECTION_CONFIRMATION_INPUT,
            RecordStatusType.REMAINING_COLLECTION_INPUT,
            RecordStatusType.OCCUPIED,
            RecordStatusType.DELETED,
        ).contains(this.recordStatusType)
    }

    fun isTemporaryReserved(temporaryReservation: TemporaryReservation?): Boolean {
        return moveInApplicationDate == null && !listOf(
            RecordStatusType.FORM_REGISTERED,                 // 申込登録済
            RecordStatusType.DEPOSIT_CHANGED,                 // 手付変更済
            RecordStatusType.REMAINING_COLLECTION_CONFIRMATION_INPUT, // 残集確定入力済
            RecordStatusType.REMAINING_COLLECTION_INPUT,      // 残集入力済
        ).contains(this.recordStatusType) && temporaryReservation != null && temporaryReservation !is CancelledTemporaryReservationInfo
    }

    fun isTemporaryReserved(): Boolean {
        return applicationScheduledDate != null && moveInApplicationDate == null
    }

    fun isApplicationSubmitted(temporaryReservation: TemporaryReservation?): Boolean {
        return listOf(
            RecordStatusType.FORM_REGISTERED,                 // 申込登録済
            RecordStatusType.DEPOSIT_CHANGED,                 // 手付変更済
            RecordStatusType.REMAINING_COLLECTION_CONFIRMATION_INPUT, // 残集確定入力済
            RecordStatusType.REMAINING_COLLECTION_INPUT,      // 残集入力済
        ).contains(this.recordStatusType) || (moveInApplicationDate != null && temporaryReservation != null && temporaryReservation !is CancelledTemporaryReservationInfo)
    }

    fun isApplicationSubmitted(): Boolean {
        return listOf(
            RecordStatusType.FORM_REGISTERED,                 // 申込登録済
            RecordStatusType.DEPOSIT_CHANGED,                 // 手付変更済
            RecordStatusType.REMAINING_COLLECTION_CONFIRMATION_INPUT, // 残集確定入力済
            RecordStatusType.REMAINING_COLLECTION_INPUT,      // 残集入力済
        ).contains(this.recordStatusType) || (moveInApplicationDate != null && applicationScheduledDate != null)
    }
}

