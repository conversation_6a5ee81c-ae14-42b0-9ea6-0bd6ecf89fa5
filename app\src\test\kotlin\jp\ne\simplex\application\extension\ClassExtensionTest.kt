package jp.ne.simplex.application.extension

import com.fasterxml.jackson.annotation.JsonProperty
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.shouldBe
import jp.ne.simplex.shared.ClassExtension.Companion.getJsonPropertyNames
import java.time.LocalDate

class ClassExtensionTest : FunSpec({

    context("クラスに定義されているフィールドが入れ子になっていない場合、プロパティ値が全て取得できること") {

        class Sample private constructor(
            @field:JsonProperty("property1")
            private val property1: String,
            @field:JsonProperty("property2")
            private val property2: Boolean,
            @field:JsonProperty("property3")
            private val property3: Int,
            @field:JsonProperty("property4")
            private val property4: LocalDate,
        )

        test("定義されているプロパティの数が正しいこと") {
            Sample::class.java.getJsonPropertyNames().size.shouldBe(4)
        }

        test("定義されているプロパティの値が正しいこと") {
            val properties = Sample::class.java.getJsonPropertyNames()
            properties.shouldContain("property1")
            properties.shouldContain("property2")
            properties.shouldContain("property3")
            properties.shouldContain("property4")
        }
    }

    context("入れ子フィールドが List 型である場合、ルートオブジェクトのプロパティ値および入れ子の値全て取得できること") {

        class Sample private constructor(
            @field:JsonProperty("list")
            private val list: List<NestedSample>,
        ) {
            inner class NestedSample(
                @field:JsonProperty("property1")
                private val property1: String,
                @field:JsonProperty("property2")
                private val property2: Boolean,
                @field:JsonProperty("property3")
                private val property3: Int,
                @field:JsonProperty("property4")
                private val property4: LocalDate,
            )
        }

        test("定義されているプロパティの数が正しいこと") {
            Sample::class.java.getJsonPropertyNames().size.shouldBe(5)
        }

        test("定義されているプロパティの値が正しいこと") {
            val properties = Sample::class.java.getJsonPropertyNames()
            properties.shouldContain("list")
            properties.shouldContain("property1")
            properties.shouldContain("property2")
            properties.shouldContain("property3")
            properties.shouldContain("property4")
        }
    }
    // NOTE ユースケースが出てきた場合にClassExtension#getJsonPropertyNamesと併せて修正する
    context("入れ子フィールドが List 型でない場合、ルートオブジェクトのプロパティ値のみ取得できること") {

        class Sample private constructor(
            @field:JsonProperty("object")
            private val nestedObject: NestedSample,
        ) {
            inner class NestedSample(
                @field:JsonProperty("property1")
                private val property1: String,
                @field:JsonProperty("property2")
                private val property2: Boolean,
                @field:JsonProperty("property3")
                private val property3: Int,
                @field:JsonProperty("property4")
                private val property4: LocalDate,
            )
        }

        test("定義されているルートオブジェクトのプロパティの数が正しいこと") {
            Sample::class.java.getJsonPropertyNames().size.shouldBe(1)
        }

        test("定義されているルートオブジェクトのプロパティの値が正しいこと") {
            Sample::class.java.getJsonPropertyNames().first().shouldBe("object")
        }
    }
})
