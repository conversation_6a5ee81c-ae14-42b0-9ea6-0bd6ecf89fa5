name: Deploy ECR PRD
run-name: 'deploy-ecr-prd'

on:
  push:
    tags:
      - 'v*-ecr'
      - 'v*-all'

permissions:
  contents: read
  id-token: write
  actions: read

jobs:
  docker_pull_and_push:
    runs-on: ubuntu-latest
    environment: prd-deploy
    timeout-minutes: 60
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/arm64

      - name: Copy buildkitd.toml
        run: |
          sudo mkdir -p /etc/buildkit
          sudo cp "$GITHUB_WORKSPACE/.github/etc/buildkit/buildkitd.toml" /etc/buildkit/buildkitd.toml
        shell: bash

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-config: /etc/buildkit/buildkitd.toml
          driver-opts: |
            image=moby/buildkit:buildx-stable-1

      - name: App Dock<PERSON> Pull and Push
        uses: ./.github/actions/pull-and-push-ecr
        with:
          prd_account_id: ${{ vars.ACCOUNT_ID }}
          stg_account_id: ${{ vars.ACCOUNT_ID_STG }}
          prd_deploy_env: ${{ vars.DEPLOY_ENV }}
          stg_deploy_env: ${{ vars.PULL_ENV }}
          prd_ecr_repository: ${{ vars.ECR_URI }}
          stg_ecr_repository: ${{ vars.PULL_ECR_URI }}
          use_arm64: true
          ssm_parameter_name: ${{ vars.PRD_APP_IMAGE_TAG_PARAMETER_NAME }}
          stg_ssm_parameter_name: ${{ vars.STG_APP_IMAGE_TAG_PARAMETER_NAME }}

      - name: Batch Docker Pull and Push
        uses: ./.github/actions/pull-and-push-ecr
        with:
          prd_account_id: ${{ vars.ACCOUNT_ID }}
          stg_account_id: ${{ vars.ACCOUNT_ID_STG }}
          prd_deploy_env: ${{ vars.DEPLOY_ENV }}
          stg_deploy_env: ${{ vars.PULL_ENV }}
          prd_ecr_repository: ${{ vars.ECR_URI }}
          stg_ecr_repository: ${{ vars.PULL_ECR_URI }}
          use_arm64: true
          ssm_parameter_name: ${{ vars.PRD_BATCH_IMAGE_TAG_PARAMETER_NAME }}
          stg_ssm_parameter_name: ${{ vars.STG_BATCH_IMAGE_TAG_PARAMETER_NAME }}