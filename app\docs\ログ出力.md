# ログ出力

## 導入

SpringBootでは、デフォルトでlogbackが使用できるため、ログ出力をするだけの場合、ライブラリの追加は特別必要ない。

ただし、今回、当該アプリケーションを動かす環境におけるログ集計をAWSのCloudWatchで行うため、ログをJSON形式で出力できるように、`net.logstash.logback:logstash-logback-encoder`
を追加している

※ JSON形式で出力しないと、StackTrace等（改行が含まれるログ）が出力された場合に、ログが複数行にまたがり、視認性及び検索性が著しく低下するため

---

## 設定

`src/main/resources/application-xxx.yml`で、`app.log.appender` にて、以下の2つの内いずれかを設定できる

- `console-json`
    - 以下のように、全てのログをJSON形式で出力する設定（AWS環境上で動かす際に使用）
    - ![log_json.png](images/log/log_json.png)
- `console-text`
    - 以下のように、全てのログをテキスト形式で出力する設定（ローカル開発時に使用）
    - ![img.png](images/log/log_text.png)

上記それぞれのログのフォーマット設定は `src/main/resources/logback-spring.xml` にて行っている

---

## ログ内容のマスク処理

認証情報や個人情報などをAPIでやり取りする場合など、そのままログ出力してしまう情報漏洩のリスクがある。
そのため、そのような情報はマスクしてログ出力するように処理する必要がある。

### マスク処理をログ出力処理に注入する方法

- ログフォーマット設定などを行っている `src/main/resources/logback-spring.xml` にて行っている （以下例）
- 以下の `<valueMasker class="jp.ne.simplex.log.LogMaskingConfig"/>`
  部分で、ログのマスク処理を行うクラスファイルを指定することで、ログ出力の際に当該クラスファイルの処理が呼ばれる

```xml

<appender class="ch.qos.logback.core.ConsoleAppender" name="console-json">
  <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
    <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
      <valueMasker class="jp.ne.simplex.log.LogMaskingConfig"/>
    </jsonGeneratorDecorator>
    <providers>
      <pattern>
        <omitEmptyFields>true</omitEmptyFields>
        <pattern>
          {
          "timestamp": "%date{yyyy/MM/dd HH:mm:ss.SSS}",
          "level": "[%level]",
          "thread": "%thread",
          "transaction_id": "%mdc{transaction-id}",
          "logger": "%logger",
          "status": "%mdc{status}",
          "method": "%mdc{method}",
          "uri": "%mdc{uri}",
          "message": "%message",
          "latency": "%mdc{latency}",
          "stack_trace": "%ex{full}"
          }
        </pattern>
      </pattern>
    </providers>
  </encoder>
</appender>
```

### マスク対象のフィールドを追加する

以下のように、対象のクラスに `@MaskTarget` を、そのクラスの内のプロパティに `@MaskValue`
を付与することで、ログ出力時にマスクされるようになる

```kotlin
@MaskTarget
data class ClientLoginRequest(
    @JsonProperty("employeeId")
    val employeeId: String,

    @MaskValue
    @JsonProperty("password")
    val password: String,
)
```

実際に出力されるログは、以下のように、`password` の値がマスクされて出力される

```json
{
  "timestamp": "2025/01/09 19:17:20.671",
  "level": "[INFO]",
  "thread": "http-nio-8082-exec-1",
  "transaction_id": "874625de-52eb-46f0-a162-107e0913bf1d",
  "logger": "jp.ne.simplex.log.ApiLoggingFilter",
  "method": "POST",
  "uri": "/api/auth/login",
  "message": "{\n  \"employeeId\": \"000011\",\n  \"password\": \"******\"\n}"
}
```