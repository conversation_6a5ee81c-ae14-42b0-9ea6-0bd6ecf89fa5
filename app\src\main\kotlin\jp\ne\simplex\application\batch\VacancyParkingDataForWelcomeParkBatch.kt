package jp.ne.simplex.application.batch

import jp.ne.simplex.application.repository.aws.S3RepositoryInterface
import jp.ne.simplex.application.repository.db.BatchExecuteHistoryRepositoryInterface
import jp.ne.simplex.application.repository.file.ParkingFileRepositoryInterface
import jp.ne.simplex.application.repository.ftp.FtpRepositoryInterface
import jp.ne.simplex.application.service.ParkingDetailsService
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.time.LocalDateTime

/** WelcomePark向け空き駐車場バッチ */
@Profile("batch")
@Component("VacancyParkingDataForWelcomeParkBatch")
class VacancyParkingDataForWelcomeParkBatch(
    private val parkingDetailsService: ParkingDetailsService,
    private val parkingFileRepository: ParkingFileRepositoryInterface,
    private val s3Repository: S3RepositoryInterface,
    private val ftpRepository: FtpRepositoryInterface,
    @Value("\${batch.s3.vacancy-parking-batch-bucket}")
    private val bucketName: String,
    override val batchExecuteHistoryRepository: BatchExecuteHistoryRepositoryInterface,
) : BatchInterface {
    override val batchType: BatchType = BatchType.VACANCY_PARKING_DATA_FOR_WELCOME_PARK

    override fun executeInternal(executeOption: ExecuteOptionType?) {
        if (executeOption == ExecuteOptionType.RERUN_ONLY_SEND_FILE) {
            executeOnlySendFile()
            return
        }
        val currentDatetime = LocalDateTime.now()
        val results = parkingDetailsService.getAllVacancyParkingTarget()
        val localFilePath =
            parkingFileRepository.saveVacancyParkingDataForWelcomePark(results, currentDatetime)

        s3Repository.uploadFile(
            localFilePath,
            localFilePath.fileName.toString(),
            bucketName
        )
        ftpRepository.sendFileToWelcomePark(
            localFilePath,
            localFilePath.fileName.toString()
        )
    }

    override fun isAcceptableOption(executeOption: ExecuteOptionType): Boolean {
        return executeOption == ExecuteOptionType.RERUN_ONLY_SEND_FILE || executeOption == ExecuteOptionType.RERUN
    }

    private fun executeOnlySendFile() {
        val latestCsvContent = s3Repository.getLatestFile(bucketName)
        val localFilePath = parkingFileRepository.saveFromS3ForWelcomePark(
            latestCsvContent,
        )
        ftpRepository.sendFileToWelcomePark(
            localFilePath,
            localFilePath.fileName.toString()
        )
    }
}
