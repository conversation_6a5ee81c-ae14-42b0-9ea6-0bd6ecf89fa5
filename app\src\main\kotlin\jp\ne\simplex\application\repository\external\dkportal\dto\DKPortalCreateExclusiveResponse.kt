package jp.ne.simplex.application.repository.external.dkportal.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalResponse

class DKPortalCreateExclusiveListResponse(
    val list: List<DKPortalCreateExclusiveResponse>,
) : DKPortalResponse {
    class DKPortalCreateExclusiveResponse(
        @field:JsonProperty("id")
        val id: String,
        @field:JsonProperty("kentaku_building_code")
        val kentakuBuildingCode: String,
        @field:JsonProperty("kentaku_room_code")
        val kentakuRoomCode: String,
        @field:JsonProperty("exclusive_from")
        val exclusiveFrom: String,
        @field:JsonProperty("exclusive_to")
        val exclusiveTo: String,
        @field:JsonProperty("company_type")
        val companyType: Int,
        @field:JsonProperty("e_code")
        val eCode: String?
    ) : DKPortalResponse
}

