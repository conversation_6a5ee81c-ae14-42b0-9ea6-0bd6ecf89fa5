import { DKPortalValidationError } from '../../shared/dkportal/dkportal_response';
import { deserializeGraphqlBody, getOperationName } from '../../shared/shared_function';

/**
 * DKポータル/駐車場情報更新API
 *
 * サンプルリクエスト
 * curl -i -X POST -H "Content-Type: application/json" http://localhost:8083/dkportal -d '{"query": "mutation { updateParkingLot(order_code: \"0191008\", available_parking_flag: 1, second_parking_contract_possible_flag: 1) { status } }"}'
 *
 */

const targetOperation = 'updateParkingLot';

export default [
  {
    id: 'dkportal_update_parking_lot',
    url: '/dkportal',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: async (req, res, next) => {
            const query = req.body['query'];
            // 対象の関数ではない場合は処理を終了
            const operation = getOperationName(query);
            if (targetOperation !== operation) {
              next();
              return;
            }

            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const result = await checkParameter(query);

            if (result) {
              res.status(400);
              res.send({
                errors: [result],
                data: { [targetOperation]: null },
              });
              return;
            }

            res.status(200);
            res.send({
              data: { [targetOperation]: { status: true } },
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            res.status(500);
            res.send({
              error_message: '予期せぬエラーが発生しました。',
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する。適切な場合はtrueを返す。
 * @param {*} req
 * @returns {Boolean}
 */
async function checkParameter(query): Promise<DKPortalValidationError> {
  var deserializedReq = deserializeGraphqlBody(query);

  // 必須チェック
  if (deserializedReq['order_code'] === undefined) {
    return new DKPortalValidationError(
      targetOperation,
      'order_code',
      '受注コードは必須です。',
    );
  }
  if (deserializedReq['available_parking_flag'] === undefined) {
    return new DKPortalValidationError(
      targetOperation,
      'available_parking_flag',
      '駐車場有無は必須です。',
    );
  }
  if (deserializedReq['second_parking_contract_possible_flag'] === undefined) {
    return new DKPortalValidationError(
      targetOperation,
      'second_parking_contract_possible_flag',
      '2台目可否は必須です。',
    );
  }

  const orderCode = deserializedReq['order_code']['value'];
  const availableParkingFlag = deserializedReq['available_parking_flag']['value'];
  const secondParkingContractPossibleFlag =
    deserializedReq['second_parking_contract_possible_flag']['value'];

  // 値チェック
  if (orderCode.length !== 7) {
    return new DKPortalValidationError(
      targetOperation,
      'order_code',
      '受注コードは7桁で指定してください。',
    );
  }
  if (availableParkingFlag != 0 && availableParkingFlag != 1) {
    return new DKPortalValidationError(
      targetOperation,
      'available_parking_flag',
      '駐車場有無は0か1で指定してください。',
    );
  }
  if (secondParkingContractPossibleFlag != 0 && secondParkingContractPossibleFlag != 1) {
    return new DKPortalValidationError(
      targetOperation,
      'second_parking_contract_possible_flag',
      '2台目可否は0か1で指定してください。',
    );
  }

  return undefined;
}
