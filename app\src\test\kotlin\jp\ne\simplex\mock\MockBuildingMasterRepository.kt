package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.repository.db.BuildingMasterRepositoryInterface

class MockBuildingMasterRepository(
    val findActiveByFunc: (buildingCode: Building.Code) -> Building? = { _ -> null },
    val findByFunc: (buildingCode: Building.Code) -> Building? = { _ -> null },
    val isExistFunc: (buildingCode: Building.Code) -> Boolean = { _ -> false },
) : BuildingMasterRepositoryInterface {
    override fun findActiveBy(buildingCode: Building.Code): Building? {
        return findActiveByFunc(buildingCode)
    }

    override fun findBy(buildingCode: Building.Code): Building? {
        return findByFunc(buildingCode)
    }

    override fun isExist(buildingCode: Building.Code): Boolean {
        return isExistFunc(buildingCode)
    }
}
