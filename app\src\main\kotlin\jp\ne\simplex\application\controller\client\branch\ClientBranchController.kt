package jp.ne.simplex.application.controller.client.branch

import jp.ne.simplex.application.controller.client.branch.dto.ClientBranchListResponse
import jp.ne.simplex.application.service.BranchService
import jp.ne.simplex.application.service.EmployeeService
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/branch")
class ClientBranchController(
    private val service: BranchService,
    private val employeeService: EmployeeService
) {

    @GetMapping()
    @ApiDefinition(summary = "支店一覧取得API")
    fun getBranchList(@AuthenticationPrincipal authInfo: AuthInfo): ClientBranchListResponse {
        return ClientBranchListResponse.of(service.getBranchList(),
            employeeService.getAffiliationBranchCode(authInfo.getEmployeeCode())
        )
    }
}
