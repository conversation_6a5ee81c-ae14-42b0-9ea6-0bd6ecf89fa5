package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.*
import jp.ne.simplex.application.model.RegisterTemporaryReservation.*
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.getBranchCode
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.getCancelledTemporaryReservationInfo
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.getEmployeeCode
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.getOtherCompanyTemporaryReservationInfo
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.getOwnCompanyTemporaryReservationInfo
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.getVersion
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.isAlreadyCancelled
import jp.ne.simplex.application.repository.db.extension.TemporaryReservationFileEx.Companion.isRegisteredByOtherCompany
import jp.ne.simplex.db.jooq.gen.tables.pojos.TemporaryReservationFilePojo
import jp.ne.simplex.db.jooq.gen.tables.references.TEMPORARY_RESERVATION_FILE
import jp.ne.simplex.exception.DBValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.*
import org.springframework.dao.CannotAcquireLockException
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class TemporaryReservationRepository(
    private val context: DSLContext,
    private val branchRepository: BranchRepositoryInterface,
    private val employeeRepository: EmployeeRepositoryInterface
) : TemporaryReservationRepositoryInterface {

    override fun findBy(id: Property.Id): TemporaryReservationInfo? {
        return context.select()
            .from(TEMPORARY_RESERVATION_FILE)
            .where(id)
            .fetchOneInto(TemporaryReservationFilePojo::class.java)
            ?.getTemporaryReservationInfo()
    }

    override fun forceRegister(config: Configuration, rtr: ForceRegisterTemporaryReservation) {
        val id = rtr.getId()

        return when (lockRecordWait(config, id)) {
            null -> {
                config.dsl()
                    .insertInto(TEMPORARY_RESERVATION_FILE)
                    .set(rtr.toFieldValueMap())
                    .execute()
                    .let { config.checkDuplicated(id) }
            }

            else -> {
                config.dsl()
                    .update(TEMPORARY_RESERVATION_FILE)
                    .set(rtr.toFieldValueMap())
                    .where(id)
                    .execute().let {}
            }
        }
    }

    override fun forceCancel(config: Configuration, ctr: ForceCancelTemporaryReservation) {
        val id = ctr.getId()

        return when (lockRecordWait(config, id)) {
            null -> config.dsl()
                .insertInto(TEMPORARY_RESERVATION_FILE)
                .set(ctr.toFieldValueMap())
                .execute()
                .let { config.checkDuplicated(id) }

            else -> config.dsl()
                .update(TEMPORARY_RESERVATION_FILE)
                .set(ctr.toFieldValueMap())
                .where(id)
                .execute()
                .let {}
        }
    }

    override fun register(config: Configuration, rtr: RegisterTemporaryReservation) {
        val id = rtr.getId()

        return when (rtr) {
            is OwnCompanyRegisterTemporaryReservation -> {
                when (val record = lockRecordNoWait(config, id)) {
                    null -> {
                        config.dsl()
                            .insertInto(TEMPORARY_RESERVATION_FILE)
                            .set(rtr.toFieldValueMap())
                            .execute()
                            .let { config.checkDuplicated(id) }
                    }

                    else -> {
                        if (record.getVersion() != rtr.version) {
                            throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format())
                        }

                        config.dsl()
                            .update(TEMPORARY_RESERVATION_FILE)
                            .set(rtr.toFieldValueMap())
                            .where(id)
                            .execute()
                            .let {}
                    }
                }
            }

            is OtherCompanyRegisterTemporaryReservation -> {
                when (val record = lockRecordNoWait(config, id)) {
                    null -> {
                        // 他社仮押さえの新規登録は、システム上あり得ないので、この分岐に入ることはない
                    }

                    else -> {
                        if (record.getVersion() != rtr.version) {
                            throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format())
                        }

                        val currentDateTime = LocalDateTime.now()

                        // @formatter:off
                        // 他社による仮押さえは、コメントのみ更新可能なため、他の他社情報を絞り込み条件に追加する
                        config.dsl()
                            .update(TEMPORARY_RESERVATION_FILE)
                            .set(TEMPORARY_RESERVATION_FILE.COMMENT, rtr.comment.value) // E03P10
                            .set(TEMPORARY_RESERVATION_FILE.REGISTRATION_DATE, currentDateTime.yyyyMMdd()) // E03P14
                            .set(TEMPORARY_RESERVATION_FILE.LINK_CD_REGISTRATION_TIME, currentDateTime.HHmmss()) // E03P15
                            .where(id)
                            .and(TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE.eq(rtr.scheduledMoveInDate.yyyyMMdd()))
                            .and(TEMPORARY_RESERVATION_FILE.CONTRACT_FORM_E_CODE.eq(rtr.otherCompanyInfo.companyCode))
                            .and(TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_NAME.eq(rtr.otherCompanyInfo.companyName))
                            .and(TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_STORE_NAME.eq(rtr.otherCompanyInfo.storeName))
                            .and(TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_REP_NAME.eq(rtr.otherCompanyInfo.staffName))
                            .execute()
                            .let {}
                        // @formatter:on
                    }
                }
            }
        }
    }

    // @formatter:off
    override fun cancel(config: Configuration, ctr: CancelTemporaryReservation): TemporaryReservationInfo? {
        // @formatter:on
        val id = ctr.getId()

        return when (val record = lockRecordNoWait(config, id)) {
            null -> {
                return config.dsl()
                    .insertInto(TEMPORARY_RESERVATION_FILE)
                    .set(ctr.toFieldValueMap())
                    .execute()
                    .let { config.checkDuplicated(id) }
                    .let { null }
            }

            else -> {
                if (record.getVersion() != ctr.version) {
                    throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format())
                }
                config.dsl()
                    .update(TEMPORARY_RESERVATION_FILE)
                    .set(ctr.toFieldValueMap())
                    .where(id)
                    .execute()
                    .let { record.getTemporaryReservationInfo() }
            }
        }
    }

    override fun updateComment(config: Configuration, utrc: UpdateTemporaryReservationComment) {
        val id = utrc.getId()

        return when (findBy(id)) {
            null -> {
                config.dsl()
                    .insertInto(TEMPORARY_RESERVATION_FILE)
                    .set(TEMPORARY_RESERVATION_FILE.BUILDING_CD, id.buildingCode.value) // E03P01
                    .set(TEMPORARY_RESERVATION_FILE.ROOM_CD, id.roomCode.value) // E03P02
                    .set(TEMPORARY_RESERVATION_FILE.COMMENT, utrc.comment.value) // E03P10
                    .execute()
                    .let {}
            }

            else -> {
                config.dsl()
                    .update(TEMPORARY_RESERVATION_FILE)
                    .set(TEMPORARY_RESERVATION_FILE.COMMENT, utrc.comment.value) // E03P10
                    .where(id)
                    .execute()
                    .let {}
            }
        }
    }

    // 仮押さえテーブルの1レコードを、TemporaryReservationInfo クラスにマッピングする
    private fun TemporaryReservationFilePojo.getTemporaryReservationInfo(): TemporaryReservationInfo? {
        // すでにキャンセルされている場合は、仮押さえ情報は取得できない
        if (this.isAlreadyCancelled()) {
            return this.getCancelledTemporaryReservationInfo()
        }

        // 他社によって登録/更新された仮押さえ情報
        if (this.isRegisteredByOtherCompany()) {
            return this.getOtherCompanyTemporaryReservationInfo()
        }

        // 自社によって登録/更新された仮押さえ情報
        return this.getOwnCompanyTemporaryReservationInfo(
            // 基本はあり得ないが、branchRepository.getKtBranch で支店情報を取得できない場合でも、
            // 後続のいい物件ボードへのAPIをコールするため、永続化されているレコードの情報を付与したダミーのインスタンスを作成する
            branch = branchRepository.getKtBranch(getBranchCode())
                ?: Branch.dummy(code = customerRepBranchCd),

            // 基本はあり得ないが、employeeRepository.findBy で従業員情報を取得できない場合でも、
            // 後続のいい物件ボードへのAPIをコールするため、永続化されているレコードの情報を付与したダミーのインスタンスを作成する
            employee = employeeRepository.findBy(getEmployeeCode())
                ?: Employee.dummy(code = applicationScheduledPersonCd)
        )
    }

    private fun ForceRegisterTemporaryReservation.toFieldValueMap(): Map<Field<*>, Any?> {
        val currentDateTime = LocalDateTime.now()

        return mapOf(
            TEMPORARY_RESERVATION_FILE.BUILDING_CD to getId().buildingCode.value,// E03P01
            TEMPORARY_RESERVATION_FILE.ROOM_CD to getId().roomCode.value,  // E03P02
            TEMPORARY_RESERVATION_FILE.STATUS to "1", // E03P03 常に「1」固定
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE to scheduledMoveInDate.yyyyMMdd(), // E03P04
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_PERSON_CD to assignedEmployeeCode?.value, // E03P06
            TEMPORARY_RESERVATION_FILE.CUSTOMER_REP_BRANCH_CD to assignedBranchCode?.getPrefix(), // E03P08
            TEMPORARY_RESERVATION_FILE.COMMENT to comment.value, // E03P10
            TEMPORARY_RESERVATION_FILE.REGISTRATION_DATE to currentDateTime.yyyyMMdd(), // E03P14
            TEMPORARY_RESERVATION_FILE.LINK_CD_REGISTRATION_TIME to currentDateTime.HHmmss(), // E03P15
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_FLAG to if (otherCompanyInfo != null) "1" else null, // E03P16
            TEMPORARY_RESERVATION_FILE.CONTRACT_FORM_E_CODE to otherCompanyInfo?.companyCode, // E03P11
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_NAME to otherCompanyInfo?.companyName, // E03P18
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_STORE_NAME to otherCompanyInfo?.storeName, // E03P19
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_REP_NAME to otherCompanyInfo?.staffName, // E03P20
        )
    }

    private fun ForceCancelTemporaryReservation.toFieldValueMap(): Map<Field<*>, Any?> {
        return mapOf(
            TEMPORARY_RESERVATION_FILE.BUILDING_CD to getId().buildingCode.value,// E03P01
            TEMPORARY_RESERVATION_FILE.ROOM_CD to getId().roomCode.value,  // E03P02
            TEMPORARY_RESERVATION_FILE.STATUS to "1", // E03P03 常に「1」固定
            TEMPORARY_RESERVATION_FILE.COMMENT to comment.value, // E03P10
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE to null, // E03P04
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_PERSON_CD to null, // E03P06
            TEMPORARY_RESERVATION_FILE.CUSTOMER_REP_BRANCH_CD to null, // E03P08
            TEMPORARY_RESERVATION_FILE.CONTRACT_FORM_E_CODE to null, // E03P11
            TEMPORARY_RESERVATION_FILE.REGISTRATION_DATE to null, // E03P14
            TEMPORARY_RESERVATION_FILE.LINK_CD_REGISTRATION_TIME to null, // E03P15
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_FLAG to null, // E03P16
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_MEMBER_ID to null, // E03P17
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_NAME to null, // E03P18
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_STORE_NAME to null, // E03P19
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_REP_NAME to null, // E03P20
        )
    }

    private fun OwnCompanyRegisterTemporaryReservation.toFieldValueMap(): Map<Field<*>, Any?> {
        val currentDateTime = LocalDateTime.now()

        return mapOf(
            TEMPORARY_RESERVATION_FILE.BUILDING_CD to getId().buildingCode.value,// E03P01
            TEMPORARY_RESERVATION_FILE.ROOM_CD to getId().roomCode.value,  // E03P02
            TEMPORARY_RESERVATION_FILE.STATUS to "1", // E03P03 常に「1」固定
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE to scheduledMoveInDate.yyyyMMdd(), // E03P04
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_PERSON_CD to assignedEmployee.code.value, // E03P06
            TEMPORARY_RESERVATION_FILE.CUSTOMER_REP_BRANCH_CD to assignedBranch.code.getPrefix(), // E03P08
            TEMPORARY_RESERVATION_FILE.COMMENT to comment.value, // E03P10
            TEMPORARY_RESERVATION_FILE.REGISTRATION_DATE to currentDateTime.yyyyMMdd(), // E03P14
            TEMPORARY_RESERVATION_FILE.LINK_CD_REGISTRATION_TIME to currentDateTime.HHmmss(), // E03P15
        )
    }

    private fun CancelTemporaryReservation.toFieldValueMap(): Map<Field<*>, Any?> {
        return mapOf(
            TEMPORARY_RESERVATION_FILE.BUILDING_CD to getId().buildingCode.value,// E03P01
            TEMPORARY_RESERVATION_FILE.ROOM_CD to getId().roomCode.value,  // E03P02
            TEMPORARY_RESERVATION_FILE.STATUS to "1", // E03P03 常に「1」固定
            TEMPORARY_RESERVATION_FILE.COMMENT to comment.value, // E03P10
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE to null, // E03P04
            TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_PERSON_CD to null, // E03P06
            TEMPORARY_RESERVATION_FILE.CUSTOMER_REP_BRANCH_CD to null, // E03P08
            TEMPORARY_RESERVATION_FILE.CONTRACT_FORM_E_CODE to null, // E03P11
            TEMPORARY_RESERVATION_FILE.REGISTRATION_DATE to null, // E03P14
            TEMPORARY_RESERVATION_FILE.LINK_CD_REGISTRATION_TIME to null, // E03P15
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_FLAG to null, // E03P16
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_MEMBER_ID to null, // E03P17
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_NAME to null, // E03P18
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_STORE_NAME to null, // E03P19
            TEMPORARY_RESERVATION_FILE.OTHER_COMPANY_REP_NAME to null, // E03P20
        )
    }

    private fun SelectWhereStep<*>.where(id: Property.Id): SelectConditionStep<*> {
        return this
            .where(TEMPORARY_RESERVATION_FILE.BUILDING_CD.eq(id.buildingCode.value)) // E03P01
            .and(TEMPORARY_RESERVATION_FILE.ROOM_CD.eq(id.roomCode.value)) // E03P02
    }

    private fun UpdateWhereStep<*>.where(id: Property.Id): UpdateConditionStep<*> {
        return this
            .where(TEMPORARY_RESERVATION_FILE.BUILDING_CD.eq(id.buildingCode.value)) // E03P01
            .and(TEMPORARY_RESERVATION_FILE.ROOM_CD.eq(id.roomCode.value)) // E03P02
    }

    /**
     * 引数で指定された値で、更新ロックを取得する（更新ロック取得失敗時は即時でExceptionをスローする）
     *
     *  ロックに成功した場合：引数で指定された値に合致するレコードがあればそれを返す
     *  ロックに失敗した場合：DBValidationException をスローする
     */
    @Throws(DBValidationException::class)
    fun lockRecordNoWait(config: Configuration, id: Property.Id): TemporaryReservationFilePojo? {
        try {
            return config.dsl().selectFrom(TEMPORARY_RESERVATION_FILE)
                .where(TEMPORARY_RESERVATION_FILE.BUILDING_CD.eq(id.buildingCode.value)) // E03P01
                .and(TEMPORARY_RESERVATION_FILE.ROOM_CD.eq(id.roomCode.value)) // E03P02
                .forUpdate()
                .noWait()
                .fetchOneInto(TemporaryReservationFilePojo::class.java)
        } catch (ex: CannotAcquireLockException) {
            throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format(), ex)
        }
    }

    /**
     * 引数で指定された値で、更新ロックを取得する（更新ロック取得失敗時は10秒待機する）
     *
     *  ロックに成功した場合：引数で指定された値に合致するレコードがあればそれを返す
     *  ロックに失敗した場合：DBValidationException をスローする
     */
    @Throws(DBValidationException::class)
    fun lockRecordWait(config: Configuration, id: Property.Id): TemporaryReservationFilePojo? {
        try {
            return config.dsl().selectFrom(TEMPORARY_RESERVATION_FILE)
                .where(TEMPORARY_RESERVATION_FILE.BUILDING_CD.eq(id.buildingCode.value)) // E03P01
                .and(TEMPORARY_RESERVATION_FILE.ROOM_CD.eq(id.roomCode.value)) // E03P02
                .forUpdate()
                .wait(10)
                .fetchOneInto(TemporaryReservationFilePojo::class.java)
        } catch (ex: CannotAcquireLockException) {
            throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format(), ex)
        }
    }

    /**
     * 引数で指定された値で、データ追加処理の競合が発生していないか確認する
     *
     * Legacyのテーブルに主キー制約がないため、Insertの場合、レコードロックや主キー制約違反などで競合を検知できない
     * そのため、Insert後に、事実上の主キーの役割を果たしている「BUILDING_CD」と「ROOM_CD」で検索し 1 より大きくないことを確認する
     */
    @Throws(DBValidationException::class)
    private fun Configuration.checkDuplicated(id: Property.Id) {
        val updatedCount = dsl().selectFrom(TEMPORARY_RESERVATION_FILE)
            .where(TEMPORARY_RESERVATION_FILE.BUILDING_CD.eq(id.buildingCode.value)) // E03P01
            .and(TEMPORARY_RESERVATION_FILE.ROOM_CD.eq(id.roomCode.value)) // E03P02
            .count()
        if (updatedCount > 1) {
            throw DBValidationException(ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format())
        }
    }
}

interface TemporaryReservationRepositoryInterface {
    /** 物件に紐づく仮押さえ情報を取得する */
    fun findBy(id: Property.Id): TemporaryReservationInfo?

    /** 仮押さえ強制登録（いい物件ボードからの情報連携時のみに使用する） */
    fun forceRegister(config: Configuration, rtr: ForceRegisterTemporaryReservation)

    /** 仮押さえ強制解除（いい物件ボードからの情報連携時のみに使用する） */
    fun forceCancel(config: Configuration, ctr: ForceCancelTemporaryReservation)

    /** 仮押さえ登録 */
    fun register(config: Configuration, rtr: RegisterTemporaryReservation)

    /** 仮押さえ解除（解除対象の仮押さえ情報を返す） */
    fun cancel(config: Configuration, ctr: CancelTemporaryReservation): TemporaryReservationInfo?

    /** 仮押さえコメント更新 */
    fun updateComment(config: Configuration, utrc: UpdateTemporaryReservationComment)
}
