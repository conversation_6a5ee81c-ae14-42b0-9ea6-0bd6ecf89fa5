package jp.ne.simplex.arch_unit

import com.tngtech.archunit.core.domain.JavaClass
import com.tngtech.archunit.core.domain.JavaMethod
import com.tngtech.archunit.core.domain.JavaMethodCall
import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.lang.ArchCondition
import com.tngtech.archunit.lang.ConditionEvents
import com.tngtech.archunit.lang.SimpleConditionEvent
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.lang.reflect.Modifier
import java.nio.file.Paths

// 下記の記事のコードを参照
// https://zenn.dev/loglass/articles/archunit-kotlin-copy-method
class MethodsTest {
    @Nested
    @DisplayName("data classのメソッドルールに関するシナリオ")
    inner class Scenario1 {
        @Test
        @DisplayName("private constructorを使っている場合、copyメソッドが呼び出されていないこと")
        fun case01() {
            val allApplicationClasses = ClassFileImporter()
                .importPath(Paths.get("build/classes/kotlin/main"))

            methods()
                .should(BanPrivateDataClassCopyCondition)
                .check(allApplicationClasses)
        }
    }

    private object BanPrivateDataClassCopyCondition :
        ArchCondition<JavaMethod>("data classのcopyは、同じクラス内でしかコールすることができない") {
        override fun check(method: JavaMethod, events: ConditionEvents) {
            // Kotlinで自動的に実装されるメソッドにはメタデータの関係上サフィックスに$defaultが付与される
            if (method.name != "copy\$default") {
                return
            }

            val ownerClass = method.owner
            if (!isDataClass(ownerClass) || !hasPrivateConstructor(ownerClass)) {
                return
            }

            method.callsOfSelf.forEach { caller ->

                // this.copyは許容する
                if (method.owner != caller.originOwner) {
                    events.add(
                        SimpleConditionEvent.violated(
                            /* correspondingObject = */ method,
                            /* message = */buildErrorMessage(caller = caller),
                        ),
                    )
                }
            }
        }

        private fun buildErrorMessage(caller: JavaMethodCall): String {
            val callerClassAndMethod =
                "${
                    caller.origin.owner.name.replace(
                        "${caller.origin.owner.packageName}.",
                        ""
                    )
                }.${caller.origin.name}"
            val calledClassAndMethod = "${caller.target.owner.simpleName}.${caller.name}"
            return "$callerClassAndMethod calls $calledClassAndMethod"
        }

        // data classであるかをチェック
        private fun isDataClass(clazz: JavaClass): Boolean {
            // Kotlinのdata classは通常、特定のメソッドを持つため、それを判定条件に利用
            return clazz.methods.any { it.name == "copy\$default" } &&
                    clazz.annotations.any { it.type.name.contains("kotlin.Metadata") }
        }

        // プライベートコンストラクタを持っているかチェック
        private fun hasPrivateConstructor(clazz: JavaClass): Boolean {
            return clazz.constructors.any { constructor ->
                Modifier.isPrivate(constructor.reflect().modifiers)
            }
        }
    }
}
