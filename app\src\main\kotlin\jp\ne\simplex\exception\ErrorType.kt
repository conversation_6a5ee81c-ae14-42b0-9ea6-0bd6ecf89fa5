package jp.ne.simplex.exception

// <!-- @formatter:off -->
enum class ErrorType(val code: String, val message: String) {
    // 予期せぬエラー
    UNEXPECTED_ERROR("MST-00001", "予期せぬエラーが発生しました。"),

    // リクエスト不正
    BAD_REQUEST("MST-00002", "不正なリクエストです。"),

    // ドメインオブジェクト作成時にバリデーションに抵触したエラー（※ 基本的には、APIリクエストが不正な場合にスローされる）
    MODEL_CREATION_FAILED("MST-000006", "モデルの生成に失敗しました。"),

    // 認証エラー
    AUTHENTICATION_FAILURE("MST-01001", "ログインに失敗しました。"),
    ACCESS_TOKEN_EXPIRED("MST-01002", "アクセストークンが有効期限切れです。"),
    REFRESH_TOKEN_EXPIRED("MST-01003", "リフレッシュトークンが有効期限切れです。"),
    UNEXPECTED_AUTHENTICATION_ERROR("MST-01999", "認証情報が不正です。"),
    INVALID_API_KEY("MST-01998", "APIキーが不正です。"),

    // DB関連のエラー
    DB_UPDATE_FAILED("MST-02000", "データベースの更新に失敗しました。"),

    // データ不整合エラー
    DATA_INCONSISTENCY("MST-90000", "データの不整合が発生しました。"),

    // 外部API呼び出し関連のエラー
    EBOARD_API_ERROR("MST-03000", "いい物件ボードのAPIでエラーが発生しました。"),
    DK_PORTAL_API_ERROR("MST-03100", "DKポータルのAPIでエラーが発生しました。"),
}
