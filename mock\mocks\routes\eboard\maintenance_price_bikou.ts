import {
  EboardResponseType,
  eboardResponseMapping,
} from '../../shared/eboard/eboard_response_type';
import { keysToLowerCase } from '../../shared/shared_function';

/**
 * いい物件ボード/物件メンテナンス（金額&備考）API
 *
 * サンプルリクエスト
 *  curl -H "Authorization: Bearer 51480924-5792-46ef-9503-c927b4e596a7" -H "Content-Type:application/x-www-form-urlencoded" -X POST "http://localhost:8083/eboard/newEboardApi/maintenance_price_bikou?tatemonoCd=21421&heyaCd=202&userId=000011&ytn=100000&rkn=100000&skkn=200000&bikou=5432642362"
 */
export default [
  {
    id: 'eboard_maintenance_price_bikou',
    url: '/eboard/newEboardApi/maintenance_price_bikou',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: (req, res) => {
            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const responseType = checkParameter(req);

            if (responseType != EboardResponseType.SUCCESS) {
              res.status(400);
              res.send({
                result: responseType,
                message: eboardResponseMapping[responseType],
              });

              return;
            }

            // status: 200 の場合
            res.status(200);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
              updateResult: 'OK',
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            const responseType = EboardResponseType.OTHER_ERROR;

            res.status(500);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する
 * responseCodeMapping（api_response_specification.js）定義のエラーコードを返却する
 * @param {*} req
 * @returns {EboardResponseType}
 */
function checkParameter(req): EboardResponseType {
  const headers = keysToLowerCase(req.headers);
  if (headers['content-type'] != 'application/x-www-form-urlencoded') {
    return EboardResponseType.PARAMETER_INVALID;
  }

  const { tatemonoCd, heyaCd, userId, ytn, rkn, skkn, bikou } = req.query;

  if (!tatemonoCd || !heyaCd || !userId) {
    return EboardResponseType.PARAMETER_MISSING;
  }
  if (
    ytn === undefined ||
    rkn === undefined ||
    skkn === undefined ||
    bikou === undefined
  ) {
    return EboardResponseType.PARAMETER_MISSING;
  }
  if (ytn === 0 || rkn === 0 || skkn === 0) {
    return EboardResponseType.PARAMETER_INVALID;
  }

  return EboardResponseType.SUCCESS;
}
