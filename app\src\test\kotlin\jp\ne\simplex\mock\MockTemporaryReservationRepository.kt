package jp.ne.simplex.mock

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceCancelTemporaryReservation
import jp.ne.simplex.application.model.ForceUpdateTemporaryReservation.ForceRegisterTemporaryReservation
import jp.ne.simplex.application.repository.db.TemporaryReservationRepositoryInterface
import org.jooq.Configuration

class MockTemporaryReservationRepository(
    val findByFunc: (id: Property.Id) -> TemporaryReservationInfo? = { _ -> null },
    val forceRegisterFunc: (config: Configuration, rtr: ForceRegisterTemporaryReservation) -> Unit = { _, _ -> },
    val forceCancelFunc: (config: Configuration, ctr: ForceCancelTemporaryReservation) -> Unit = { _, _ -> },
    val registerFunc: (config: Configuration, rtr: RegisterTemporaryReservation) -> Unit = { _, _ -> },
    val cancelFunc: (config: Configuration, ctr: CancelTemporaryReservation) -> TemporaryReservationInfo? = { _, _ -> null },
    val updateCommentFunc: (config: Configuration, utrc: UpdateTemporaryReservationComment) -> Unit = { _, _ -> null },
) : TemporaryReservationRepositoryInterface {
    override fun findBy(id: Property.Id): TemporaryReservationInfo? {
        return findByFunc(id)
    }

    override fun forceRegister(config: Configuration, rtr: ForceRegisterTemporaryReservation) {
        return forceRegisterFunc(config, rtr)
    }

    override fun forceCancel(config: Configuration, ctr: ForceCancelTemporaryReservation) {
        return forceCancelFunc(config, ctr)
    }

    override fun register(config: Configuration, rtr: RegisterTemporaryReservation) {
        return registerFunc.invoke(config, rtr)
    }

    override fun cancel(
        config: Configuration,
        ctr: CancelTemporaryReservation
    ): TemporaryReservationInfo? {
        return cancelFunc.invoke(config, ctr)
    }

    override fun updateComment(config: Configuration, utrc: UpdateTemporaryReservationComment) {
        return updateCommentFunc(config, utrc)
    }

}
