package jp.ne.simplex.application.repository.aws

import org.springframework.stereotype.Component
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerException

@Component
class SecretManagerRepository(
    private val client: SecretsManagerClient
) {

    fun getValue(secretName: String): String {
        val request = GetSecretValueRequest.builder().secretId(secretName).build()

        val secrets: GetSecretValueResponse?
        try {
            secrets = client.getSecretValue(request)
        } catch (e: SecretsManagerException) {
            // TODO エラーハンドリング（今は適当にスローしている）
            throw e
        }

        return secrets!!.secretString()
    }
}

