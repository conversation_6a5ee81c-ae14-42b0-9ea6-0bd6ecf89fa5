name: SetUp

runs:
  using: composite
  steps:
    - name: Set up Java
      uses: actions/setup-java@v3
      with:
        distribution: 'corretto'
        java-version: '17'
    - name: Grant permission for gradlew
      run: chmod +x gradlew
      shell: bash
    # propetechリポジトリからDDL等をコピー
    - name: prepare
      run: ./gradlew prepare
      shell: bash
    # コピーしたDDL等を元に作成したハッシュ値をキーにDBプロジェクトのビルド生成物をキャッシュから取得
    - name: Download db-project build artifact
      uses: actions/cache@v3
      with:
        path: ./db/build
        key: db-build-artifact-${{ hashFiles('db/src/test/resources/**') }}
    # キャッシュが存在したかチェック
    - name: Check exists db-project build artifact
      id: check-cache
      run: |
        if [ -d "db/build/libs" ]; then
          echo "cache-hit=true" >> "$GITHUB_OUTPUT"
        else
          echo "cache-hit=false" >> "$GITHUB_OUTPUT"
        fi
      shell: bash
    # DBプロジェクトのビルド成果物を生成
    - name: Build db-project
      if: ${{ steps.check-cache.outputs.cache-hit != 'true' }}
      run: |
        ./gradlew setup -x prepare
        ./gradlew :db:build
      shell: bash
    # DBプロジェクトのビルド成果物をキャッシュに保存
    - name: Save db-project build artifact
      if: ${{ steps.check-cache.outputs.cache-hit != 'true' }}
      uses: actions/cache@v3
      with:
        path: ./db/build
        key: db-build-artifact-${{ hashFiles('db/src/test/resources/**') }}
