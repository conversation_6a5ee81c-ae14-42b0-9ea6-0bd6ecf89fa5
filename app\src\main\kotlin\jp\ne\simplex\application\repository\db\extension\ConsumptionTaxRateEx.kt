package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.ConsumptionTaxRate
import jp.ne.simplex.db.jooq.gen.tables.pojos.ConsumptionTaxRateMasterPojo

class ConsumptionTaxRateEx {
    companion object {
        fun ConsumptionTaxRateMasterPojo.toConsumptionTaxRate():
                ConsumptionTaxRate {
            return ConsumptionTaxRate.of(
                nationalTaxConsumptionPercent = this.nationalTaxConsumptionPercent
            )
        }
    }
}
