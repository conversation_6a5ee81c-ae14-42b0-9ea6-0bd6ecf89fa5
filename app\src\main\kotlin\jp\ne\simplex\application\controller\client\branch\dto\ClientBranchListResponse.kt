package jp.ne.simplex.application.controller.client.branch.dto

import jp.ne.simplex.application.controller.client.shared.ClientBranchDto
import jp.ne.simplex.application.model.Branch

data class ClientBranchListResponse private constructor(
    val list: List<ClientBranchDto>,
    val affiliationBranchCode: String?
) {

    companion object {
        fun of(branchList: List<Branch>, affiliationBranchCode: Branch.Code?): ClientBranchListResponse {
            return ClientBranchListResponse(
                list = branchList.map { ClientBranchDto.of(it) },
                affiliationBranchCode = affiliationBranchCode?.getValue()
            )
        }
    }
}
