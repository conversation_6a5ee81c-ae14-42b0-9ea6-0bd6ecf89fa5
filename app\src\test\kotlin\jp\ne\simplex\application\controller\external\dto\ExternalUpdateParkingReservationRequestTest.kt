package jp.ne.simplex.application.controller.external.dto

import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import jp.ne.simplex.application.controller.external.parking.dto.ExternalUpdateParkingReservationRequest
import jp.ne.simplex.application.model.CancelApplicationParkingReservation
import jp.ne.simplex.application.model.ExternalSystem
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.application.model.RegisterParkingReservation
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.stubApiKeyAuthInfo
import java.time.LocalDate

class ExternalUpdateParkingReservationRequestTest : FunSpec({

    val apiKeyAuthInfo = stubApiKeyAuthInfo(ExternalSystem.KIMAROOM_SIGN)

    context("キマルームsign向けに外部公開している駐車場予約更新APIのリクエストを適切にデシリアライズできること") {

        val base = ExternalUpdateParkingReservationRequest(
            buildingCode = "000000001",
            parkingCode = "001",
            reservationStatus = 1,
            reservationType = 0,
            reserveStartDate = LocalDate.now().yyyyMMdd(),
            reserveEndDate = null,
            receptionStaff = null,
            reserverName = null,
            reserverTel = null,
            linkedBuildingCode = null,
            linkedRoomCode = null,
            remarks = null,
        )

        context("予約状態によって、適切なサービスのインスタンスを生成できること") {

            test("予約状態が1(受付)のとき駐車場予約(新規登録)のドメインに変換できること") {

                val request = base.copy(
                    receptionStaff = "simplex受付",
                    reserverName = "simplex予約",
                    reserverTel = "090-1234-5678",
                    linkedBuildingCode = "000000001",
                    linkedRoomCode = "00001",
                    remarks = "予約メモ",
                )

                val actual = request.toServiceInterface(apiKeyAuthInfo)

                actual.shouldBeInstanceOf<RegisterParkingReservation>()
                actual.parkingLotId.buildingCode.value.shouldBe(request.buildingCode)
                actual.parkingLotId.parkingLotCode.value.shouldBe(request.parkingCode)
                actual.parkingReservationStatus.value.shouldBe(request.reservationStatus.toString())
                actual.reservationType.value.shouldBe(request.reservationType.toString())
                actual.reserveStartDatetime!!.yyyyMMdd().shouldBe(request.reserveStartDate)
                actual.receptionStaff.shouldBe(request.receptionStaff)
                actual.reserverName.shouldBe(request.reserverName)
                actual.reserverTel!!.value.shouldBe(request.reserverTel)
                actual.requestSource.value.shouldBe(ParkingReservation.RequestSource.KIMA_SIGN.value)
                actual.linkedBuildingCode!!.value.shouldBe(request.linkedBuildingCode)
                actual.linkedRoomCode!!.value.shouldBe(request.linkedRoomCode)
                actual.remarks!!.value.shouldBe(request.remarks)
            }

            test("予約状態が3(取消)のとき駐車場予約(取消)のドメインに変換できること") {

                val request = base.copy(
                    reservationStatus = 3,
                    remarks = "メモ"
                )

                val actual = request.toServiceInterface(apiKeyAuthInfo)

                actual.shouldBeInstanceOf<CancelApplicationParkingReservation>()
                actual.parkingLotId.buildingCode.value.shouldBe(request.buildingCode)
                actual.parkingLotId.parkingLotCode.value.shouldBe(request.parkingCode)
                actual.status.value.shouldBe(request.reservationStatus.toString())
                actual.remarks!!.value.shouldBe(request.remarks)
            }

            test("予約状態が0(仮申込)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationStatus = 0,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format().message)

            }

            test("予約状態が2(完了)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationStatus = 2,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format().message)
            }
        }

        context("予約種別は申込のみ受け付けること") {

            test("予約種別が0(申込)のときサービスのドメインに変換できること") {

                shouldNotThrow<ClientValidationException> {
                    base.copy(
                        receptionStaff = "simplex受付",
                        reserverName = "simplex予約",
                        reserverTel = "090-1234-5678",
                        linkedBuildingCode = "000000001",
                        linkedRoomCode = "00001",
                        remarks = "予約メモ",
                    ).toServiceInterface(apiKeyAuthInfo)
                }
            }

            test("予約種別が1(作業)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationType = 1,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format().message)
            }

            test("予約種別が2(場所変更)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationType = 2,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format().message)
            }

            test("予約種別が3(1日利用)のときバリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reservationType = 3,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format().message)
            }
        }

        context("予約開始日と予約終了日のが適切に設定できること") {

            test("予約開始日が指定のフォーマット(yyyymmdd)である場合サービスのドメインに変換できること") {

                shouldNotThrow<ClientValidationException> {
                    base.copy(
                        reserveStartDate = "20250101",
                        reserveEndDate = null,
                    ).toServiceInterface(apiKeyAuthInfo)
                }
            }

            test("予約開始日が指定のフォーマット(yyyymmdd)でない場合バリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reserveStartDate = "2025101",
                        reserveEndDate = null,
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(ErrorMessage.INVALID_DATE_FORMAT.format("yyyyMMdd").message)
            }

            test("予約開始日が指定されている場合バリデーションに抵触すること") {

                shouldThrow<ClientValidationException> {
                    base.copy(
                        reserveStartDate = "20250101",
                        reserveEndDate = "20250102",
                    ).toServiceInterface(apiKeyAuthInfo)
                }.detail.message.shouldBe(
                    ErrorMessage.PARKING_RESERVATION_END_DATE_NOT_ALLOWED.format("申込").message
                )
            }
        }
    }
})
