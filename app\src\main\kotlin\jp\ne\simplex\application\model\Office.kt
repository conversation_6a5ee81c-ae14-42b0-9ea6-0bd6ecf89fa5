package jp.ne.simplex.application.model

class Office(
    val code: Code,
    val name: Name,
) {

    data class Code private constructor(val value: String) {
        companion object {
            fun of(value: String): Code {
                return Code(value)
            }
        }
    }

    data class Name private constructor(val value: String) {
        companion object {
            fun of(value: String): Name {
                return Name(value)
            }
        }
    }
}
