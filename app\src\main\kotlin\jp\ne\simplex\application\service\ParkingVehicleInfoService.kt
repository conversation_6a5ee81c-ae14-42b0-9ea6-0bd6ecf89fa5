package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.ParkingVehicleInfo
import jp.ne.simplex.application.repository.db.ParkingVehicleInfoRepository
import jp.ne.simplex.authentication.AuthInfo
import org.springframework.stereotype.Service

@Service
class ParkingVehicleInfoService(
    private val repository: ParkingVehicleInfoRepository,
) {
    fun update(requestUser: AuthInfo.RequestUser, parameter: ParkingVehicleInfo) {
        repository.upsert(requestUser, parameter)
    }
}
