package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException

/** 支店 */
class Branch(
    val code: Code,
    val name: Name,
    val company: Company,
) {
    data class Code private constructor(private val value: String) {
        companion object {
            private const val VALUE_MIN_LENGTH = 3

            fun of(value: String): Code {
                if (value.length < VALUE_MIN_LENGTH) {
                    throw ModelCreationFailedException(
                        ErrorMessage.STRING_MIN_LENGTH.format("支店コード", VALUE_MIN_LENGTH)
                    )
                }
                return Code(value)
            }
        }

        /**
         * 支店コードの全文字を取得する
         *
         * @see getPrefix と取得できる値が異なるため確認の上適切に使用すること
         */
        fun getValue(): String {
            return value
        }

        /**
         * 支店コードの先頭3文字を取得する
         *
         * DBには、3桁/6桁の2種類の支店コードが存在するが、実態は、先頭3文字が支店コードを表すため、
         * DBで検索する際は、基本的にこちらを使って 末尾に "000" や "800"を付与して検索することが多い
         */
        fun getPrefix(): String {
            return value.substring(0, 3)
        }
    }

    data class Name private constructor(val value: String) {
        companion object {
            fun of(value: String): Name {
                return Name(value)
            }
        }
    }

    companion object {
        // いい物件ボードに不正な状態で永続化されているデータを扱うため
        fun dummy(code: String? = null, name: String? = null): Branch {
            return Branch(
                code = Code.of(code ?: "000"),
                name = Name.of(name ?: ""),
                company = Company.DaitouKentaku,
            )
        }
    }
}
