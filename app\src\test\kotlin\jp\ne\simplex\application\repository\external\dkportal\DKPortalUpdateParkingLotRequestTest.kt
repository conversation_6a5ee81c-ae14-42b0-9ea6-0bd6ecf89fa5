package jp.ne.simplex.application.repository.external.dkportal

import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.application.repository.external.dkportal.dto.DKPortalUpdateParkingLotRequest
import jp.ne.simplex.stub.stubParkingContractPossibility

class DKPortalUpdateParkingLotRequestTest : FunSpec({

    context("DKリンクでの駐車場申込可否の判定結果からDKポータルで表示される検索結果が正しくマッピングされていること") {

        context("駐車場要問合せの設定がある場合") {
            test("DKポータル「あり：表示」「2台目可否：なし」") {
                val request = stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.MANUAL
                )

                val actual = DKPortalUpdateParkingLotRequest.of(request)

                actual.orderCode.shouldBe(request.orderCode.value)
                actual.availableParkingFlag.shouldBe(1)
                actual.secondParkingContractPossibleFlag.shouldBe(0)
            }
        }

        context("駐車場要問合せの設定がない場合") {

            test("申込判定「1台目：可」「2台目：可」-> DKポータル「あり：表示」「2台目可否：表示」") {
                val request = stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.POSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO
                )

                val actual = DKPortalUpdateParkingLotRequest.of(request)

                actual.orderCode.shouldBe(request.orderCode.value)
                actual.availableParkingFlag.shouldBe(1)
                actual.secondParkingContractPossibleFlag.shouldBe(1)
            }

            test("申込判定「1台目：可」「2台目：不可」-> DKポータル「あり：表示」「2台目可否：なし」") {
                val request = stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO
                )

                val actual = DKPortalUpdateParkingLotRequest.of(request)

                actual.orderCode.shouldBe(request.orderCode.value)
                actual.availableParkingFlag.shouldBe(1)
                actual.secondParkingContractPossibleFlag.shouldBe(0)
            }

            test("申込判定「1台目：不可」「2台目：不可」-> DKポータル「あり：なし」「2台目可否：なし」") {
                val request = stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO
                )

                val actual = DKPortalUpdateParkingLotRequest.of(request)

                actual.orderCode.shouldBe(request.orderCode.value)
                actual.availableParkingFlag.shouldBe(0)
                actual.secondParkingContractPossibleFlag.shouldBe(0)
            }

            test("申込判定「1台目：可」「2台目：要問合せ」-> DKポータル「あり：表示」「2台目可否：なし」") {
                val request = stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.REQUIRED_CONFIRM,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO
                )

                val actual = DKPortalUpdateParkingLotRequest.of(request)

                actual.orderCode.shouldBe(request.orderCode.value)
                actual.availableParkingFlag.shouldBe(1)
                actual.secondParkingContractPossibleFlag.shouldBe(0)
            }

            test("申込判定「1台目：要問合せ」「2台目：不可」-> DKポータル「あり：なし」「2台目可否：なし」") {
                val request = stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.REQUIRED_CONFIRM,
                    secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO
                )

                val actual = DKPortalUpdateParkingLotRequest.of(request)

                actual.orderCode.shouldBe(request.orderCode.value)
                actual.availableParkingFlag.shouldBe(0)
                actual.secondParkingContractPossibleFlag.shouldBe(0)
            }
        }
    }
})
