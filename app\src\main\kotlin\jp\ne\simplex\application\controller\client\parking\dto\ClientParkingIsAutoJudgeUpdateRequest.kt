package jp.ne.simplex.application.controller.client.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.model.UpdateParkingContractIsAutoJudge
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ModelCreationFailedException

data class ClientParkingIsAutoJudgeUpdateRequest(

    @JsonProperty("orderCode")
    @field:Schema(description = "受注コード", example = "0001303")
    val orderCode: String,

    @JsonProperty("isAutoJudge")
    @field:Schema(description = "契約可否自動判定有無", example = "MANUAL")
    val isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge,

    ) {

    fun toServiceInterface(): UpdateParkingContractIsAutoJudge {
        try {
            return UpdateParkingContractIsAutoJudge(
                orderCode = Building.OrderCode.of(orderCode),
                isAutoJudge = isAutoJudge
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }
}
