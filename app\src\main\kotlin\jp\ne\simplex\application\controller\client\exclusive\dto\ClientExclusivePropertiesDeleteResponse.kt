package jp.ne.simplex.application.controller.client.exclusive.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.model.ExclusivePropertyAction

class ClientExclusivePropertiesDeleteResponse(
    @JsonProperty("status")
    @field:Schema(description = "実行結果")
    val status: ExclusivePropertyAction.Status,

    @JsonProperty("reason")
    @field:Schema(description = "結果理由")
    val reason: ExclusivePropertyAction.StatusReason?,

    @JsonProperty("failedList")
    @field:Schema(description = "変更に失敗した先行公開IDリスト")
    val failedList: List<String>,
) {
    companion object {
        fun of(record: ExclusivePropertyAction.Result<ExclusiveProperty.Id>): ClientExclusivePropertiesDeleteResponse {
            return ClientExclusivePropertiesDeleteResponse(
                status = record.status,
                reason = record.reason,
                failedList = record.failedList.map { it.value.toString() }
            )
        }
    }
}
