package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import java.time.LocalDate

class Building(
    // 建物コード
    val code: Code,
    // 建物名称
    val name: Name,
    /** リーシング店舗CD */
    val leasingStoreCode: Branch.Code? = null,
    /** 審査支店CD */
    val reviewBranchCode: Branch.Code? = null,
    // 郵便番号
    val postalCode: String? = null,
    // 所在地
    val location: String? = null,
    // 営業所コード
    val businessOfficeCode: Office.Code? = null,
    // 建物契約形態
    val buildingContractForm: String? = null,
    // 家主名
    val landlordName: String? = null,
    // 完成日
    val completionDeliveryDate: LocalDate? = null,
) {

    data class OrderCode private constructor(val value: String) {
        companion object {

            private const val LENGTH = 7

            fun of(value: String): OrderCode {
                return if (value.length == LENGTH) OrderCode(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("受注コード", LENGTH)
                )
            }
        }
    }

    /** 建物コード */
    data class Code private constructor(val value: String) {
        companion object {
            const val LENGTH = 9
            fun of(value: String): Code {
                return if (value.length == LENGTH) Code(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("建物コード", LENGTH)
                )
            }
        }

        fun getOrderCode(): OrderCode {
            return OrderCode.of(value.substring(0, 7))
        }

        /** 棟が違うが同じ建物かどうか */
        fun isSameOrderCode(buildingCode: Code?): Boolean {
            return buildingCode?.let { this.getOrderCode() == it.getOrderCode() } ?: false
        }

        fun toLayoutImageUrl(tmpUrlBase: String = ""): String {
            val imagePath =
                "${value.substring(0, 3)}/${value.substring(3, 6)}"
            return "${tmpUrlBase}${imagePath}/${value}.jpg"
        }
    }

    data class Name private constructor(val value: String) {
        companion object {
            fun of(value: String): Name {
                return Name(value)
            }
        }
    }

    /** 建物種別 */
    enum class Type(val code: String) {
        APARTMENT("10"), // アパート・コーポ
        MANSION("20"), // マンション
        DETACHED_HOUSE("70"), // 一戸建
        COMMERCIAL_APARTMENT("100"), // アパート・コーポ
        STORE_APARTMENT("110"), // 店舗付アパート
        COMMERCIAL_MANSION("200"), // マンション
        STORE_MANSION("210"), // 店舗付マンション
        WAREHOUSE("300"), // 倉庫
        OFFICE_WAREHOUSE("310"), // 事務所付倉庫
        TRUNK_ROOM("320"), // トランクルーム
        FACTORY("350"), // 工場
        OFFICE_FACTORY("360"), // 事務所付工場
        OFFICE("400"), // 事務所
        WAREHOUSE_OFFICE("410"), // 倉庫付事務所
        FACTORY_OFFICE("420"), // 工場付事務所
        DEDICATED_OFFICE("490"), // 専用事務所
        STORE("500"), // 店舗
        APARTMENT_STORE("510"), // アパート付店舗
        MANSION_STORE("520"), // マンション付店舗
        BUILDING("600"), // ビル
        COMMERCIAL_DETACHED_HOUSE("700"), // 戸建住宅
        PARKING("900"), // 駐車場
        OTHER("990"), // その他
        ;

        companion object {
            fun getResidentialTypes(): List<Type> {
                return listOf(APARTMENT, MANSION, DETACHED_HOUSE)
            }
        }
    }

    /** 建物契約形態 */
    enum class SubleaseType(val value: String) {
        IKKATSU("4"), // 30年一括借上
        NEW_IKKATSU("42"), // 新35年一括借上
        NEW_IKKATSU_PARKING("43"), // 新35年一括借上（駐車管
        KANRIDAITATE("5"), //管理契約(大パ)
        NEW_KANRIDAITATE_PARKING("52"), // 管理契約(新35年一括)
    }
}
