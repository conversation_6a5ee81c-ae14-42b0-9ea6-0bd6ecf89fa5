package jp.ne.simplex.application.controller.external.shared

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

class ReserveDateCreatorForWelcomePark {
    companion object {
        // WelcomeParkの予約可能日を生成するメソッド
        fun reserveStartDate(): LocalDate {
            // リクエスト受信時刻が7:30より前の場合、前日の予約状況の確認リクエストに変換
            val now = LocalDateTime.now()
            return if (now.toLocalTime() < LocalTime.of(7, 30)) {
                now.minusDays(1).toLocalDate()
            } else {
                now.withHour(0).toLocalDate()
            }
        }
    }
}
