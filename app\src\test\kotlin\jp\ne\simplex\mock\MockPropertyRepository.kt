package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.db.PropertyRepositoryInterface

class MockPropertyRepository(
    val listFunc: (propertyIds: List<Property.Id>) -> List<Property> = { _ -> emptyList() },
) : PropertyRepositoryInterface {
    override fun findBy(propertyId: Property.Id): Property? {
        return listFunc(listOf(propertyId)).firstOrNull()
    }

    override fun list(propertyIds: List<Property.Id>): List<Property> {
        return listFunc(propertyIds)
    }
}
