package jp.ne.simplex.application.repository.external.dkportal.dto

import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.model.ExclusivePropertyAction
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd

class DKPortalUpdateExclusiveHousingRequest(
    exclusiveId: ExclusiveProperty.Id,
    record: ExclusivePropertyAction.Record,
) : DKPortalUpdateExclusiveRequest(
    dkLinkId = exclusiveId.value.toString(),
    exclusiveFrom = record.exclusiveRange.from.yyyyMMdd(),
    exclusiveTo = record.exclusiveRange.to.yyyyMMdd(),
    eCode = record.exclusiveTargetWithId.target.eCode?.value,
    companyType = DKPortalExclusiveCompanyType.of(record.exclusiveTargetWithId.target.companyType).value,
) {
    override fun getDKPortalOperationName(): DKPortalOperationName {
        return DKPortalOperationName.UPDATE_EXCLUSIVE_HOUSING
    }
}
