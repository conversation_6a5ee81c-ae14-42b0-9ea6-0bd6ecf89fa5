package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException

class Room(
    val code: Code,
    val number: Number,
) {
    data class Code private constructor(val value: String) {
        companion object {

            const val LENGTH = 5

            fun of(value: String): Code {
                return if (value.length == LENGTH) Code(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("部屋コード", LENGTH)
                )
            }
        }
    }

    data class Number private constructor(private val value: String) {
        companion object {

            private const val MAX_LENGTH = 4

            fun of(value: String): Number {
                return if (value.length <= MAX_LENGTH) Number(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_MAX_LENGTH.format("部屋番号", MAX_LENGTH)
                )
            }
        }

        /**
         * 部屋番号をそのまま取得する
         *
         * @see getFormattedValue と取得できる値が異なるため確認の上適切に使用すること
         */
        fun getValue(): String {
            return this.value
        }

        /**
         * 部屋番号の先頭0を除去したものを取得する
         *
         * ソートに利用するため4桁で0埋めされているので、先頭の0を除去
         */
        fun getFormattedValue(): String {
            return this.value.trimStart('0')
        }
    }
}
