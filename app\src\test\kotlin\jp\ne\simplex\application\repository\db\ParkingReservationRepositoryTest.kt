package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.*
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingReservationPojo
import jp.ne.simplex.db.jooq.gen.tables.records.ParkingReservationRecord
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_RESERVATION
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.shared.DSLContextEx.Companion.selectBy
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import java.util.*

class ParkingReservationRepositoryTest : AbstractTestContainerTest() {

    private lateinit var repository: ParkingReservationRepository
    private val currentDateTime = LocalDateTime.of(2028, 7, 3, 13, 45, 30)

    override fun beforeEach() {
        repository = ParkingReservationRepository(dslContext)
        MockLocalDateTime.setNow(currentDateTime)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_RESERVATION)
    }

    companion object {
        private const val DEFAULT_BUILDING_CODE = "000000100"
        private const val DEFAULT_PARKING_LOT_CODE = "001"
    }

    @Nested
    @DisplayName("駐車場予約情報更新APIの検証")
    inner class Scenario1 {

        // カラム突合をします
        private fun assertEqualsAll(
            expected: RegisterParkingReservation,
            actual: ParkingReservationPojo
        ) {
            assertNotNull(actual.parkingReservationId)
            assertEquals(actual.parkingReservationId.length, 36)
            assertEquals(expected.parkingLotId.buildingCode.value, actual.buildingCode)
            assertEquals(expected.parkingLotId.parkingLotCode.value, actual.parkingLotCode)
            assertEquals(expected.reservationType.value, actual.reserveType)
            assertEquals(expected.parkingReservationStatus.value, actual.reserveStatus)
            assertEquals(
                expected.reserveStartDatetime?.truncatedTo(ChronoUnit.SECONDS),
                actual.reserveStartDatetime
            )
            assertEquals(
                expected.reserveEndDatetime?.truncatedTo(ChronoUnit.SECONDS),
                actual.reserveEndDatetime
            )
            assertEquals(expected.receptionStaff, actual.receptionStaff)
            assertEquals(expected.reserverName, actual.reserverName)
            assertEquals(expected.reserverTel?.value, actual.reserverTel)
            assertEquals(expected.requestSource.value, actual.reserverSystem)
            assertEquals(expected.remarks?.value, actual.remarks)
            assertEquals("0", actual.deleteFlag)
        }

        @Nested
        @DisplayName("新規登録処理の検証")
        inner class Scenario1x1 {

            @Test
            @DisplayName("NULLABLEの項目が全てNULLの場合PARKING_RESERVATIONにレコードがINSERTされること")
            fun case1() {

                val parkingReservation = stubRegisterParkingReservation()
                repository.register(stubJwtRequestUser(), parkingReservation)

                val result =
                    dslContext.selectBy(parkingReservation.parkingLotId)

                // レコード数の突合
                assertEquals(1, result.size)

                // 全項目の突合
                assertEqualsAll(parkingReservation, result.first())
            }

            @Test
            @DisplayName("リクエストに全て値がある場合PARKING_RESERVATIONにレコードがINSERTされること")
            fun case2() {
                val now = LocalDate.now()
                // setup
                val parkingReservation = stubRegisterParkingReservation(
                    reservationType = ParkingReservation.Type.WORK,
                    reserveStartDatetime = now.plusDays(1).atTime(LocalTime.MIN),
                    reserveEndDatetime = now.plusDays(31).atTime(LocalTime.MAX),
                    receptionStaff = "シンプレクス受付",
                    reserverName = "シンプレクス予約",
                    reserverTel = "090-1234-5678",
                    remarks = "コメント",
                )

                repository.register(stubJwtRequestUser(), parkingReservation)

                val result =
                    dslContext.selectBy(parkingReservation.parkingLotId)

                // レコード数の突合
                assertEquals(1, result.size)

                // 全項目の突合
                assertEqualsAll(parkingReservation, result.first())
            }
        }

        @Nested
        @DisplayName("更新処理の検証")
        inner class Scenario1x2 {

            private val now = LocalDate.now()

            private val existsData = stubParkingReservationPojo(
                reserveType = ParkingReservation.Type.WORK,
                receptionDate = now.yyyyMMdd().toInt(),
                receptionStaff = "シンプレクス受付",
                reserverName = "シンプレクス予約",
                reserverTel = "090-1234-5678",
                remarks = "コメント",
                requestSource = ParkingReservation.RequestSource.DK_LINK,
                reserveStartDatetime = now.plusDays(1).atTime(LocalTime.MIN),
                reserveEndDatetime = now.plusDays(5).atTime(LocalTime.MAX),
            )

            @BeforeEach
            fun setup() {
                dslContext.saveParkingReservationPojo(existsData)
            }

            @Test
            @DisplayName("リクエスト値が設定されたレコードが更新されること")
            fun case1() {

                val updateData = stubUpdateParkingReservation(
                    receptionStaff = "シンプレクス受付(更新済)",
                    reserverName = "シンプレクス予約(更新済)",
                    reserverTel = "090-1234-5679",
                    remarks = "コメント(更新済)",
                    reserveStartDatetime = now.plusDays(6).atTime(LocalTime.MIN),
                    reserveEndDatetime = now.plusDays(10).atTime(LocalTime.MAX),
                )

                repository.update(stubJwtRequestUser(), updateData)

                val result = dslContext.selectBy(updateData.id)

                // レコードの存在チェック
                assertNotNull(result!!)

                // 全項目の突合
                assertEquals(updateData.status.value, result.reserveStatus)
                assertEquals(
                    updateData.reserveStartDatetime?.truncatedTo(ChronoUnit.SECONDS),
                    result.reserveStartDatetime
                )
                assertEquals(
                    updateData.reserveEndDatetime?.truncatedTo(ChronoUnit.SECONDS),
                    result.reserveEndDatetime
                )
                assertEquals(updateData.receptionStaff, result.receptionStaff)
                assertEquals(updateData.reserverName, result.reserverName)
                assertEquals(updateData.reserverTel?.value, result.reserverTel)
                assertEquals(updateData.requestSource.value, result.reserverSystem)
                assertEquals(updateData.remarks?.value, result.remarks)
            }
        }

        @Nested
        @DisplayName("ID指定取消処理の検証")
        inner class Scenario1x3 {

            private val existsData = stubParkingReservationPojo(
                reserveStatus = ParkingReservation.Status.RESERVATION,
                reserveType = ParkingReservation.Type.REPLACE,
            )

            @BeforeEach
            fun setup() {
                dslContext.saveParkingReservationPojo(existsData)
            }

            @Test
            @DisplayName("IDに紐づくレコードが取消されること")
            fun case1() {

                val cancelData = stubCancelParkingReservation(remarks = "コメント(更新済)")

                repository.cancel(stubJwtRequestUser(), cancelData)

                val result = dslContext.selectBy(cancelData.id)

                // レコードの存在チェック
                assertNotNull(result!!)

                // 全項目の突合
                assertEquals(ParkingReservation.Status.CANCEL.value, result.reserveStatus)
                assertEquals(cancelData.remarks?.value, result.remarks)
            }
        }

        @Nested
        @DisplayName("申込の予約取消処理の検証")
        inner class Scenario1x4 {

            private val now: LocalDate = LocalDate.now()

            // 申込
            private val existsApplication = stubParkingReservationPojo(
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveType = ParkingReservation.Type.AUTO_APPLICATION,
                requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                reserveEndDatetime = null,
            )

            // 作業
            private val existsWork = stubParkingReservationPojo(
                parkingReservationId = "ff0e398d-95cb-46de-aa9f-1474049f4c8b",
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveType = ParkingReservation.Type.WORK,
                requestSource = ParkingReservation.RequestSource.DK_LINK,
                reserveStartDatetime = now.plusDays(2).atTime(LocalTime.MIN),
                reserveEndDatetime = now.plusDays(3).atTime(LocalTime.MAX),
            )

            @BeforeEach
            fun setup() {
                dslContext.saveParkingReservationPojo(existsApplication, existsWork)
            }

            @Test
            @DisplayName("建物コードと部屋コードに紐づく申込ステータスのレコードが取消されること")
            fun case1() {

                val cancelData = stubCancelParkingApplicationReservation(
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                    remarks = "コメント(更新済)"
                )

                repository.cancelApplication(
                    stubJwtRequestUser(),
                    ParkingReservation.Id.of(existsApplication.parkingReservationId),
                    cancelData
                )

                val result = dslContext.selectBy(
                    ParkingLot.Id(
                        Building.Code.of(DEFAULT_BUILDING_CODE),
                        ParkingLot.Code.of(DEFAULT_PARKING_LOT_CODE)
                    )
                )
                val actual =
                    result.first { it.parkingReservationId == existsApplication.parkingReservationId }

                // レコードの存在のチェック
                assertNotNull(actual)

                // 全項目の突合
                assertEquals(ParkingReservation.Status.CANCEL.value, actual.reserveStatus)
                assertEquals(cancelData.remarks?.value, actual.remarks)
            }
        }
    }

    @Nested
    @DisplayName("バリデーション用レコード取得ロジックの検証")
    inner class Scenario2 {

        @Nested
        @DisplayName("駐車場予約情報取得(ID指定)の検証")
        inner class Scenario2x1 {

            private val now: LocalDate = LocalDate.now()

            // 申込
            private val existsApplication = stubParkingReservationPojo(
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveStatus = ParkingReservation.Status.RESERVATION,
                reserveType = ParkingReservation.Type.AUTO_APPLICATION,
                requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                reserveEndDatetime = null,
            )

            @BeforeEach
            fun setup() {
                dslContext.saveParkingReservationPojo(existsApplication)
            }

            @Test
            @DisplayName("指定した駐車場予約IDのレコードが取得できること")
            fun case1() {

                val result =
                    repository.findById(ParkingReservation.Id.of(existsApplication.parkingReservationId))
                assertNotNull(result!!)

                // 全項目の突合
                assertEquals(existsApplication.parkingReservationId, result.id.value)
                assertEquals(existsApplication.buildingCode, result.buildingCode.value)
                assertEquals(DEFAULT_PARKING_LOT_CODE, result.parkingLotCode?.value)
                assertEquals(ParkingReservation.Status.RESERVATION, result.status)
                assertEquals(ParkingReservation.Type.AUTO_APPLICATION, result.reservationType)
                assertEquals(now.plusDays(5).atTime(LocalTime.MIN), result.reserveStartDatetime)
                assertEquals(null, result.reserveEndDatetime)
            }
        }

        @Nested
        @DisplayName("有効な駐車場申込予約情報取得の検証")
        inner class Scenario2x2 {

            private val now: LocalDate = LocalDate.now()

            // 有効申込
            private val existsApplication = stubParkingReservationPojo(
                parkingReservationId = "ff0e398d-95cb-46de-aa9f-1474049f4c8b",
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveStatus = ParkingReservation.Status.RESERVATION,
                reserveType = ParkingReservation.Type.AUTO_APPLICATION,
                requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                reserveEndDatetime = null,
            )

            // 取消済申込
            private val finishedApplication = stubParkingReservationPojo(
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveStatus = ParkingReservation.Status.CANCEL,
                reserveType = ParkingReservation.Type.AUTO_APPLICATION,
                requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                reserveEndDatetime = null,
            )

            // 有効作業
            private val existsWork = stubParkingReservationPojo(
                parkingReservationId = "ff0e398d-95cb-46de-aa9f-1474049f4c8a",
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveStatus = ParkingReservation.Status.RESERVATION,
                reserveType = ParkingReservation.Type.WORK,
                requestSource = ParkingReservation.RequestSource.DK_LINK,
                reserveStartDatetime = now.plusDays(2).atTime(LocalTime.MIN),
                reserveEndDatetime = now.plusDays(3).atTime(LocalTime.MAX),
            )

            @BeforeEach
            fun setup() {
                dslContext.saveParkingReservationPojo(
                    existsApplication,
                    finishedApplication,
                    existsWork
                )
            }

            @Test
            @DisplayName("指定した建物コードと部屋コードに紐づく有効申込のレコードが取得できること")
            fun case1() {

                val result = repository.findActiveApplication(
                    ParkingLot.Id(
                        buildingCode = Building.Code.of(DEFAULT_BUILDING_CODE),
                        parkingLotCode = ParkingLot.Code.of(DEFAULT_PARKING_LOT_CODE)
                    )
                )
                assertNotNull(result!!)

                // 全項目の突合
                assertEquals("ff0e398d-95cb-46de-aa9f-1474049f4c8b", result.id.value)
                assertEquals(DEFAULT_BUILDING_CODE, result.buildingCode.value)
                assertEquals(DEFAULT_PARKING_LOT_CODE, result.parkingLotCode?.value)
                assertEquals(ParkingReservation.Status.RESERVATION, result.status)
                assertEquals(ParkingReservation.Type.AUTO_APPLICATION, result.reservationType)
                assertEquals(now.plusDays(5).atTime(LocalTime.MIN), result.reserveStartDatetime)
                assertEquals(null, result.reserveEndDatetime)
            }
        }

        @Nested
        @DisplayName("有効な駐車場予約情報リスト取得の検証")
        inner class Scenario2x3 {

            private val now = LocalDate.now()

            // 有効申込
            private val existsApplication = stubParkingReservationPojo(
                parkingReservationId = "ff0e398d-95cb-46de-aa9f-1474049f4c8b",
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveStatus = ParkingReservation.Status.RESERVATION,
                reserveType = ParkingReservation.Type.AUTO_APPLICATION,
                requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                reserveEndDatetime = null
            )

            // 取消済申込
            private val finishedApplication = stubParkingReservationPojo(
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveStatus = ParkingReservation.Status.CANCEL,
                reserveType = ParkingReservation.Type.AUTO_APPLICATION,
                requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                reserveStartDatetime = now.plusDays(5).atTime(LocalTime.MIN),
                reserveEndDatetime = null
            )

            // 有効作業
            private val existsWork = stubParkingReservationPojo(
                parkingReservationId = "ff0e398d-95cb-46de-aa9f-1474049f4c8a",
                buildingCode = DEFAULT_BUILDING_CODE,
                parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                reserveStatus = ParkingReservation.Status.RESERVATION,
                reserveType = ParkingReservation.Type.WORK,
                requestSource = ParkingReservation.RequestSource.DK_LINK,
                reserveStartDatetime = now.plusDays(2).atTime(LocalTime.MIN),
                reserveEndDatetime = now.plusDays(3).atTime(LocalTime.MAX),
            )

            @BeforeEach
            fun setup() {
                dslContext.saveParkingReservationPojo(
                    existsApplication,
                    finishedApplication,
                    existsWork
                )
            }

            @Test
            @DisplayName("指定した建物コードと部屋コードに紐づく有効予約のレコード数が正しいこと")
            fun case1() {

                val result = repository.findActiveReservations(
                    Building.Code.of(DEFAULT_BUILDING_CODE),
                    ParkingLot.Code.of(DEFAULT_PARKING_LOT_CODE)
                )

                // レコード数チェック
                assertEquals(2, result.size)
            }
        }
    }

    @Nested
    @DisplayName("処理中駐車場予約情報検索の検証")
    inner class Scenario3 {
        private val now: LocalDate = LocalDate.now()

        @BeforeEach
        fun setup() {
            dslContext.save(
                table = PARKING_RESERVATION,
                recordConstructor = { p: ParkingReservationPojo -> ParkingReservationRecord(p) },
                pojos = listOf(
                    stubParkingReservationPojo(
                        parkingReservationId = "012345678901234567890123456789000001",
                        buildingCode = "000024301",
                        parkingLotCode = "701",
                        reserveStatus = ParkingReservation.Status.TENTATIVE,
                        reserveType = ParkingReservation.Type.MANUAL_APPLICATION,
                        reserveStartDatetime = now.plusDays(-1).atTime(LocalTime.of(0, 0, 0)),
                        reserveEndDatetime = now.plusDays(1).atTime(LocalTime.of(23, 59, 59)),
                        remarks = "メモ1",
                        requestSource = ParkingReservation.RequestSource.DK_LINK,
                        deleteFlag = "0",
                        creationDate = 20250423,
                        creationTime = 1
                    ),
                    stubParkingReservationPojo(
                        parkingReservationId = "012345678901234567890123456789000002",
                        buildingCode = "000024301",
                        parkingLotCode = "702",
                        reserveStatus = ParkingReservation.Status.RESERVATION,
                        reserveType = ParkingReservation.Type.WORK,
                        reserveStartDatetime = now.plusDays(-1).atTime(LocalTime.of(0, 0, 0)),
                        reserveEndDatetime = now.plusDays(1).atTime(LocalTime.of(23, 59, 59)),
                        remarks = "メモ2",
                        requestSource = ParkingReservation.RequestSource.DK_LINK,
                        deleteFlag = "0",
                        creationDate = 20250423,
                        creationTime = 1,
                        updateDate = 20250424,
                        updateTime = 235959
                    ),
                    stubParkingReservationPojo(
                        parkingReservationId = "012345678901234567890123456789000101",
                        buildingCode = "000024302",
                        parkingLotCode = "101",
                        reserveStatus = ParkingReservation.Status.TENTATIVE,
                        reserveType = ParkingReservation.Type.ONE_DAY,
                        reserveStartDatetime = now.plusDays(-1).atTime(LocalTime.of(0, 0, 0)),
                        reserveEndDatetime = now.plusDays(1).atTime(LocalTime.of(23, 59, 59)),
                        remarks = "メモ3",
                        requestSource = ParkingReservation.RequestSource.WELCOME_PARK,
                        deleteFlag = "0"
                    ),
                    stubParkingReservationPojo(
                        parkingReservationId = "012345678901234567890123456789000102",
                        buildingCode = "000024302",
                        parkingLotCode = "101",
                        reserveStatus = ParkingReservation.Status.FINISHED,
                        reserveType = ParkingReservation.Type.REPLACE,
                        requestSource = ParkingReservation.RequestSource.DK_LINK,
                        deleteFlag = "0"
                    ),
                    stubParkingReservationPojo(
                        parkingReservationId = "012345678901234567890123456789000201",
                        buildingCode = "000024101",
                        parkingLotCode = "101",
                        reserveStatus = ParkingReservation.Status.RESERVATION,
                        reserveType = ParkingReservation.Type.MANUAL_APPLICATION,
                        requestSource = ParkingReservation.RequestSource.DK_LINK,
                        deleteFlag = "1"
                    ),
                )
            )
        }

        @Test
        @DisplayName("駐車場予約情報が0件取得されること")
        fun case01() {
            val results =
                repository.findActiveParkingReservationByOrderCode(Building.OrderCode.of("0000241"))

            assertEquals(0, results.size)
        }

        @Test
        @DisplayName("駐車場予約情報が複数件取得されること")
        fun case02() {
            val results =
                repository.findActiveParkingReservationByOrderCode(Building.OrderCode.of("0000243"))

            assertEquals(3, results.size)
            assertEquals(Building.Code.of("000024301"), results[0].buildingCode)
            assertEquals(ParkingLot.Code.of("701"), results[0].parkingLotCode)
            assertEquals(ParkingReservation.Status.TENTATIVE, results[0].status)
            assertEquals(ParkingReservation.Type.MANUAL_APPLICATION, results[0].reservationType)
            assertEquals(
                now.plusDays(-1).atTime(LocalTime.of(0, 0, 0)),
                results[0].reserveStartDatetime
            )
            assertEquals(
                now.plusDays(1).atTime(LocalTime.of(23, 59, 59)),
                results[0].reserveEndDatetime
            )
            assertEquals("メモ1", results[0].remarks?.value)
            assertEquals(ParkingReservation.RequestSource.DK_LINK, results[0].requestSource)
            assertEquals("2025/04/23 00:00:01", results[0].updateDateTime)

            assertEquals(Building.Code.of("000024301"), results[1].buildingCode)
            assertEquals(ParkingLot.Code.of("702"), results[1].parkingLotCode)
            assertEquals(ParkingReservation.Status.RESERVATION, results[1].status)
            assertEquals(ParkingReservation.Type.WORK, results[1].reservationType)
            assertEquals(
                now.plusDays(-1).atTime(LocalTime.of(0, 0, 0)),
                results[1].reserveStartDatetime
            )
            assertEquals(
                now.plusDays(1).atTime(LocalTime.of(23, 59, 59)),
                results[1].reserveEndDatetime
            )
            assertEquals("メモ2", results[1].remarks?.value)
            assertEquals(ParkingReservation.RequestSource.DK_LINK, results[1].requestSource)
            assertEquals("2025/04/24 23:59:59", results[1].updateDateTime)

            assertEquals(Building.Code.of("000024302"), results[2].buildingCode)
            assertEquals(ParkingLot.Code.of("101"), results[2].parkingLotCode)
            assertEquals(ParkingReservation.Status.TENTATIVE, results[2].status)
            assertEquals(ParkingReservation.Type.ONE_DAY, results[2].reservationType)
            assertEquals(
                now.plusDays(-1).atTime(LocalTime.of(0, 0, 0)),
                results[2].reserveStartDatetime
            )
            assertEquals(
                now.plusDays(1).atTime(LocalTime.of(23, 59, 59)),
                results[2].reserveEndDatetime
            )
            assertEquals("メモ3", results[2].remarks?.value)
            assertEquals(ParkingReservation.RequestSource.WELCOME_PARK, results[2].requestSource)
            assertNull(results[2].updateDateTime)
        }
    }

    @Nested
    @DisplayName("予約完了消込バッチの検証")
    inner class Scenario4 {

        private val updateReservationID = UUID.randomUUID().toString()

        @BeforeEach
        fun setup() {
            dslContext.saveParkingReservationPojo(
                stubParkingReservationPojo(
                    parkingReservationId = updateReservationID,
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                    reserveStatus = ParkingReservation.Status.RESERVATION,
                    reserveType = ParkingReservation.Type.AUTO_APPLICATION,
                    reserveStartDatetime = LocalDateTime.now().minusDays(1),
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    reserveEndDatetime = null,
                    remarks = "完了消し込み対象の予約",
                ),
                stubParkingReservationPojo(
                    parkingReservationId = UUID.randomUUID().toString(),
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                    reserveStatus = ParkingReservation.Status.RESERVATION,
                    reserveType = ParkingReservation.Type.MANUAL_APPLICATION,
                    reserveStartDatetime = LocalDateTime.now().minusDays(1),
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    reserveEndDatetime = null,
                ),
                stubParkingReservationPojo(
                    parkingReservationId = UUID.randomUUID().toString(),
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = null,
                    reserveStatus = ParkingReservation.Status.RESERVATION,
                    reserveType = ParkingReservation.Type.MANUAL_APPLICATION,
                    reserveStartDatetime = LocalDateTime.now().minusDays(1),
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    reserveEndDatetime = null,
                ),
                stubParkingReservationPojo(
                    parkingReservationId = UUID.randomUUID().toString(),
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                    reserveStatus = ParkingReservation.Status.TENTATIVE,
                    reserveType = ParkingReservation.Type.MANUAL_APPLICATION,
                    reserveStartDatetime = LocalDateTime.now().minusDays(1),
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    reserveEndDatetime = null,
                ),
                stubParkingReservationPojo(
                    parkingReservationId = UUID.randomUUID().toString(),
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                    reserveStatus = ParkingReservation.Status.RESERVATION,
                    reserveType = ParkingReservation.Type.WORK,
                    reserveStartDatetime = LocalDateTime.now().minusDays(1),
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    reserveEndDatetime = null,
                ),
                stubParkingReservationPojo(
                    parkingReservationId = UUID.randomUUID().toString(),
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                    reserveStatus = ParkingReservation.Status.RESERVATION,
                    reserveType = ParkingReservation.Type.MANUAL_APPLICATION,
                    reserveStartDatetime = LocalDateTime.now().minusHours(1),
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    reserveEndDatetime = null,
                ),
                stubParkingReservationPojo(
                    parkingReservationId = UUID.randomUUID().toString(),
                    buildingCode = DEFAULT_BUILDING_CODE,
                    parkingLotCode = DEFAULT_PARKING_LOT_CODE,
                    reserveStatus = ParkingReservation.Status.RESERVATION,
                    reserveType = ParkingReservation.Type.MANUAL_APPLICATION,
                    reserveStartDatetime = LocalDateTime.now().minusDays(1),
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                    reserveEndDatetime = null,
                    deleteFlag = "1"
                )
            )
        }

        @Test
        @DisplayName("完了消し込み対象の予約が抽出されること")
        fun case01() {
            val results =
                repository.findActiveReservationsForBatch()

            assertEquals(2, results.size)
        }

        @Test
        @DisplayName("完了消し込みされること")
        fun case02() {
            val param = FinishParkingReservation.of(
                ParkingReservation.Id.of(updateReservationID),
            )
            repository.finish(AuthInfo.Batch().getRequestUser(), param)

            val result =
                repository.findById(ParkingReservation.Id.of(updateReservationID))
            assertNotNull(result)
            assertEquals(updateReservationID, result?.id?.value)
            assertEquals(ParkingReservation.Status.FINISHED, result?.status)
            assertEquals(
                "完了消し込み対象の予約",
                result?.remarks?.value
            ) // 予約のメモはそのまま残ることを確認
        }
    }
}
