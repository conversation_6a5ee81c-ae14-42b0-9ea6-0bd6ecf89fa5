{"name": "mock-server", "version": "1.0.0", "main": "index.js", "scripts": {"mock": "mocks-server"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.699.0", "@types/uuid": "^9.0.7", "axios": "^1.9.0", "graphql": "^16.10.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-typescript": "^7.26.0", "@mocks-server/main": "^4.1.0", "@mocks-server/plugin-admin-api": "^4.0.1", "@types/node": "^22.14.0", "eslint": "^9.22.0", "prettier": "^3.5.3", "prettier-eslint": "^16.3.0"}}