name: CI

on:
  pull_request:
    branches:
      - main
      - release/4

jobs:
  UnitTest:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    steps:
      # GitHubAppのトークンを作成
      - name: Generate GitHub Apps token
        id: generate
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.REPOSITORY_READ_TOKEN_APP_ID }}
          private-key: ${{ secrets.REPOSITORY_READ_TOKEN_PRIVATE_KEY }}
          repositories: |
            propetech-handover
            propetech-server-handover
      # 自リポジトリからソースをダウンロード
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate.outputs.token }}
          repository: ${{ github.repository }}
      # propetech-handover リポジトリからソースをダウンロード
      - name: Clone dklink-project/propetech-handover repository
        env:
          GITHUB_TOKEN: ${{ steps.generate.outputs.token }}
        run: |
          cd ../
          base_branch=${{ github.base_ref }}
          echo "base_branch: $base_branch"
          if [ "${base_branch}" == "release/4" ]; then
            target_branch="release/4"
          else
            target_branch="main"
          fi
          echo "target_branch: $target_branch"
          git clone --branch $target_branch https://x-access-token:${GITHUB_TOKEN}@github.com/dklink-project/propetech-handover.git
        shell: bash
      # Setup（Java/Gradleの設定及び、Jooq自動生成）
      - name: Setup
        uses: ./.github/workflows/composite/setup
      # テスト実行
      - name: Test
        run: ./gradlew unitTest -x buildTestContainer -x :db:jar
        shell: bash

  IntegrationTest:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    steps:
      # GitHubAppのトークンを作成
      - name: Generate GitHub Apps token
        id: generate
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.REPOSITORY_READ_TOKEN_APP_ID }}
          private-key: ${{ secrets.REPOSITORY_READ_TOKEN_PRIVATE_KEY }}
          repositories: |
            propetech-handover
            propetech-server-handover
      # 自リポジトリからソースをダウンロード
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate.outputs.token }}
          repository: ${{ github.repository }}
      # propetech リポジトリからソースをダウンロード
      - name: Clone dklink-project/propetech-handover repository
        env:
          GITHUB_TOKEN: ${{ steps.generate.outputs.token }}
        run: |
          cd ../
          base_branch=${{ github.base_ref }}
          echo "base_branch: $base_branch"
          if [ "${base_branch}" == "release/4" ]; then
            target_branch="release/4"
          else
            target_branch="main"
          fi
          echo "target_branch: $target_branch"
          git clone --branch $target_branch https://x-access-token:${GITHUB_TOKEN}@github.com/dklink-project/propetech-handover.git
        shell: bash
      # Setup（Java/Gradleの設定及び、Jooq自動生成）
      - name: Setup
        uses: ./.github/workflows/composite/setup
      # テスト実行
      - name: Test
        run: ./gradlew integrationTest -x prepare -x :db:jar
        shell: bash
