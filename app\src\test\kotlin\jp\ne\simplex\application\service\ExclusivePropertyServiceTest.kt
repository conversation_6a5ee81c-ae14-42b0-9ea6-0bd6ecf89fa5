package jp.ne.simplex.application.service

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.AgentRepository
import jp.ne.simplex.application.repository.db.ExclusivePropertyRepository
import jp.ne.simplex.application.repository.db.PropertyRepository
import jp.ne.simplex.db.jooq.gen.tables.references.AGENT
import jp.ne.simplex.db.jooq.gen.tables.references.EXCLUSIVE_PROPERTY
import jp.ne.simplex.db.jooq.gen.tables.references.EXCLUSIVE_PROPERTY_E_CODE
import jp.ne.simplex.db.jooq.gen.tables.references.ROOM_INFO_MASTER
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiServerException
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.mock.*
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.DSLContextEx.Companion.selectExclusivePropertyBy
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.StringExtension.Companion.toBoolean
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertTrue

class ExclusivePropertyServiceTest : AbstractTestContainerTest() {

    private lateinit var service: ExclusivePropertyService

    private val allPassAgentRepository = MockAgentRepository(
        // Eコードの存在チェックにパスするように関数をモック
        listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
    )

    private val allPassPropertyRepository = MockPropertyRepository(
        listFunc = { ids ->
            ids.map { stubProperty(it.buildingCode.value, it.roomCode.value) }
        }
    )

    companion object {
        private val currentDateTime = LocalDateTime.of(2025, 11, 12, 13, 45, 30)
    }

    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(AGENT, EXCLUSIVE_PROPERTY, EXCLUSIVE_PROPERTY_E_CODE, ROOM_INFO_MASTER)
    }

    @AfterEach
    override fun tearDownEach() {
        super.tearDownEach()
        MockLocalDateTime.close()
    }

    @Nested
    @DisplayName("先行公開新規登録シナリオの検証")
    inner class RegisterScenario {

        @Nested
        @DisplayName("トランザクション管理が適切に行えているか確認する")
        inner class TransactionCheck {

            @Test
            @DisplayName("DKポータルへの登録APIに失敗した場合、トランザクションがロールバックされること")
            fun case1() {
                // setup
                val request = stubRegisterExclusiveProperty(
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 100,
                            companyType = ExclusiveProperty.CompanyType.RealEstate,
                            eCode = "*********"
                        ),
                        stubExclusivePropertyTargetWithId(
                            id = 200,
                            companyType = ExclusiveProperty.CompanyType.RealEstate,
                            eCode = "*********"
                        )
                    )
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = MockAgentRepository(
                        // Eコードの存在チェックにパスするように関数をモック
                        listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
                    ),
                    propertyRepository = MockPropertyRepository(
                        // 物件の存在チェックにパスするように関数をモック
                        listFunc = { ids ->
                            ids.map {
                                stubProperty(
                                    it.buildingCode.value, it.roomCode.value, changeDivision = "1",
                                    customerCompletionFlag = false
                                )
                            }
                        }
                    ),
                    dkPortalRepository = MockDKPortalRepository(
                        createExclusiveFunc = { _, record ->
                            if (record.exclusiveTargetWithId.id.value == 100L) {
                                throw ExternalApiServerException(
                                    ErrorType.EBOARD_API_ERROR,
                                    ErrorMessage.DK_PORTAL_RECEIVED_ERROR_RESPONSE.format("DKポータルAPIエラー")
                                )
                            }
                        }
                    ),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                // execute
                val result = service.register(stubJwtAuthInfo(), request)
                assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)
                assertEquals(1, result.failedList.size)
                assertEquals(
                    ExclusiveProperty.ExclusiveTarget(
                        companyType = ExclusiveProperty.CompanyType.RealEstate,
                        eCode = Agent.ECode.of("*********")
                    ),
                    result.failedList.first()
                )

                // verify
                // ID = 100 のリクエストは失敗するため、永続化されたレコードがないことを確認
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).size
                )
                // ID = 200 のリクエストは成功するため、永続化されたレコードがあることを確認
                assertEquals(
                    1,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).size
                )
            }
        }

        @Nested
        @DisplayName("バリデーションチェック")
        inner class ValidationCheck {

            @BeforeEach
            fun setup() {
                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = AgentRepository(dslContext),
                    propertyRepository = PropertyRepository(dslContext),
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )
            }

            @Test
            @DisplayName("不正なEコードが存在する場合、後続処理が実施されず、不正なEコードが返却されること")
            fun case1() {
                // setup
                val data1 = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val data2 = stubExclusivePropertyTargetWithId(
                    id = 200,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val data3 = stubExclusivePropertyTargetWithId(
                    id = 300,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val request = stubRegisterExclusiveProperty(
                    exclusiveTargetWithIds = listOf(data1, data2, data3)
                )




                dslContext.saveAgentPojo(stubAgentPojo("*********"))

                dslContext.saveRoomInfoMasterPojo(
                    stubRoomInfoMasterPojo(
                        buildingCode = request.propertyId.buildingCode.value,
                        roomCode = request.propertyId.roomCode.value,
                        direction = "東"
                    )
                )

                val vacantHouseData1 = stubVacantHouseHpPojo(
                    buildingCode = request.propertyId.buildingCode.value,
                    roomCode = request.propertyId.roomCode.value,
                    changeDivision = "1",
                    customerCompletionFlag = "0"
                )

                dslContext.saveVacantHousePojo(vacantHouseData1)

                // execute
                val result = service.register(stubJwtAuthInfo(), request)

                // verify
                assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)

                // 失敗したレコードの詳細を検証
                assertEquals(2, result.failedList.size)
                assertTrue(result.failedList.map { it.eCode?.value }.contains("*********"))
                assertTrue(result.failedList.map { it.eCode?.value }.contains("*********"))

                // 永続化されたレコードがないことを確認
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).size
                )
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).size
                )
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(300)).size
                )
            }

            @Test
            @DisplayName("存在しない建物CD/部屋CDが指定された場合、後続処理が実施されず、Exceptionがスローされること")
            fun case2() {
                // setup
                val data = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val request = stubRegisterExclusiveProperty(
                    buildingCode = "*********",
                    roomCode = "00000",
                    exclusiveTargetWithIds = listOf(data)
                )

                dslContext.saveAgentPojo(stubAgentPojo("*********"))

                dslContext.saveRoomInfoMasterPojo(
                    stubRoomInfoMasterPojo(
                        buildingCode = "*********",
                        roomCode = "99999",
                    )
                )

                // execute
                assertThrows<ServerValidationException> {
                    service.register(stubJwtAuthInfo(), request)
                }

                // 永続化されたレコードがないことを確認
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).size
                )
            }

            @Test
            @DisplayName("方位未設定の物件を先行公開しようとした時、後続処理が実施されず、Exceptionがスローされること")
            fun case3() {
                // setup
                val data = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val request = stubRegisterExclusiveProperty(
                    buildingCode = "*********",
                    roomCode = "00000",
                    exclusiveTargetWithIds = listOf(data)
                )

                dslContext.saveAgentPojo(stubAgentPojo("*********"))

                dslContext.saveRoomInfoMasterPojo(
                    stubRoomInfoMasterPojo(
                        buildingCode = "*********",
                        roomCode = "99999",
                    )
                )

                // execute
                assertThrows<ServerValidationException> {
                    service.register(stubJwtAuthInfo(), request)
                }

                // 永続化されたレコードがないことを確認
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).size
                )
            }

            @Test
            @DisplayName("仮押さえ中の物件を先行公開しようとした時、後続処理が実施されず、Exceptionがスローされること")
            fun case4() {
                // setup
                val data = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val request = stubRegisterExclusiveProperty(
                    buildingCode = "*********",
                    roomCode = "00000",
                    exclusiveTargetWithIds = listOf(data)
                )

                dslContext.saveAgentPojo(stubAgentPojo("*********"))

                dslContext.saveRoomInfoMasterPojo(
                    stubRoomInfoMasterPojo(
                        buildingCode = "*********",
                        roomCode = "99999",
                    )
                )

                // execute
                assertThrows<ServerValidationException> {
                    ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = AgentRepository(dslContext),
                        propertyRepository = PropertyRepository(dslContext),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(
                            findByFunc = { stubOwnCompanyTemporaryReservationInfo() }
                        ),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    ).register(stubJwtAuthInfo(), request)
                }

                // 永続化されたレコードがないことを確認
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).size
                )
            }

            @Test
            @DisplayName("申込済みの物件を先行公開しようとした時、後続処理が実施されず、Exceptionがスローされること")
            fun case5() {
                // setup
                val data = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val request = stubRegisterExclusiveProperty(
                    buildingCode = "*********",
                    roomCode = "00000",
                    exclusiveTargetWithIds = listOf(data)
                )

                dslContext.saveAgentPojo(stubAgentPojo("*********"))

                dslContext.saveRoomInfoMasterPojo(
                    stubRoomInfoMasterPojo(
                        buildingCode = "*********",
                        roomCode = "99999",
                        recordStatusType = Property.RecordStatusType.FORM_REGISTERED
                    )
                )

                // execute
                assertThrows<ServerValidationException> {
                    service.register(stubJwtAuthInfo(), request)
                }

                // 永続化されたレコードがないことを確認
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).size
                )
            }

            @Test
            @DisplayName("物件に紐づく営業所CDがない場合、後続処理が実施されず、Exceptionがスローされること")
            fun case6() {
                // setup
                val data = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val request = stubRegisterExclusiveProperty(
                    buildingCode = "*********",
                    roomCode = "00000",
                    exclusiveTargetWithIds = listOf(data)
                )

                dslContext.saveAgentPojo(stubAgentPojo("*********"))

                dslContext.saveRoomInfoMasterPojo(
                    stubRoomInfoMasterPojo(
                        buildingCode = "*********",
                        roomCode = "00000",
                        marketingBranchOfficeCd = null
                    )
                )

                // execute
                assertThrows<ServerValidationException> {
                    service.register(stubJwtAuthInfo(), request)
                }

                // 永続化されたレコードがないことを確認
                assertEquals(
                    0,
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).size
                )

            }

            @Nested
            @DisplayName("営業所によるバリデーションチェック")
            inner class Scenario2 {

                @BeforeEach
                fun setup() {
                    service = ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = MockAgentRepository(
                            // Eコードの存在チェックにパスするように関数をモック
                            listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
                        ),
                        propertyRepository = MockPropertyRepository(
                            listFunc = { ids ->
                                ids.map { stubProperty(it.buildingCode.value, it.roomCode.value) }
                            }
                        ),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    )
                }

                @Test
                @DisplayName("営業所Aに所属のユーザーが、営業所Aによって公開されている物件を先行公開しようとした場合、公開できること")
                fun case0() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo(businessOfficeCode = "645")
                    val request = stubRegisterExclusiveProperty(
                        exclusiveTo = "20240203".yyyyMMdd(),
                        exclusiveFrom = "20240201".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(stubExclusivePropertyTargetWithId())
                    )

                    // 他営業所で先行公開されている物件を登録
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            salesOfficeCode = "645",
                            exclusiveFrom = 20240101,
                            exclusiveTo = 20240103,
                        )
                    )

                    service = ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = MockPropertyRepository(
                            // 物件の存在チェックにパスするように関数をモック
                            listFunc = { ids ->
                                ids.map {
                                    stubProperty(
                                        it.buildingCode.value,
                                        it.roomCode.value,
                                        changeDivision = "1",
                                        customerCompletionFlag = false
                                    )
                                }
                            }
                        ),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    )

                    // execute
                    assertDoesNotThrow {
                        val result = service.register(requestAuthInfo, request)

                        assertEquals(ExclusivePropertyAction.Status.SUCCESS, result.status)
                        assertEquals(null, result.reason)
                    }

                    // 永続化されたレコードが存在することを確認
                    assertEquals(
                        1, dslContext.selectExclusivePropertyBy(
                            stubExclusivePropertyTargetWithId().id
                        ).size
                    )
                }

                @Test
                @DisplayName("営業所Aに所属のユーザーが、営業所Bによって公開されている物件を先行公開しようとした場合、公開できないこと")
                fun case1() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo(businessOfficeCode = "645")
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = "20240201".yyyyMMdd(),
                        exclusiveTo = "20240203".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(stubExclusivePropertyTargetWithId())
                    )

                    // 他営業所で先行公開されている物件を登録
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            salesOfficeCode = "123",
                            exclusiveFrom = 20240101,
                            exclusiveTo = 20240103,
                        )
                    )

                    // execute
                    assertThrows<ServerValidationException> {
                        val result = service.register(requestAuthInfo, request)

                        assertEquals(
                            ExclusivePropertyAction.StatusReason.INVALID_ECODE,
                            result.reason
                        )
                    }

                    // 永続化されたレコードがないことを確認
                    assertEquals(
                        0, dslContext.selectExclusivePropertyBy(
                            stubExclusivePropertyTargetWithId(
                                id = 111,
                            ).id
                        ).size
                    )
                }

                @Test
                @DisplayName("営業所Aに所属のユーザーが、本社によって公開されている物件を先行公開しようとした場合、公開できないこと")
                fun case2() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo(businessOfficeCode = "645")
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = "20240201".yyyyMMdd(),
                        exclusiveTo = "20240203".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(stubExclusivePropertyTargetWithId())
                    )

                    // 他営業所で先行公開されている物件を登録
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            // 本社ユーザーが先行公開した場合、OfficeCodeはnullになる
                            salesOfficeCode = null,
                            exclusiveFrom = 20240101,
                            exclusiveTo = 20240103,
                        )
                    )

                    // execute
                    assertThrows<ServerValidationException> {
                        val result = service.register(requestAuthInfo, request)

                        assertEquals(
                            ExclusivePropertyAction.StatusReason.INVALID_ECODE,
                            result.reason
                        )
                    }

                    // 永続化されたレコードがないことを確認
                    assertEquals(
                        0, dslContext.selectExclusivePropertyBy(
                            stubExclusivePropertyTargetWithId().id
                        ).size
                    )
                }

                @Test
                @DisplayName("本社ユーザーが、本社によって公開されている物件を先行公開しようとした場合、公開できること")
                fun case3() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo(businessOfficeCode = null)
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = "20240201".yyyyMMdd(),
                        exclusiveTo = "20240203".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(stubExclusivePropertyTargetWithId())
                    )

                    // 他営業所で先行公開されている物件を登録
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            // 本社ユーザーが先行公開した場合、OfficeCodeはnullになる
                            salesOfficeCode = null,
                            exclusiveFrom = 20240101,
                            exclusiveTo = 20240103,
                        )
                    )

                    service = ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = MockPropertyRepository(
                            // 物件の存在チェックにパスするように関数をモック
                            listFunc = { ids ->
                                ids.map {
                                    stubProperty(
                                        it.buildingCode.value,
                                        it.roomCode.value,
                                        changeDivision = "1",
                                        customerCompletionFlag = false
                                    )
                                }
                            }
                        ),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    )

                    // execute
                    assertDoesNotThrow {
                        val result = service.register(requestAuthInfo, request)

                        assertEquals(ExclusivePropertyAction.Status.SUCCESS, result.status)
                        assertEquals(null, result.reason)
                    }

                    // 永続化されたレコードが存在することを確認
                    assertEquals(
                        1, dslContext.selectExclusivePropertyBy(
                            stubExclusivePropertyTargetWithId().id
                        ).size
                    )
                }

                @Test
                @DisplayName("本社ユーザーが、営業所Bによって公開されている物件を先行公開しようとした場合、公開できないこと")
                fun case4() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo(businessOfficeCode = null)
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = "20240201".yyyyMMdd(),
                        exclusiveTo = "20240203".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(stubExclusivePropertyTargetWithId())
                    )
                    // 他営業所で先行公開されている物件を登録
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            salesOfficeCode = "123",
                            exclusiveFrom = 20240101,
                            exclusiveTo = 20240103,
                        )
                    )

                    // execute
                    assertThrows<ServerValidationException> {
                        val result = service.register(requestAuthInfo, request)

                        assertEquals(
                            ExclusivePropertyAction.StatusReason.INVALID_ECODE,
                            result.reason
                        )
                    }

                    // 永続化されたレコードが存在しないことを確認
                    assertEquals(
                        0, dslContext.selectExclusivePropertyBy(
                            stubExclusivePropertyTargetWithId().id
                        ).size
                    )
                }
            }

            @Nested
            @DisplayName("公開指示済かどうかのバリデーションチェック")
            inner class Scenario3 {

                @Test
                @DisplayName("物件が公開指示済でない場合、正常なリクエストととして処理されること")
                fun case1() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo()
                    val requestDetails = stubExclusivePropertyTargetWithId(
                        id = 111,
                        companyType = ExclusiveProperty.CompanyType.HouseCom,
                    )
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = "20250201".yyyyMMdd(),
                        exclusiveTo = "20250203".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(requestDetails)
                    )

                    // execute
                    assertDoesNotThrow {
                        val result = ExclusivePropertyService(
                            dslContext = dslContext,
                            repository = ExclusivePropertyRepository(dslContext),
                            agentRepository = MockAgentRepository(
                                // Eコードの存在チェックにパスするように関数をモック
                                listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
                            ),
                            propertyRepository = MockPropertyRepository(
                                listFunc = { ids ->
                                    ids.map {
                                        stubProperty(
                                            it.buildingCode.value,
                                            it.roomCode.value,
                                            changeDivision = "1",
                                            customerCompletionFlag = false
                                        )
                                    }
                                }
                            ),
                            dkPortalRepository = MockDKPortalRepository(),
                            temporaryReservationRepository = MockTemporaryReservationRepository(),
                            propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                                dslContext,
                                listByFunc = { ids ->
                                    listOf(
                                        stubPropertyMaintenanceInfo(
                                            publishStatus = PropertyMaintenance.PublishStatus.PUBLIC
                                        )
                                    )
                                }
                            ),
                            employeeRepository = MockEmployeeRepository()
                        ).register(requestAuthInfo, request)

                        assertEquals(ExclusivePropertyAction.Status.SUCCESS, result.status)
                        assertEquals(null, result.reason)
                    }

                    // 永続化されたレコードが存在することを確認
                    assertEquals(1, dslContext.selectExclusivePropertyBy(requestDetails.id).size)
                }

                @Test
                @DisplayName("物件が先行公開済の場合、不正なリクエストととして処理されること")
                fun case2() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo()
                    val requestTargetWithId = stubExclusivePropertyTargetWithId(
                        id = 111,
                        companyType = ExclusiveProperty.CompanyType.HouseCom,
                    )
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = LocalDate.now().plusDays(10),
                        exclusiveTo = LocalDate.now().plusDays(20),
                        exclusiveTargetWithIds = listOf(requestTargetWithId)
                    )

                    // 公開済みのレコードをINSERT
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            salesOfficeCode = requestAuthInfo.businessOfficeCode?.value,
                            exclusiveFrom = LocalDate.now().minusDays(1).yyyyMMdd().toInt(),
                            exclusiveTo = LocalDate.now().plusDays(1).yyyyMMdd().toInt(),
                            companyType = ExclusiveProperty.CompanyType.HouseCom.value.toByte(),
                        )
                    )

                    // execute
                    val err = assertThrows<ServerValidationException> {
                        ExclusivePropertyService(
                            dslContext = dslContext,
                            repository = ExclusivePropertyRepository(dslContext),
                            agentRepository = MockAgentRepository(
                                // Eコードの存在チェックにパスするように関数をモック
                                listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
                            ),
                            propertyRepository = MockPropertyRepository(
                                listFunc = { ids ->
                                    ids.map {
                                        stubProperty(
                                            it.buildingCode.value,
                                            it.roomCode.value,
                                            changeDivision = "1",
                                            customerCompletionFlag = false
                                        )
                                    }
                                }
                            ),
                            dkPortalRepository = MockDKPortalRepository(),
                            temporaryReservationRepository = MockTemporaryReservationRepository(),
                            propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                                dslContext,
                                findByFunc = { id ->
                                    stubPropertyMaintenanceInfo(
                                        publishStatus = PropertyMaintenance.PublishStatus.PUBLIC
                                    )
                                }
                            ),
                            employeeRepository = MockEmployeeRepository()
                        ).register(requestAuthInfo, request)
                    }

                    kotlin.test.assertEquals(
                        err.detail.message,
                        ErrorMessage.REGISTER_EXCLUSIVE_PROPERTY_NOT_ALLOWED.format().message
                    )

                    // 永続化されたレコードが存在しないことを確認
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(requestTargetWithId.id).size
                    )
                }
            }

            @Nested
            @DisplayName("先行公開期間のバリデーションチェック")
            inner class Scenario5 {

                @BeforeEach
                fun setup() {
                    service = ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = MockAgentRepository(
                            // Eコードの存在チェックにパスするように関数をモック
                            listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
                        ),
                        propertyRepository = MockPropertyRepository(
                            listFunc = { ids ->
                                ids.map { stubProperty(it.buildingCode.value, it.roomCode.value) }
                            }
                        ),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    )
                }

                @Test
                @DisplayName("異なる先行先に対して、期間が重複している場合、正常なリクエストととして処理されること")
                fun case1() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo()
                    val requestDetails = stubExclusivePropertyTargetWithId(
                        id = 111,
                        companyType = ExclusiveProperty.CompanyType.HouseCom,
                    )
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = "20240201".yyyyMMdd(),
                        exclusiveTo = "20240203".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(requestDetails)
                    )

                    // 他営業所で先行公開されている物件を登録
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            salesOfficeCode = requestAuthInfo.businessOfficeCode?.value,
                            exclusiveFrom = request.exclusiveRange.to.yyyyMMdd().toInt(),
                            exclusiveTo = request.exclusiveRange.to.plusDays(1).yyyyMMdd()
                                .toInt(),
                            companyType = ExclusiveProperty.CompanyType.Leasing.value.toByte(),
                        )
                    )

                    service = ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = MockPropertyRepository(
                            // 物件の存在チェックにパスするように関数をモック
                            listFunc = { ids ->
                                ids.map {
                                    stubProperty(
                                        it.buildingCode.value,
                                        it.roomCode.value,
                                        changeDivision = "1",
                                        customerCompletionFlag = false
                                    )
                                }
                            }
                        ),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    )

                    // execute
                    assertDoesNotThrow {
                        val result = service.register(requestAuthInfo, request)

                        assertEquals(ExclusivePropertyAction.Status.SUCCESS, result.status)
                        assertEquals(null, result.reason)
                    }

                    // 永続化されたレコードが存在することを確認
                    assertEquals(1, dslContext.selectExclusivePropertyBy(requestDetails.id).size)
                }

                @Test
                @DisplayName("同じ先行先に対して、不正なリクエストととして処理されること")
                fun case2() {
                    // setup
                    val requestAuthInfo = stubJwtAuthInfo()
                    val requestTargetWithId = stubExclusivePropertyTargetWithId(
                        id = 111,
                        companyType = ExclusiveProperty.CompanyType.HouseCom,
                    )
                    val request = stubRegisterExclusiveProperty(
                        exclusiveFrom = "20260201".yyyyMMdd(),
                        exclusiveTo = "20260203".yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(requestTargetWithId)
                    )

                    // 他営業所で先行公開されている物件を登録
                    dslContext.saveExclusivePropertyPojo(
                        stubExclusivePropertyPojo(
                            id = 222,
                            buildingCode = request.propertyId.buildingCode.value,
                            roomCode = request.propertyId.roomCode.value,
                            salesOfficeCode = requestAuthInfo.businessOfficeCode?.value,
                            exclusiveFrom = request.exclusiveRange.to.yyyyMMdd().toInt(),
                            exclusiveTo = request.exclusiveRange.to.plusDays(1).yyyyMMdd()
                                .toInt(),
                            companyType = ExclusiveProperty.CompanyType.HouseCom.value.toByte(),
                        )
                    )

                    service = ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = MockAgentRepository(
                            // Eコードの存在チェックにパスするように関数をモック
                            listByFunc = { eCodeList -> eCodeList.map { stubAgent(it.value) } }
                        ),
                        propertyRepository = MockPropertyRepository(
                            // 物件の存在チェックにパスするように関数をモック
                            listFunc = { ids ->
                                ids.map {
                                    stubProperty(
                                        it.buildingCode.value,
                                        it.roomCode.value,
                                        changeDivision = "1",
                                        customerCompletionFlag = false
                                    )
                                }
                            }
                        ),
                        dkPortalRepository = MockDKPortalRepository(
                            createExclusiveFunc = { _, record ->
                                if (record.exclusiveTargetWithId.id.value == 100L) {
                                    throw ExternalApiServerException(
                                        ErrorType.EBOARD_API_ERROR,
                                        ErrorMessage.DK_PORTAL_RECEIVED_ERROR_RESPONSE.format("DKポータルAPIエラー")
                                    )
                                }
                            }
                        ),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    )

                    // execute
                    assertDoesNotThrow {
                        val result = service.register(requestAuthInfo, request)

                        assertEquals(
                            ExclusivePropertyAction.StatusReason.EXCLUSIVE_TARGET_DUPLICATE,
                            result.reason
                        )
                    }

                    // 永続化されたレコードが存在しないことを確認
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(requestTargetWithId.id).size
                    )
                }
            }
        }
    }

    @Nested
    @DisplayName("先行公開修正シナリオの検証")
    inner class UpdateScenario {

        @Nested
        @DisplayName("トランザクション管理が適切に行えているか確認する")
        inner class TransactionCheck {

            val now = LocalDateTime.of(2025, 3, 15, 12, 0)!!
            val propertyId = Property.Id(
                buildingCode = Building.Code.of("*********"),
                roomCode = Room.Code.of("R0001"),
            )
            val pojo = stubExclusivePropertyPojo(
                id = *********012345678,
                buildingCode = propertyId.buildingCode.value,
                roomCode = propertyId.roomCode.value,
                salesOfficeCode = "SO1",
                exclusiveFrom = 20250401,
                exclusiveTo = 20250407,
                companyType = 1,
                creationDate = 20250301,
                creationTime = 230000,
                creator = "USERA",
                updateDate = 20250301,
                updateTime = 230000,
                updater = "USERA",
                deleteFlag = "0",
            )

            val occurredException = ExternalApiServerException(
                ErrorType.DK_PORTAL_API_ERROR,
                ErrorMessage.UNEXPECTED_ERROR.format()
            )

            @BeforeEach
            fun setup() {
                MockLocalDateTime.setNow(now)
                dslContext.saveExclusivePropertyPojo(pojo)
                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(
                        createExclusiveFunc = { _, _ -> throw occurredException },
                        updateExclusiveFunc = { _, _, _ -> throw occurredException },
                        deleteExclusiveFunc = { _, _ -> throw occurredException },
                    ),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )
            }

            @AfterEach
            fun tearDown() {
                MockLocalDateTime.close()
            }

            @Test
            @DisplayName("先行期間のみの先行公開修正の場合、DKポータルへの修正APIに失敗した場合、トランザクションがロールバックされること")
            fun case1() {
                val updateExclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd().plusDays(1)
                val updateExclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd().plusDays(3)

                val request = stubUpdateExclusiveProperty(
                    id = pojo.id,
                    exclusiveFrom = updateExclusiveFrom,
                    exclusiveTo = updateExclusiveTo,
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 100,
                            companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                        )
                    )
                )

                // execute
                val result = service.update(stubJwtAuthInfo(), request)
                assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)

                // verify
                // 先行期間が更新されていないことを確認
                val record = dslContext.selectExclusivePropertyBy(propertyId).first()
                assertEquals(pojo.exclusiveFrom, record.exclusiveFrom)
                assertEquals(pojo.exclusiveTo, record.exclusiveTo)
            }

            @Test
            @DisplayName("先行先が追加されている場合、DKポータルへの修正/登録APIに失敗した場合、トランザクションがロールバックされること")
            fun case2() {
                // setup
                val updateExclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd().plusDays(1)
                val updateExclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd().plusDays(3)

                val request = stubUpdateExclusiveProperty(
                    id = pojo.id,
                    exclusiveFrom = updateExclusiveFrom,
                    exclusiveTo = updateExclusiveTo,
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 100,
                            companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                        ),
                        stubExclusivePropertyTargetWithId(
                            id = 200,
                            companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                            eCode = "E87654321"
                        ),
                    )
                )

                // execute
                service.update(stubJwtAuthInfo(), request)

                // verify
                // 外部APIが成功した場合は対象物件に対してレコードは計2件（更新レコード + 新規レコード）できるが、
                // ロールバックされるので、既存の1レコードしかないのが期待値
                assertEquals(1, dslContext.selectExclusivePropertyBy(propertyId).size)

                // 先行期間が更新されていないことを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                val record = dslContext.selectExclusivePropertyBy(request.id).first()
                assertEquals(pojo.companyType, record.companyType)
                assertEquals(pojo.exclusiveFrom, record.exclusiveFrom)
                assertEquals(pojo.exclusiveTo, record.exclusiveTo)
            }

            @Test
            @DisplayName("先行先が変更されている場合、DKポータルへの削除/登録APIに失敗した場合、全てのトランザクションがロールバックされること")
            fun case3() {
                // setup
                val updateExclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd().plusDays(1)
                val updateExclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd().plusDays(3)

                val target1 = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.Leasing,
                )
                val target2 = stubExclusivePropertyTargetWithId(
                    id = 200,
                    companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                    eCode = "E87654321"
                )
                val request = stubUpdateExclusiveProperty(
                    id = pojo.id,
                    exclusiveFrom = updateExclusiveFrom,
                    exclusiveTo = updateExclusiveTo,
                    exclusiveTargetWithIds = listOf(target1, target2),
                )

                // execute
                service.update(stubJwtAuthInfo(), request)

                // verify
                // 外部APIが成功した場合は対象物件に対してレコードは計3件（削除レコード + 新規レコードx2）できるが、
                // ロールバックされるので、既存の1レコードしかないのが期待値
                assertEquals(1, dslContext.selectExclusivePropertyBy(propertyId).size)

                // 先行期間が更新されていないことを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                val record = dslContext.selectExclusivePropertyBy(request.id).first()
                assertEquals(pojo.companyType, record.companyType)
                assertEquals(pojo.exclusiveFrom, record.exclusiveFrom)
                assertEquals(pojo.exclusiveTo, record.exclusiveTo)
            }
        }

        @Nested
        @DisplayName("バリデーションチェック")
        inner class ValidationCheck {

            @Test
            @DisplayName("指定された先行公開に紐づく物件が存在しない場合、Exceptionがスローされること")
            fun case0() {
                val data1 = stubExclusivePropertyPojo(
                    id = 100,
                    buildingCode = "*********",
                    roomCode = "01010",
                    exclusiveFrom = 20251115,
                    exclusiveTo = 20251120,
                    companyType = ExclusiveProperty.CompanyType.Leasing.value.toByte(),
                )
                val request = stubUpdateExclusiveProperty(
                    id = data1.id,
                    exclusiveFrom = data1.exclusiveFrom.toString().yyyyMMdd(),
                    exclusiveTo = data1.exclusiveTo.toString().yyyyMMdd(),
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 200,
                            companyType = ExclusiveProperty.CompanyType.RealEstate,
                            eCode = "E12345678",
                        ),
                        stubExclusivePropertyTargetWithId(
                            id = 300,
                            companyType = ExclusiveProperty.CompanyType.Leasing,
                        ),
                    )
                )

                // setup
                dslContext.saveExclusivePropertyPojo(data1)
                dslContext.saveRoomInfoMasterPojo() // RoomInfoMasterに何も保存しない

                // execute
                assertThrows<ServerValidationException> {
                    ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = PropertyRepository(dslContext),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    ).update(stubJwtAuthInfo(), request)
                }
            }

            @Test
            @DisplayName("指定された先行公開IDが不正(DBに存在しない)な場合、Exceptionがスローされること")
            fun case1() {
                // setup
                val requestTargetWithId = stubExclusivePropertyTargetWithId(
                    companyType = ExclusiveProperty.CompanyType.RealEstate,
                    eCode = "*********"
                )
                val request = stubUpdateExclusiveProperty(
                    id = 100,
                    exclusiveTargetWithIds = listOf(requestTargetWithId)
                )

                dslContext.saveAgentPojo(stubAgentPojo("*********"))

                // execute
                assertThrows<ServerValidationException> {
                    ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = allPassPropertyRepository,
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    ).update(stubJwtAuthInfo(), request)
                }

                // 永続化されたレコードがないことを確認
                assertEquals(0, dslContext.selectExclusivePropertyBy(request.id).size)
            }

            @Test
            @DisplayName("修正対象の先行公開が、既に終了日をすぎている場合、Exceptionがスローされること")
            fun case2() {
                // setup
                val now = LocalDateTime.of(2024, 4, 18, 0, 0)
                MockLocalDateTime.setNow(now)

                val request = stubUpdateExclusiveProperty(
                    id = 100,
                    exclusiveFrom = now.minusDays(7).toLocalDate(),
                    exclusiveTo = now.plusDays(7).toLocalDate(),
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 200,
                            companyType = ExclusiveProperty.CompanyType.Leasing,
                        ),
                    )
                )

                val pojo = stubExclusivePropertyPojo(
                    id = request.id.value,
                    exclusiveFrom = now.minusDays(7).yyyyMMdd().toInt(),
                    exclusiveTo = now.minusDays(1).yyyyMMdd().toInt(),
                    companyType = ExclusiveProperty.CompanyType.Leasing.value.toByte(),
                )

                dslContext.saveExclusivePropertyPojo(pojo)

                // execute
                assertThrows<ServerValidationException> {
                    ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = allPassPropertyRepository,
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    ).update(stubJwtAuthInfo(), request)
                }

                // レコードが更新されていないことを確認
                val result = dslContext.selectExclusivePropertyBy(request.id)
                assertEquals(pojo.exclusiveFrom, result.first().exclusiveFrom)
                assertEquals(pojo.exclusiveTo, result.first().exclusiveTo)

                MockLocalDateTime.close()
            }

            @Nested
            @DisplayName("修正対象の先行公開が、既に開始日付をすぎている場合")
            inner class Scenario2x1 {

                val now = LocalDateTime.of(2024, 4, 18, 0, 0)!!

                val pojo = stubExclusivePropertyPojo(
                    id = 100,
                    buildingCode = "*********",
                    roomCode = "01010",
                    companyType = ExclusiveProperty.CompanyType.RealEstate.value.toByte(),
                    exclusiveFrom = 20240415,
                    exclusiveTo = 20240425,
                )

                val eCodePojo = stubExclusivePropertyECodePojo(
                    id = pojo.id,
                    eCode = "E12345678",
                )

                @BeforeEach
                fun beforeEach() {
                    MockLocalDateTime.setNow(now)
                    dslContext.saveExclusivePropertyPojo(pojo)
                    dslContext.saveExclusivePropertyECodePojo(eCodePojo)
                    service = ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = allPassPropertyRepository,
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    )
                }

                @AfterEach
                fun afterEach() {
                    MockLocalDateTime.close()
                }

                @Test
                @DisplayName("先行先種別が追加されている場合は、Exceptionがスローされること")
                fun case1() {
                    // setup
                    val request = stubUpdateExclusiveProperty(
                        id = pojo.id,
                        exclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd(),
                        exclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(
                            stubExclusivePropertyTargetWithId(
                                id = 200,
                                companyType = ExclusiveProperty.CompanyType.RealEstate,
                                eCode = eCodePojo.eCode
                            ),
                            stubExclusivePropertyTargetWithId(
                                id = 300,
                                companyType = ExclusiveProperty.CompanyType.HouseCom,
                            )
                        )
                    )

                    // execute
                    assertThrows<ServerValidationException> {
                        service.update(stubJwtAuthInfo(), request)
                    }

                    // 永続化されたレコードがないことを確認
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).size
                    )
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(300)).size
                    )
                }

                @Test
                @DisplayName("先行先種別が変更されている場合は、Exceptionがスローされること")
                fun case2() {
                    // setup
                    val request = stubUpdateExclusiveProperty(
                        id = pojo.id,
                        exclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd(),
                        exclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(
                            stubExclusivePropertyTargetWithId(
                                id = 300,
                                companyType = ExclusiveProperty.CompanyType.HouseCom,
                            )
                        )
                    )

                    // execute
                    assertThrows<ServerValidationException> {
                        service.update(stubJwtAuthInfo(), request)
                    }

                    // 永続化されたレコードがないことを確認
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(300)).size
                    )
                }

                @Test
                @DisplayName("Eコードが追加されている場合は、Exceptionがスローされること")
                fun case4() {
                    // setup
                    val request = stubUpdateExclusiveProperty(
                        id = pojo.id,
                        exclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd(),
                        exclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(
                            stubExclusivePropertyTargetWithId(
                                id = 200,
                                companyType = ExclusiveProperty.CompanyType.RealEstate,
                                eCode = eCodePojo.eCode
                            ),
                            stubExclusivePropertyTargetWithId(
                                id = 300,
                                companyType = ExclusiveProperty.CompanyType.RealEstate,
                                eCode = "E87654321"
                            )
                        )
                    )

                    // execute
                    assertThrows<ServerValidationException> {
                        service.update(stubJwtAuthInfo(), request)
                    }

                    // 永続化されたレコードがないことを確認
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).size
                    )
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(300)).size
                    )
                }

                @Test
                @DisplayName("先行期間の開始日付が更新されている場合は、Exceptionがスローされること")
                fun case5() {
                    // setup
                    val request = stubUpdateExclusiveProperty(
                        id = pojo.id,
                        exclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd().minusDays(1),
                        exclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(
                            stubExclusivePropertyTargetWithId(
                                id = 200,
                                companyType = ExclusiveProperty.CompanyType.RealEstate,
                                eCode = eCodePojo.eCode
                            ),
                        )
                    )

                    // execute
                    assertThrows<ServerValidationException> {
                        service.update(stubJwtAuthInfo(), request)
                    }

                    // レコードが更新されていないことを確認
                    val result =
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(pojo.id))
                            .first()
                    assertEquals(pojo.exclusiveFrom, result.exclusiveFrom)
                }

                @Test
                @DisplayName("先行期間の終了日付のみ更新されている場合、適切に更新できること")
                fun case6() {
                    // setup
                    val request = stubUpdateExclusiveProperty(
                        id = pojo.id,
                        exclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd(),
                        exclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd().plusDays(3),
                        exclusiveTargetWithIds = listOf(
                            stubExclusivePropertyTargetWithId(
                                id = 200,
                                companyType = ExclusiveProperty.CompanyType.RealEstate,
                                eCode = eCodePojo.eCode
                            ),
                        )
                    )

                    // execute
                    assertDoesNotThrow {
                        service.update(stubJwtAuthInfo(), request)
                    }

                    // レコードが更新されていることを確認
                    val result = dslContext.selectExclusivePropertyBy(request.id).first()
                    assertEquals(pojo.exclusiveFrom, result.exclusiveFrom)
                    assertEquals(request.exclusiveRange.to.yyyyMMdd().toInt(), result.exclusiveTo)
                }
            }

            @Nested
            @DisplayName("修正対象の先行公開が、開始日付をすぎていない場合")
            inner class Scenario2x2 {
                val now = LocalDateTime.of(2024, 4, 12, 0, 0)!!

                val pojo = stubExclusivePropertyPojo(
                    id = 100,
                    buildingCode = "*********",
                    roomCode = "01010",
                    companyType = ExclusiveProperty.CompanyType.Leasing.value.toByte(),
                    exclusiveFrom = 20240415,
                    exclusiveTo = 20240425,
                )

                @BeforeEach
                fun beforeEach() {
                    MockLocalDateTime.setNow(now)
                    dslContext.saveExclusivePropertyPojo(pojo)
                }

                @AfterEach
                fun afterEach() {
                    MockLocalDateTime.close()
                }

                @Test
                @DisplayName("不正なEコードが存在する場合、後続処理が実施されず、不正なEコードが返却されること")
                fun case1() {
                    // setup
                    val request = stubUpdateExclusiveProperty(
                        id = pojo.id,
                        exclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd(),
                        exclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(
                            stubExclusivePropertyTargetWithId(
                                id = 200,
                                companyType = ExclusiveProperty.CompanyType.RealEstate,
                                eCode = "E12345678"
                            ),
                        )
                    )

                    // execute
                    assertDoesNotThrow {
                        val result = ExclusivePropertyService(
                            dslContext = dslContext,
                            repository = ExclusivePropertyRepository(dslContext),
                            agentRepository = AgentRepository(dslContext),
                            propertyRepository = allPassPropertyRepository,
                            dkPortalRepository = MockDKPortalRepository(),
                            temporaryReservationRepository = MockTemporaryReservationRepository(),
                            propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                                dslContext
                            ),
                            employeeRepository = MockEmployeeRepository()
                        ).update(stubJwtAuthInfo(), request)
                        assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)
                        assertEquals(
                            ExclusivePropertyAction.StatusReason.INVALID_ECODE,
                            result.reason
                        )
                    }

                    // 永続化されたレコードがないことを確認
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).size
                    )
                }

                @Test
                @DisplayName("先行先が重複する場合、後続処理が実施されず、不正なリクエストとして判断されること")
                fun case2() {
                    // setup
                    val pojo2 = stubExclusivePropertyPojo(
                        id = 101,
                        buildingCode = pojo.buildingCode,
                        roomCode = pojo.roomCode,
                        companyType = ExclusiveProperty.CompanyType.Leasing.value.toByte(),
                        exclusiveFrom = 20240515,
                        exclusiveTo = 20240525,
                    )
                    dslContext.saveExclusivePropertyPojo(pojo2)

                    val request = stubUpdateExclusiveProperty(
                        id = pojo.id,
                        exclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd(),
                        exclusiveTo = pojo2.exclusiveTo.toString().yyyyMMdd(),
                        exclusiveTargetWithIds = listOf(
                            stubExclusivePropertyTargetWithId(
                                id = 200,
                                companyType = ExclusiveProperty.CompanyType.Leasing,
                            ),
                        )
                    )

                    // execute
                    assertDoesNotThrow {
                        val result = ExclusivePropertyService(
                            dslContext = dslContext,
                            repository = ExclusivePropertyRepository(dslContext),
                            agentRepository = allPassAgentRepository,
                            propertyRepository = allPassPropertyRepository,
                            dkPortalRepository = MockDKPortalRepository(),
                            temporaryReservationRepository = MockTemporaryReservationRepository(),
                            propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                                dslContext
                            ),
                            employeeRepository = MockEmployeeRepository()
                        ).update(stubJwtAuthInfo(), request)
                        assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)
                        assertEquals(
                            ExclusivePropertyAction.StatusReason.EXCLUSIVE_TARGET_DUPLICATE,
                            result.reason
                        )
                    }

                    // 永続化されたレコードがないことを確認
                    assertEquals(
                        0,
                        dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).size
                    )
                }
            }
        }

        @Nested
        @DisplayName("正常系のユースケース確認")
        inner class NormalScenario {
            val now = LocalDateTime.of(2025, 3, 15, 12, 0)!!
            val propertyId = Property.Id(
                buildingCode = Building.Code.of("*********"),
                roomCode = Room.Code.of("R0001"),
            )
            val pojo = stubExclusivePropertyPojo(
                id = *********012345678,
                buildingCode = propertyId.buildingCode.value,
                roomCode = propertyId.roomCode.value,
                salesOfficeCode = "SO1",
                exclusiveFrom = 20250401,
                exclusiveTo = 20250407,
                companyType = 0,
                creationDate = 20250301,
                creationTime = 230000,
                creator = "USERA",
                updateDate = 20250301,
                updateTime = 230000,
                updater = "USERA",
                deleteFlag = "0",
            )
            val eCodePojo = stubExclusivePropertyECodePojo(
                id = pojo.id,
                eCode = "E12345678",
                creationDate = pojo.creationDate,
                creationTime = pojo.creationTime,
                creator = pojo.creator,
                updateDate = pojo.updateDate,
                updateTime = pojo.updateTime,
                updater = pojo.updater,
                deleteFlag = "0",
            )

            var dkPortalRegisterApiCalledCount = 0
            var dkPortalUpdateApiCalledCount = 0
            var dkPortalDeleteApiCalledCount = 0

            @BeforeEach
            fun setup() {
                MockLocalDateTime.setNow(now)
                dslContext.saveExclusivePropertyPojo(pojo)
                dslContext.saveExclusivePropertyECodePojo(eCodePojo)
                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(
                        createExclusiveFunc = { _, _ -> dkPortalRegisterApiCalledCount++ },
                        updateExclusiveFunc = { _, _, _ -> dkPortalUpdateApiCalledCount++ },
                        deleteExclusiveFunc = { _, _ -> dkPortalDeleteApiCalledCount++ },
                    ),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )
            }

            @AfterEach
            fun tearDown() {
                MockLocalDateTime.close()
                dkPortalRegisterApiCalledCount = 0
                dkPortalUpdateApiCalledCount = 0
                dkPortalDeleteApiCalledCount = 0
            }

            @Test
            @DisplayName("先行期間のみが更新されている場合、既存レコードが更新され、DKポータルの更新APIがコールされること")
            fun case1() {
                // setup
                val updateExclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd().plusDays(1)
                val updateExclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd().plusDays(3)

                val request = stubUpdateExclusiveProperty(
                    id = pojo.id,
                    exclusiveFrom = updateExclusiveFrom,
                    exclusiveTo = updateExclusiveTo,
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 100,
                            companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                            eCode = eCodePojo.eCode
                        )
                    )
                )

                // execute
                service.update(stubJwtAuthInfo(), request)

                // verify
                val result = dslContext.selectExclusivePropertyBy(propertyId)
                assertEquals(1, result.size)

                // 先行期間が更新されていることを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                val record = result.first()
                assertEquals(updateExclusiveFrom.yyyyMMdd().toInt(), record.exclusiveFrom)
                assertEquals(updateExclusiveTo.yyyyMMdd().toInt(), record.exclusiveTo)

                // 外部APIがコールされていることの確認
                assertEquals(0, dkPortalRegisterApiCalledCount)
                assertEquals(1, dkPortalUpdateApiCalledCount)
                assertEquals(0, dkPortalDeleteApiCalledCount)
            }

            @Test
            @DisplayName("先行先が追加されている場合、追加分が新規作成され、DKポータルの追加APIがコールされること")
            fun case2() {
                // setup
                val updateExclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd().plusDays(1)
                val updateExclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd().plusDays(3)

                val request = stubUpdateExclusiveProperty(
                    id = pojo.id,
                    exclusiveFrom = updateExclusiveFrom,
                    exclusiveTo = updateExclusiveTo,
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 100,
                            companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                            eCode = eCodePojo.eCode
                        ),
                        stubExclusivePropertyTargetWithId(
                            id = 200,
                            companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                            eCode = "E87654321"
                        ),
                    )
                )

                // execute
                service.update(stubJwtAuthInfo(), request)

                // verify
                val result = dslContext.selectExclusivePropertyBy(propertyId)
                assertEquals(2, result.size)

                // 先行期間が更新されていることを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                val record1 = dslContext.selectExclusivePropertyBy(request.id).first()
                assertEquals(updateExclusiveFrom.yyyyMMdd().toInt(), record1.exclusiveFrom)
                assertEquals(updateExclusiveTo.yyyyMMdd().toInt(), record1.exclusiveTo)

                // 新規先行先のレコードが追加されていることを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                val record2 =
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).first()
                assertEquals(propertyId.buildingCode.value, record2.buildingCode)
                assertEquals(propertyId.roomCode.value, record2.roomCode)

                // 外部APIがコールされていることの確認
                assertEquals(1, dkPortalRegisterApiCalledCount)
                assertEquals(1, dkPortalUpdateApiCalledCount)
                assertEquals(0, dkPortalDeleteApiCalledCount)
            }

            @Test
            @DisplayName("先行先が変更されている場合、既存の先行先のレコードは削除/追加分は新規作成され、DKポータルの削除/追加APIがコールされること")
            fun case3() {
                // setup
                val updateExclusiveFrom = pojo.exclusiveFrom.toString().yyyyMMdd().plusDays(1)
                val updateExclusiveTo = pojo.exclusiveTo.toString().yyyyMMdd().plusDays(3)

                val target1 = stubExclusivePropertyTargetWithId(
                    id = 100,
                    companyType = ExclusiveProperty.CompanyType.Leasing,
                )
                val target2 = stubExclusivePropertyTargetWithId(
                    id = 200,
                    companyType = ExclusiveProperty.CompanyType.fromValue(pojo.companyType.toInt())!!,
                    eCode = "E87654321"
                )
                val request = stubUpdateExclusiveProperty(
                    id = pojo.id,
                    exclusiveFrom = updateExclusiveFrom,
                    exclusiveTo = updateExclusiveTo,
                    exclusiveTargetWithIds = listOf(target1, target2),
                )

                // execute
                service.update(stubJwtAuthInfo(), request)

                // verify
                // 対象物件に対してレコードは計3件できる（削除レコード + 新規レコードx2）
                // 先行公開が削除されていることを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                assertEquals(3, dslContext.selectExclusivePropertyBy(propertyId).size)
                assertEquals(
                    true,
                    dslContext.selectExclusivePropertyBy(request.id).first().deleteFlag.toBoolean()
                )

                // 先行期間が更新されていることを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                val record1 = dslContext.selectExclusivePropertyBy(target1.id).first()
                assertEquals(target1.target.companyType.value.toByte(), record1.companyType)

                // 新規先行先のレコードが追加されていることを確認（各カラムの更新結果の検証はRepositoryのテストで実施）
                val record2 = dslContext.selectExclusivePropertyBy(target2.id).first()
                assertEquals(target2.target.companyType.value.toByte(), record2.companyType)

                // 外部APIがコールされていることの確認
                assertEquals(2, dkPortalRegisterApiCalledCount)
                assertEquals(0, dkPortalUpdateApiCalledCount)
                assertEquals(1, dkPortalDeleteApiCalledCount)
            }
        }
    }

    @Nested
    @DisplayName("先行公開削除シナリオの検証")
    inner class DeleteScenario {

        @Nested
        @DisplayName("トランザクション管理が適切に行えているか確認する")
        inner class TransactionCheck {
            @Test
            @DisplayName("DKポータルへの登録APIに失敗した場合、トランザクションがロールバックされること")
            fun case1() {
                // setup
                val data1 = stubExclusivePropertyPojo(
                    id = 100,
                    exclusiveFrom = 20251201,
                    exclusiveTo = 20251202,
                    earlyClosureFlag = false.toInt().toString(),
                    deleteFlag = false.toInt().toString()
                )
                val data2 = stubExclusivePropertyPojo(
                    id = 200,
                    exclusiveFrom = 20251205,
                    exclusiveTo = 20251206,
                    earlyClosureFlag = false.toInt().toString(),
                    deleteFlag = false.toInt().toString()
                )

                dslContext.saveExclusivePropertyPojo(data1, data2)

                val request = listOf(
                    ExclusiveProperty.Id.of(data1.id),
                    ExclusiveProperty.Id.of(data2.id),
                )

                service = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(
                        deleteExclusiveFunc = { _, id ->
                            if (id.value == 100L) {
                                throw ExternalApiServerException(
                                    ErrorType.EBOARD_API_ERROR,
                                    ErrorMessage.DK_PORTAL_RECEIVED_ERROR_RESPONSE.format("DKポータルAPIエラー")
                                )
                            }
                        }
                    ),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                )

                // execute
                val result = service.cancel(stubJwtAuthInfo(), request)

                assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)
                assertEquals(1, result.failedList.size)
                assertEquals(data1.id, result.failedList.first().value)

                // verify
                // ID = 100 のリクエストは失敗するため、削除フラグ等は更新されていない
                val pojo1 =
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(100)).first()
                assertEquals(false, pojo1.earlyClosureFlag.toBoolean()!!)
                assertEquals(false, pojo1.deleteFlag.toBoolean()!!)

                // ID = 200 のリクエストは成功するため、永続化されたレコードがあることを確認
                val pojo2 =
                    dslContext.selectExclusivePropertyBy(ExclusiveProperty.Id.of(200)).first()
                assertEquals(false, pojo2.earlyClosureFlag.toBoolean()!!)
                assertEquals(true, pojo2.deleteFlag.toBoolean()!!)

            }
        }

        @Nested
        @DisplayName("バリデーションチェック")
        inner class ValidationCheck {

            @Test
            @DisplayName("永続化されていないIDが存在する場合、削除処理が実施されず、不正なIDが返却されること")
            fun case3() {
                val data1 = ExclusiveProperty.Id.of(100)
                val data2 = ExclusiveProperty.Id.of(200)

                // setup
                dslContext.saveExclusivePropertyPojo(
                    stubExclusivePropertyPojo(
                        id = data1.value,
                    )
                )
                // execute
                val result = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                ).cancel(stubJwtAuthInfo(), listOf(data1, data2))

                // verify
                assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)
                assertEquals(
                    ExclusivePropertyAction.StatusReason.INVALID_ID_NOT_FOUND,
                    result.reason
                )

                // 失敗したレコードの詳細を検証
                assertEquals(1, result.failedList.size)
                assertEquals(data2, result.failedList.first())

                // 永続化されたレコードがないことを確認
                assertTrue(dslContext.selectExclusivePropertyBy(data2).isEmpty())
            }

            @Test
            @DisplayName("公開終了したIDが存在する場合、削除処理が実施されず、不正なIDが返却されること")
            fun case4() {
                val data1 = ExclusiveProperty.Id.of(100)
                val data2 = ExclusiveProperty.Id.of(200)
                val data3 = ExclusiveProperty.Id.of(300)
                // setup
                dslContext.saveExclusivePropertyPojo(
                    stubExclusivePropertyPojo(
                        id = data1.value,
                        exclusiveFrom = 20250101,
                        exclusiveTo = 20251231,
                        earlyClosureFlag = false.toInt().toString(),
                    ),
                    stubExclusivePropertyPojo(
                        id = data2.value,
                        exclusiveFrom = 20240101,
                        exclusiveTo = 20241231,
                        earlyClosureFlag = false.toInt().toString(),
                    ),
                    stubExclusivePropertyPojo(
                        id = data3.value,
                        exclusiveFrom = 20250101,
                        exclusiveTo = 20251231,
                        earlyClosureFlag = true.toInt().toString(),
                    )
                )

                // execute
                val result = ExclusivePropertyService(
                    dslContext = dslContext,
                    repository = ExclusivePropertyRepository(dslContext),
                    agentRepository = allPassAgentRepository,
                    propertyRepository = allPassPropertyRepository,
                    dkPortalRepository = MockDKPortalRepository(),
                    temporaryReservationRepository = MockTemporaryReservationRepository(),
                    propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                        dslContext
                    ),
                    employeeRepository = MockEmployeeRepository()
                ).cancel(stubJwtAuthInfo(), listOf(data1, data2, data3))

                // verify
                assertEquals(ExclusivePropertyAction.Status.FAILED, result.status)
                assertEquals(
                    ExclusivePropertyAction.StatusReason.INVALID_ID_COMPLETED,
                    result.reason
                )

                // 失敗したレコードの詳細を検証
                assertEquals(2, result.failedList.size)
                assertTrue(result.failedList.contains(data2))
                assertTrue(result.failedList.contains(data3))
            }

            @Test
            @DisplayName("指定された先行公開に紐づく物件が存在しない場合、Exceptionがスローされること")
            fun case5() {
                val data1 = stubExclusivePropertyPojo(
                    id = 100,
                    buildingCode = "*********",
                    roomCode = "01010",
                    exclusiveFrom = 20251115,
                    exclusiveTo = 20251120,
                )
                val data2 = stubExclusivePropertyPojo(
                    id = 200,
                    buildingCode = "000000002",
                    roomCode = "01010",
                    exclusiveFrom = 20251121,
                    exclusiveTo = 20251127,
                )

                val data3 = stubExclusivePropertyPojo(
                    id = 300,
                    buildingCode = "000000003",
                    roomCode = "02020",
                    exclusiveFrom = 20251201,
                    exclusiveTo = 20251209,
                )

                // setup
                dslContext.saveExclusivePropertyPojo(data1, data2, data3)
                dslContext.saveRoomInfoMasterPojo(
                    stubRoomInfoMasterPojo(
                        buildingCode = data1.buildingCode,
                        roomCode = data1.roomCode
                    )
                )

                // execute
                assertThrows<ServerValidationException> {
                    ExclusivePropertyService(
                        dslContext = dslContext,
                        repository = ExclusivePropertyRepository(dslContext),
                        agentRepository = allPassAgentRepository,
                        propertyRepository = MockPropertyRepository(
                            // 物件の存在チェックにパスするように関数をモック
                            listFunc = { ids ->
                                ids.filter { it.buildingCode.value != "*********" }
                                    .map {
                                        stubProperty(
                                            it.buildingCode.value,
                                            it.roomCode.value,
                                            changeDivision = "1",
                                            customerCompletionFlag = false
                                        )
                                    }
                            }
                        ),
                        dkPortalRepository = MockDKPortalRepository(),
                        temporaryReservationRepository = MockTemporaryReservationRepository(),
                        propertyMaintenanceRepository = MockPropertyMaintenanceRepository(
                            dslContext
                        ),
                        employeeRepository = MockEmployeeRepository()
                    ).cancel(
                        stubJwtAuthInfo(),
                        listOf(
                            ExclusiveProperty.Id.of(data1.id),
                            ExclusiveProperty.Id.of(data2.id),
                            ExclusiveProperty.Id.of(data3.id),
                        )
                    )
                }
            }
        }
    }
}
