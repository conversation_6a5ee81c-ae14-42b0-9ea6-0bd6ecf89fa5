package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.PropertyMaintenanceInfo
import jp.ne.simplex.application.model.UpdatePropertyMaintenance
import jp.ne.simplex.application.repository.db.PropertyMaintenanceRepository
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.stub.stubJwtRequestUser
import org.jooq.DSLContext

class MockPropertyMaintenanceRepository(
    context: DSLContext,
    val updateAdFfFunc: ((parameter: List<UpdatePropertyMaintenance.AdFf>) -> Unit)? = null,
    val updateInstructPublicFunc: ((parameter: List<UpdatePropertyMaintenance.PublishStatus>) -> Unit)? = null,
    val findByFunc: (id: Property.Id) -> PropertyMaintenanceInfo? = { _ -> null },
    val listByFunc: (ids: List<Property.Id>) -> List<PropertyMaintenanceInfo> = { _ -> emptyList() },
) :
    PropertyMaintenanceRepository(context) {
    override fun updateAdFf(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance.AdFf>
    ) {
        return if (updateAdFfFunc != null)
            updateAdFfFunc!!(parameter)
        else
            super.updateAdFf(stubJwtRequestUser(), parameter)
    }

    override fun updatePublishStatus(
        requestUser: AuthInfo.RequestUser,
        parameter: List<UpdatePropertyMaintenance.PublishStatus>
    ) {
        return if (updateInstructPublicFunc != null)
            updateInstructPublicFunc!!(parameter)
        else
            super.updatePublishStatus(stubJwtRequestUser(), parameter)
    }

    override fun findBy(id: Property.Id): PropertyMaintenanceInfo? {
        return findByFunc(id)
    }

    override fun listBy(ids: List<Property.Id>): List<PropertyMaintenanceInfo> {
        return listByFunc(ids)
    }
}
