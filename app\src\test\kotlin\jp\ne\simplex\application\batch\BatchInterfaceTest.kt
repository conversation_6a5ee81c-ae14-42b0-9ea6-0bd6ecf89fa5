package jp.ne.simplex.application.batch

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.repository.db.*
import jp.ne.simplex.application.repository.external.dkportal.DKPortalRepositoryInterface
import jp.ne.simplex.application.repository.mail.MailRepository
import jp.ne.simplex.application.service.ParkingContractService
import jp.ne.simplex.application.service.ParkingDetailsService
import jp.ne.simplex.application.service.ParkingReservationMailService
import jp.ne.simplex.application.service.ParkingReservationService
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_RESERVATION
import jp.ne.simplex.mock.*
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.dao.DuplicateKeyException
import kotlin.test.assertEquals

class BatchInterfaceTest : AbstractTestContainerTest() {
    override fun beforeEach() {
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_RESERVATION)
    }

    var sendFileSuccessCount = 0
    var getLatestFileSuccessCount = 0

    @Nested
    @DisplayName("バッチ処理実行可否判定")
    inner class Scenario1 {
        @Test
        @DisplayName("バッチ実行履歴に当日分の実行履歴がない場合に、バッチ処理が行われること")
        fun case1() {
            val batch = createBatch(false)
            batch.execute(null)
            assertEquals(sendFileSuccessCount, 1)

        }

        @Test
        @DisplayName("バッチ実行履歴に当日分の実行履歴がある場合に、バッチ処理が行われないこと")
        fun case2() {
            val batch = createBatch(true)
            batch.execute(null)
            assertEquals(sendFileSuccessCount, 0)

        }

        @Test
        @DisplayName("バッチ実行履歴に当日分の実行履歴がある かつ RERUNオプション付きで実行された場合に、バッチ処理が行われること")
        fun case3() {
            val batch = createBatch(true)
            batch.execute(ExecuteOptionType.RERUN)
            assertEquals(sendFileSuccessCount, 1)

        }
    }

    @Nested
    @DisplayName("実行オプション付きでバッチ処理を実行する")
    inner class Scenario2 {
        @Test
        @DisplayName("ファイル送信のみを行う実行オプション付きでバッチ処理を実行すると、S3からファイルをダウンロードする")
        fun case1() {
            val batch = createBatch(false)
            batch.execute(ExecuteOptionType.RERUN_ONLY_SEND_FILE)
            assertEquals(sendFileSuccessCount, 1)
            assertEquals(getLatestFileSuccessCount, 1)
        }
    }

    override fun tearDownEach() {
        sendFileSuccessCount = 0
    }

    private fun createBatch(isRegisteredHistory: Boolean): VacancyParkingDataForWelcomeParkBatch {
        var mockBatchExecuteHistoryRepository = MockBatchExecuteHistoryRepository { _, _ ->
            // 何もしない
        }
        if (isRegisteredHistory) {
            mockBatchExecuteHistoryRepository = MockBatchExecuteHistoryRepository { _, _ ->
                throw DuplicateKeyException("DuplicateKeyException")
            }
        }
        return VacancyParkingDataForWelcomeParkBatch(
            parkingService(),
            MockParkingFileRepository(),
            MockS3Repository(getLatestFileFunc = { _ -> getLatestFileSuccessCount++ }),
            MockFtpRepository(
                sendFileFunc = { _, _ -> sendFileSuccessCount++ },
            ),
            "Dummy-Bucket",
            mockBatchExecuteHistoryRepository
        )
    }

    private fun parkingReservationService(
        reservationRepository: ParkingReservationRepositoryInterface = MockParkingReservationRepository(),
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
        parkingEnableRepository: ParkingEnableRepositoryInterface = MockParkingEnableRepository(),
    ): ParkingReservationService {
        return ParkingReservationService(
            reservationRepository = reservationRepository,
            parkingDetailsService = parkingService(),
            parkingRepository = parkingRepository,
            parkingEnableRepository = parkingEnableRepository,
            parkingContractService = parkingContractService(),
            eBoardRepository = MockEboardRepository(),
            mailService = parkingReservationMailService()
        )
    }

    private fun parkingContractService(
        parkingContractPossibilityRepository: ParkingContractPossibilityRepositoryInterface
        = MockParkingContractPossibilityRepository(),
        parkingDetailsService: ParkingDetailsService = parkingService(),
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
        dkPortalRepository: DKPortalRepositoryInterface = MockDKPortalRepository(),
    ): ParkingContractService {
        return ParkingContractService(
            context = dslContext,
            parkingContractPossibilityRepository = parkingContractPossibilityRepository,
            parkingDetailsService = parkingDetailsService,
            parkingRepository = parkingRepository,
            dkPortalRepository = dkPortalRepository,
        )
    }

    private fun parkingService(
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
        parkingDetailsRepository: ParkingDetailsRepositoryInterface = MockParkingDetailsRepository(),
        vacantParkingListRepository: VacantParkingListRepositoryInterface = MockVacantParkingListRepository()
    ): ParkingDetailsService {
        return ParkingDetailsService(
            parkingRepository = parkingRepository,
            parkingDetailsRepository = parkingDetailsRepository,
            vacantParkingListRepository = vacantParkingListRepository
        )
    }

    private fun parkingReservationMailService(
        emailAddressMasterRepository: EmailAddressRepositoryInterface = MockEmailAddressRepository(),
        officeBranchMappingRepository: OfficeBranchMappingRepositoryInterface = MockOfficeBranchMappingRepository(),
        buildingRepository: BuildingMasterRepositoryInterface = MockBuildingMasterRepository(),
        branchRepository: BranchRepositoryInterface = MockBranchRepository(),
        mailRepository: MailRepository = MockMailRepository(),
        parkingRepository: ParkingRepositoryInterface = MockParkingRepository(),
    ): ParkingReservationMailService {
        return ParkingReservationMailService(
            emailAddressRepository = emailAddressMasterRepository,
            officeBranchMappingRepository = officeBranchMappingRepository,
            buildingRepository = buildingRepository,
            branchRepository = branchRepository,
            mailRepository = mailRepository,
            receptAddress = "<EMAIL>",
            inputName = "いい物件ボード",
            isSendMail = true,
            parkingRepository = parkingRepository,
        )
    }
}
