package jp.ne.simplex.application.repository.external.eboard.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.Room
import jp.ne.simplex.application.repository.external.eboard.config.EboardResponse

class EboardInstructPublicResponse(

    @JsonProperty("result")
    val result: String,

    @JsonProperty("message")
    val message: String,

    @JsonProperty("list")
    val list: List<InstructPublicUnitContent>,
) : EboardResponse {

    class InstructPublicUnitContent(
        @JsonProperty("tatemonoCd")
        val tatemonoCd: String,

        @JsonProperty("heyaCd")
        val heyaCd: String,

        @JsonProperty("updateResult")
        val updateResult: String,

        @JsonProperty("up_state")
        val upState: String,

        @JsonProperty("message")
        val message: String,
    ) {

        fun isSuccess(): Boolean {
            return updateResult == "OK"
        }

        fun getPropertyId(): Property.Id {
            return Property.Id(
                buildingCode = Building.Code.of(tatemonoCd),
                roomCode = Room.Code.of(heyaCd)
            )
        }

        fun getUpState(): Property.UpState? {
            return when (upState) {
                "1" -> Property.UpState.PREPARING
                "2" -> Property.UpState.RECRUITING
                "3" -> Property.UpState.TEMPORARY_RESERVED
                "4" -> Property.UpState.ALREADY_MOVED_IN
                else -> null
            }
        }
    }
}
