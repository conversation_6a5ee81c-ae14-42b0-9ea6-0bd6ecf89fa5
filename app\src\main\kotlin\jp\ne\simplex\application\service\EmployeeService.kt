package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.repository.db.EmployeeRepositoryInterface
import org.springframework.stereotype.Service

@Service
class EmployeeService(
    private val repository: EmployeeRepositoryInterface
) {

    fun listBy(branch: Branch): List<Employee> {
        return repository.findBy(branch)
    }

    fun getAffiliationBranchCode(employeeCode: Employee.Code?): Branch.Code? {
        return repository.getAffiliationBranchCode(employeeCode)
    }
}
