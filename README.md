# propetech-server

## 目次

1. [前提](#前提)
    1. [技術スタック](#技術スタック)
    2. [プロジェクト構成](#プロジェクト構成)
2. [環境構築](#環境構築)
    1. [リポジトリのClone](#リポジトリのClone)
    2. [コードスタイル](#コードスタイル)
    3. [アプリ起動](#アプリ起動)
3. [コーディング規約](#コーディング規約)
4. [リリース手順](#リリース手順)
    1. [IT&STG環境の場合](#ITまたはSTG環境の場合)
        1. [Appのイメージpush](#Appのイメージpush)
        2. [Batchのイメージpush](#Batchのイメージpush)
        3. [Mockのイメージpush](#Mockのイメージpush)
    2. [STG2環境の場合](#STG2環境の場合)
        1. [Appのイメージpush](#Appのイメージpush-1)
        2. [Batchのイメージpush](#Batchのイメージpush-1)
    3. [PRD環境の場合](#PRD環境の場合)
5. ドキュメント（外部リンク）
    1. [リリースの仕組み](./app/README.md)
    2. 開発関連
        1. [ログ出力](./app/docs/ログ出力.md)
        2. [外部APIの認証](./app/docs/外部APIの認証.md)
6. [Tips](#tips)
    1. [Gradleタスク](#Gradleタスク)
    2. [トラブルシューティング](#トラブルシューティング)

---

## 前提

当初、サーバアプリは、Lambda（Typescript）で作成していたが、
実際にユーザーに使ってもうらと、Lambdaのコールドスタートにかかる秒数がレイテンシに致命的な影響を与えることがわかったため、
すでに稼働している機能は一旦Lambdaのまま、今後作る機能については、Kotlin x ECS で作成することになった。

また、Lambdaを利用したローカル開発では、[sxi-propetech/propetech](https://github.com/sxi-propetech/propetech)
にある、[Docker](https://github.com/sxi-propetech/propetech/blob/main/docker-compose.yml)
を用いて、DBやLocalStackを起動させている

そのため、本リポジトリで管理するサーバアプリで、接続するDBは、[sxi-propetech/propetech](https://github.com/sxi-propetech/propetech)
にある[Docker](https://github.com/sxi-propetech/propetech/blob/main/docker-compose.yml)
を用いて起動させたDBが存在することを前提とする

### 技術スタック

- 言語
    - `Kotlin (Java 17)`
- ビルドツール
    - `Gradle`
- フレームワーク/ライブラリ
    - `Spring Boot（3.2.5）`
    - `Jooq（3.19.21）`：ORMのフレームワーク（[公式リンク](https://www.jooq.org/)）
    - `Flyway（10.22.0）`：データベースマイグレーションツール
- テスト
    - `Jupiter (JUnit5)`
    - `kotest`
    - `TestContainer`
      ：テストでDockerコンテナを利用するためのライブラリ（[公式リンク](https://testcontainers.com/)）

### プロジェクト構成

```
propetech-server
|
|--- app // Webサーバアプリケーション用のプロジェクト（db プロジェクトに依存する）
|
|--- db // DB関連のプロジェクト（マイグレーションやローカル開発用のDBのDockerfileなどを管理する）（db-strategy プロジェクトに依存する）
|
|--- db-strategy // Jooqによる自動生成ファイルのカスタマイズ処理用のプロジェクト
|
|--- docs // ドキュメント類
...
```

## 環境構築

### リポジトリのClone

- [sxi-propetech/propetech](https://github.com/sxi-propetech/propetech)と同一階層にチェックアウト
    - 上記、[前提](#前提)
      記載の通り、DB構築に必要なDDL等が[sxi-propetech/propetech](https://github.com/sxi-propetech/propetech)
      にあるため、後のステップで実行する処理にて参照する際に、同一階層にあることを前提としている

### コードスタイル

無駄な差分を出すことを避けるため、コードスタイルを統一しています。
以下手順を参考に、IntelliJの設定を行ってください。

- `.idea/codeStyles/intellij-java-google-style.xml` をインポートしてください。

  ![img.png](./docs/images/code_style.png)

- 保存時のコードの再フォーマットを有効にしておいてください。

  ![img.png](./docs/images/save_action.png)

### JAVAランタイムの設定

- JVMに社内CA・Netskopeのルート証明書を追加
    - 外部のライブラリを利用するためmavenリポジトリとのSSL接続を有効にします。
    - [この手順](https://knowledge.greatsimplex.com/open.knowledge/view/995)
      内のJavaの箇所を参考に、各ルート証明書をローカルにダウンロードしてkeytoolコマンドを実行してください。
    - 使用中のJavaは「File」 → 「Project Structure」→ Project SDKで確認できます。
    - コマンド例
      ```shell
      /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.14/Contents/Home/bin/keytool --noprompt -import -alias SimplxCA -keystore /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.14/Contents/Home/lib/security/cacerts -file /Users/<USER>/Downloads/simplex-ca-20190204.pem -storepass changeit
      /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.14/Contents/Home/bin/keytool --noprompt -import -alias goskope -keystore /Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.14/Contents/Home/lib/security/cacerts -file /Users/<USER>/Downloads/simplex.goskope.com-20230608.pem -storepass changeit
      ```

### アプリ起動

- [sxi-propetech/propetech](https://github.com/sxi-propetech/propetech)
  のREADMEを元に、以下のDockerコンテナを起動させておくこと
    - db
    - localstack-main
      （以下のようになっていれば OK ）

      ![img.png](docs/images/localstack.png)

- propetech-server の Gradleタスク `setup` を実行する

  ![img.png](docs/images/gradle_setup.png)

- propetech-server の Gradleタスク `bootRunDev` を実行する

  ![img.png](docs/images/gradle_bootRunDev.png)

- [Swagger](http://localhost:8082/api/v2/swagger-ui/index.html?urls.primaryName=INTERNAL)
  にアクセスできればOK

---

## リリース手順

### ITまたはSTG環境の場合

#### Appのイメージpush

- リリースフローの実行

  1. AWSにログインし、パラメータストアで[環境名]/app1/image/tagの値を更新する。
   例) v.0.0.1 → v0.0.2

  2. Github上からpropetech-serverレポジトリ→Actionsを選択

  3. Actionsから[Deploy App DockerImage]を選択

  4. 「Use Workflow From」でデプロイしたいブランチを選択し、Dockerイメージのタグのバージョン部分に更新したタグのバージョンを入力し、「Run Workflow」で実行

  5. 結果確認
    実行結果はActionsで確認する。

#### Batchのイメージpush

- リリースフローの実行

  1. AWSにログインし、パラメータストアで[環境名]/batch1/image/tagの値を更新する。
     例) v.0.0.1 → v0.0.2

  2. Github上からpropetech-serverレポジトリ→Actionsを選択

  3. Actionsから[Deploy Batch DockerImage]を選択

  4. 「Use Workflow From」でデプロイしたいブランチを選択し、Dockerイメージのタグのバージョン部分に更新したタグのバージョンを入力し、「Run Workflow」で実行

  5. 結果確認
     実行結果はActionsで確認する。

#### Mockのイメージpush

- リリースフローの実行

    1. Github上からpropetech-serverレポジトリ→Actionsを選択

    2. Actionsから[Deploy Mock DockerImage]を選択

    3. 「Use Workflow From」でデプロイしたいブランチを選択し「Run Workflow」で実行

    4. 結果確認
       実行結果はActionsで確認する。

### STG2環境の場合

#### Appのイメージpush

- リリースフローの実行

  1. propetech-serverのリポジトリにアクセスし、SettingsのEnvironmentsのstg2-deploy中身を確認し、APP_TAG_VERSIONのタグバーションを更新する。
   　例) v.0.0.1 → v0.0.2

  2. Releaseを起票する
     以下を参考に1で作成したタグを使用しReleaseを起票する。

     この際に、[choose a tag]プルダウンから[Create new tag]を選択し、リリースしたい断面でtagを打つ。
     タグ名は`stg2-v0.0.1-app`のように必ず`stg2-バージョン-app`とする。

     上記手順でdeploy-app-image-stg2のワークフローが実行開始されます。

  3. 結果確認
     実行結果はActionsで確認する。

#### Batchのイメージpush

- リリースフローの実行

  1. propetech-serverのリポジトリにアクセスし、SettingsのEnvironmentsのstg2-deploy中身を確認し、BATCH_TAG_VERSIONのタグバーションを更新する。
   　例) v.0.0.1 → v0.0.2

  2. Releaseを起票する
     以下を参考に1で作成したタグを使用しReleaseを起票する。

     この際に、[choose a tag]プルダウンから[Create new tag]を選択し、リリースしたい断面でtagを打つ。
     タグ名は`stg2-v0.0.1-batch`のように必ず`stg2-バージョン-batch`とする。

     上記手順でdeploy-batch-image-stg2のワークフローが実行開始されます。

  3. 結果確認
     実行結果はActionsで確認する。


### PRD環境の場合

STG環境の最新イメージをpullしPRD環境にpushします。
最新バージョンのイメージタグは、ECRごとにパラメータストアで管理します。（パラメータの更新は自動で行われます。）

- リリースフローの実行

  1. STG1のAWSログインし、ECRのBATCH、AppのIMAGEタグバージョンを確認

  2. propetech-serverのリポジトリにアクセスし、Actionsで[Deploy App DockerImage]、[Deploy Batch DockerImage]を実行（タグのバージョンは1で確認したタグのバージョンを+1で指定）

  3. Releaseを起票する
     作成したタグを使用しReleaseを起票する。

     この際に、[choose a tag]プルダウンから[Create new tag]を選択し、リリースしたい断面でtagを打つ。
     タグ名は`v0.0.1-all`のように必ず`バージョン-all`とする。

     上記手順でdeploy-ecr-prdのワークフローが実行開始されます

  4. 結果確認
     実行結果はActionsで確認する。


## コーディング規約

当該プロジェクトは、既存のレガシーなシステムのリプレイス案件です。
リプレイスの範囲は、アプリケーションのみで、データの主管は、既存のシステムのままです。
導入当初、人ばり/期間の都合で、データの正規化などは、一切されず、めちゃくちゃなスキーマ定義のまま開発が行われてきました。
（例えば、主キーがなかったり、定義上の制約はないものの、実際は外部キーのように扱っていたり、あげたらキリないですが。。）

とはいえ、それに合わせて開発をすると、今後の保守性/拡張性は著しく低下することが目に見えています。
そのため、プロダクトを成長しやすくするための準備として、当該プロジェクトでは、以下をコーディング規約とすることで、
レガシーなスキーマ定義だろうが、保守/拡張性を高い状態に保つことを目的としています。

ベースは、変更の多い画面からのリクエスト/レスポンスと、レガシーで正規化されていないDBとやり取りするPojoへの依存をできる限り排除するという方針です。

もちろん、今後機能を追加していく中で、このルールがそぐわない場合は、チームでそれを変えていくことを推奨します。

### 依存関係のルール

- Controller/Repository 層で扱うオブジェクト（DTO/Pojo）は、controller/repositoryパッケージ外に参照を漏らさない
    - Controller → Service に渡すオブジェクトは、DTO → Model に変換したもの
    - Service → Controller に渡すオブジェクトは、DTOではなくModelそのまま
    - Controller → Client に返すオブジェクトは、Model → DTO に変換したもの
        - Model への変換処理は、controller パッケージ内で行う（以下、例）
        ```kotlin
        // 以下、例のため、同一ファイルに記載していますが、本来は、クラスごとにファイルを切ってください

        class Model(val value: String)

        // Controller → Service に渡すオブジェクトへの変換処理
        class RequestDto(val value: String) {
            fun toModel(): Model {
                return Model(value)
            }
        }

        // Controller → Client に渡すオブジェクトへの変換処理
        class ResponseDto(val value: String) {
            companion object {
                fun of(model: Model): ResponseDto {
                    return ResponseDto(model.value)
                }
            }
        }
        ```
    - Repository → Service に返すオブジェクトは、Pojo → Model に変換したもの
        - Model への変換処理は、repository パッケージ内で行う（以下、例）
        ```kotlin
        class Model(val value: String)

        // このクラスは、Jooqによって自動生成されるため、編集できません
        class ModelPojo(val value: String)

        // ModelPojoを直接編集できない + Model クラスへのPojoの参照を切るため
        // 別クラスで、ModelPojoをレシーバーとしたModelクラスへの変換関数を定義します
        class ModelPojoExtension {
            companion object {
                fun ModelPojo.getModel(): Model {
                    return Model(this.value)
                }
            }
        }
        ```

### 値オブジェクトの作成ルール

- 値オブジェクトは、上位概念を表すクラスの内部staticクラスとして定義する
    - データの関係性を一目で理解するため、なるべく下記例のような形で値オブジェクトを生成してください（以下、例）
    ```kotlin
    class Parking(val id: Id) {
        class Id(val value: String)
    }

    class ParkingReservation(
        // Parking という概念の Id 属性に依存していることが一目でわかる
        val parkingId: Parking.Id,
        val reservationStatus: ParkingReservationStatus
    )

    // ↑↑↑ 推奨する書き方 ↑↑↑
    //
    // ↓↓↓ 推奨しない書き方 ↓↓↓

    class ParkingId(val value: String)
    class ParkingReservation(
        // ParkingIdに依存していることがわかるが、この Id が Parking の Id なのかどうかは、わからない
        val parkingId: ParkingId,
        val reservationStatus: ParkingReservationStatus
    )

    enum class ParkingReservationStatus {
        CANCELED, RESERVED
    }
    ```

### テストで用いる Model/Pojo のインスタンス定義ルール

各テストクラス内で定義するのではなく、`StubHelper.kt` で 定義してください。
様々なケースで、インスタンスを作成する手間を省ける + テストで意識したくない項目を排除できるメリットがあります。

```kotlin
class Model(
    val id: String,
    val name: String,
    val tel: String?,
    val address: String?,
)

// ↑ のようなクラスがあったときに、以下のような stub 関数を用意してあげる
// 必須属性には、デフォルト値を、任意属性には null をデフォルトで設定する
fun stubModel(
    id: String = "000011",
    name: String = "テスト太郎",
    tel: String? = null,
    address: String? = null,
): Model {
    return Model(id, name, tel, address)
}

class TestClass : FunSpec({
    test("サンプル") {
        // Modelクラスの中身を気にする必要ない場合は、引数なしですぐに定義可能
        val input1 = stubModel()

        // Modelクラスの tel のみを意識したテストを書きたい場合でも、tel のみ指定して定義可能
        val input2 = stubModel(tel = "08035351231")
        val input3 = stubModel(tel = "080-3535-1231")
        val input4 = stubModel(tel = "080_3535_1231")
        // ...
    }
})

```

---

## Tips

### トラブルシューティング

| 内容                              | 対応方法                                                                                                                                 |
|:--------------------------------|:-------------------------------------------------------------------------------------------------------------------------------------|
| コンパイル時に、OutOfMemoryErrorが発生する場合 | **gradle.properties** の最終行に `kotlin.daemon.jvmargs=-Xmx5g` を追加する                                                                     |
| DB関連のオブジェクトへの参照等などコンパイルエラーが発生する | Step1：`propetech` リポジトリで、ddlが更新された可能性があるため、ローカルブランチを最新化の上、ローカルスタックを再デプロイしてください<br>Step2：その後、当該リポジトリの `db:setup`　のGradleタスクを再実行してください |

### Gradleタスク

### *db:setup*：開発/起動に必要な準備を行うタスクで、以下のGradleタスクを順に実行する

<!-- @formatter:off -->
- **propetech-server:prepare**
    - DB構築に必要なDDL等の資材を、[sxi-propetech/propetech](https://github.com/sxi-propetech/propetech) から `db/src/test/resources` にコピーする
- **propetech-server:buildTestContainer**
    - `db/Dockerfile` を元に、Dockerイメージをビルドする
- **db:startTestContainer**
    - 直前のステップで作成したDockerイメージを元に、Dockerコンテナを起動する
- **db:overwriteConfig**
    - Jooq の接続先DBの設定を、直前のステップで起動したDockerコンテナに向ける
        - ※ `db:startTestContainer`で起動するDockerコンテナの起動ポートはランダムのため、後で上書きする必要がある
- **db:generateAppJooq**
    - 前のステップで起動したDockerコンテナ上のDBに接続して、Kotlin上で扱うテーブル定義などを生成する
- **db:stopTestContainer**
    - 起動させたDockerコンテナを停止する
<!-- @formatter:on -->

### *app:bootRunDev*：**application-dev.yaml** の設定で、アプリを起動する（ローカル開発時に使用する）

- 中身はシンプルで、起動引数に `spring.profiles.active=dev` を指定しているだけ
