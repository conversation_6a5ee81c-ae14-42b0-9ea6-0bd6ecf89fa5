package jp.ne.simplex.application.repository.external

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.MapLikeType
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiConnectionException
import jp.ne.simplex.exception.ExternalApiUnauthorizedException
import org.apache.hc.client5.http.classic.HttpClient
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import org.springframework.web.client.ResourceAccessException
import org.springframework.web.client.ResponseErrorHandler
import org.springframework.web.client.RestClient
import java.net.URI

/**
 * 外部システム接続用のRestClient
 * 共通のロギング設定やパラメータの変換処理などを行っている
 */
class ExternalApiRestClient<Req : ExternalApiRequest, Res> private constructor() {

    class Builder<Req : ExternalApiRequest, Res>(
        private val builder: RestClient.Builder
    ) {

        fun endpoint(endpoint: String): Builder<Req, Res> {
            builder.baseUrl(endpoint)
            return this
        }

        fun requestFactory(httpClient: HttpClient?): Builder<Req, Res> {
            if (httpClient != null) {
                builder.requestFactory(HttpComponentsClientHttpRequestFactory(httpClient))
            }
            return this
        }

        fun requestInterceptor(vararg interceptor: ClientHttpRequestInterceptor): Builder<Req, Res> {
            interceptor.forEach { builder.requestInterceptor(it) }
            return this
        }

        fun errorHandler(vararg errorHandler: ResponseErrorHandler): Builder<Req, Res> {
            builder.defaultStatusHandler(ExternalApiCompositeErrorHandler(errorHandler.toList()))
            return this
        }

        fun build(): ExternalApiRestClient<Req, Res> {
            val client = ExternalApiRestClient<Req, Res>()
            client.restClient = builder.build()
            return client
        }
    }

    companion object {
        fun <Req : ExternalApiRequest, Res> builder(builder: RestClient.Builder): Builder<Req, Res> {
            return Builder(builder)
        }
    }

    /** エンドポイントとExternalApiLoggingInterceptorを設定したRestClient */
    private lateinit var restClient: RestClient

    /** クエリパラメータへの変換などを担当するマッパー */
    private val objectMapper: ObjectMapper = ExternalApiObjectMapper.getInstance()

    /** ObjectMapperで、オブジェクトを変換する先のMapの型定義 */
    private val kataOfQueryParam: MapLikeType = objectMapper.typeFactory.constructMapLikeType(
        Map::class.java,
        String::class.java,
        Any::class.java
    )

    fun <T : Res> call(
        method: Method,
        path: String,
        contentType: MediaType? = null,
        request: Req,
        responseClass: Class<T>,
        authenticationFailureCallback: (() -> Unit)? = null,
    ): T {
        try {
            return when (method) {
                Method.GET -> get(path, request, responseClass)
                Method.POST -> post(path, contentType, request, responseClass)
            }
        }
        // 通信失敗時などのExceptionをここでキャッチする
        catch (ex: ResourceAccessException) {
            throw ExternalApiConnectionException(request.getErrorType(), ex)
        }
        // 認証エラーの場合、アクセストークンの再発行など、外接先ごとに特別な処理を実行する必要があるため、
        // ここで、明示的にキャッチし、指定されたコールバックを実行するようにしている
        catch (ex: ExternalApiUnauthorizedException) {
            if (authenticationFailureCallback != null) {
                authenticationFailureCallback()
                return this.call(method, path, contentType, request, responseClass, null)
            }
            throw ex
        }
        // restClient に注入した ErrorHandler 内でハンドリングされた Exception をキャッチし、そのままスローする
        // 不要な catch 句 だが、処理をわかりやすくするために、明示的に全てのExceptionを catch するようにしている
        catch (ex: Exception) {
            throw ex
        }
    }

    private fun <T : Res> get(path: String, request: Req, responseClass: Class<T>): T {
        return restClient.get()
            .uri(path) { builder ->
                builder.queryParams(request.toQueryParams()).build()
            }
            .retrieve()
            .toEntity(responseClass)
            .body!!
    }

    private fun <T : Res> post(
        path: String, contentType: MediaType?, request: Req, responseClass: Class<T>
    ): T {
        if (contentType == MediaType.APPLICATION_FORM_URLENCODED) {
            return restClient.post()
                .uri(path) { builder ->
                    builder.queryParams(request.toQueryParams()).build()
                }
                .contentType(contentType!!)
                .retrieve()
                .toEntity(responseClass)
                .body!!
        } else if (request is ExternalApiMultipartRequest) {
            return restClient.post()
                .uri(path)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(request.toMultipartMap())
                .retrieve()
                .toEntity(responseClass)
                .body!!
        }
        return restClient.post()
            .uri(path)
            .contentType(contentType ?: MediaType.APPLICATION_JSON)
            .body(request)
            .retrieve()
            .toEntity(responseClass)
            .body!!
    }

    private fun Req.toQueryParams(): LinkedMultiValueMap<String, String> {
        val map = objectMapper.convertValue<Map<String, String?>>(this, kataOfQueryParam)

        val multiValueMap = LinkedMultiValueMap<String, String>()

        map.forEach { (name: String, value: String?) ->
            if (value != null) multiValueMap[name] = listOf(value)
        }

        return multiValueMap
    }
}

enum class Method {
    GET, POST
}

interface ExternalApiRequest {

    @JsonIgnore
    fun getErrorType(): ErrorType

}

interface ExternalApiMultipartRequest {

    fun toMultipartMap(): MultiValueMap<String, Any>

}

class ExternalApiCompositeErrorHandler(
    private val errorHandlers: List<ResponseErrorHandler>
) : ResponseErrorHandler {
    override fun hasError(response: ClientHttpResponse): Boolean {
        // どれか1つのエラーハンドラーがエラーと判断すればエラーとする
        return errorHandlers.any { it.hasError(response) }
    }

    override fun handleError(url: URI, method: HttpMethod, response: ClientHttpResponse) {
        // 各エラーハンドラーでエラー処理を試みる
        errorHandlers.forEach { handler ->
            if (handler.hasError(response)) {
                handler.handleError(url, method, response)
            }
        }
    }
}
