package jp.ne.simplex.mock

import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.model.VacancyParkingLotTarget
import jp.ne.simplex.application.repository.file.ParkingFileRepositoryInterface
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.nio.file.Path
import java.time.LocalDateTime

class MockParkingFileRepository(
    val saveParkingSummaryForDkPortalFunc: (
        parkingContractPossibilityList: List<ParkingContractPossibility>,
    ) -> Unit = { _ -> },
    val saveVacancyParkingDataForWelcomeParkFunc: (
        vacancyParkingLotList: List<VacancyParkingLotTarget>,
        datetime: LocalDateTime,
    ) -> Unit = { _, _ -> },
) : ParkingFileRepositoryInterface {
    override fun saveParkingSummaryForDkPortal(
        parkingContractPossibilityList: List<ParkingContractPossibility>
    ): Path {
        saveParkingSummaryForDkPortalFunc(parkingContractPossibilityList)
        return Path.of("Dummy")
    }

    override fun saveVacancyParkingDataForWelcomePark(
        vacancyParkingLotList: List<VacancyParkingLotTarget>,
        datetime: LocalDateTime
    ): Path {
        saveVacancyParkingDataForWelcomeParkFunc(vacancyParkingLotList, datetime)
        return Path.of("Dummy")
    }

    override fun saveFromS3ForDkPortal(latestFile: ResponseInputStream<GetObjectResponse>): Path {
        return Path.of("Dummy")
    }

    override fun saveFromS3ForWelcomePark(latestFile: ResponseInputStream<GetObjectResponse>): Path {
        return Path.of("Dummy")
    }
}
