package jp.ne.simplex.application.batch

import jp.ne.simplex.application.model.ExclusivePropertyInfo
import jp.ne.simplex.application.repository.aws.S3RepositoryInterface
import jp.ne.simplex.application.repository.db.BatchExecuteHistoryRepositoryInterface
import jp.ne.simplex.application.repository.file.ExclusivePropertyFileRepository
import jp.ne.simplex.application.repository.ftp.FtpRepositoryInterface
import jp.ne.simplex.application.service.ExclusivePropertyService
import jp.ne.simplex.application.service.PropertyService
import jp.ne.simplex.application.service.TemporaryReservationService
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.shared.MultiThreadRunBlocking.Companion.runAsyncTasks
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.util.*

/** E-Cloud向け先行公開バッチ */
@Profile("batch")
@Component("ExclusivePropertyForECloudBatch")
class ExclusivePropertyForECloudBatch (
    private val s3Repository: S3RepositoryInterface,
    private val ftpRepository: FtpRepositoryInterface,
    private val exclusivePropertyFileRepository: ExclusivePropertyFileRepository,
    private val exclusivePropertyService: ExclusivePropertyService,
    private val temporaryReservationService: TemporaryReservationService,
    private val propertyService: PropertyService,
    @Value("\${batch.s3.exclusive-property-batch-bucket}")
    private val bucketName: String,
    override val batchExecuteHistoryRepository: BatchExecuteHistoryRepositoryInterface,
    ): BatchInterface {
    override val batchType: BatchType = BatchType.EXCLUSIVE_PROPERTY_FOR_E_CLOUD

    @Value("\${app.property.exclusive-property-data.parallel-num}")
    val exclusivePropertyDataParallelNum = 1

    companion object {
        private val log = LoggerFactory.getLogger(ExclusivePropertyForECloudBatch::class.java)
    }

    override fun executeInternal(executeOption: ExecuteOptionType?) {
        if (executeOption == ExecuteOptionType.RERUN_ONLY_SEND_FILE) {
            executeOnlySendFile()
            return
        }
        val results = getExclusivePropertyList()
        val localFilePath = exclusivePropertyFileRepository.saveExclusivePropertyForECloud(results)

        s3Repository.uploadFile(
            localFilePath,
            localFilePath.fileName.toString(),
            bucketName
        )
        ftpRepository.sendFileToECloud(
            localFilePath,
            localFilePath.fileName.toString(),
        )
    }

    override fun isAcceptableOption(executeOption: ExecuteOptionType): Boolean {
        return executeOption == ExecuteOptionType.RERUN_ONLY_SEND_FILE || executeOption == ExecuteOptionType.RERUN
    }

    private fun getExclusivePropertyList(): List<ExclusivePropertyInfo> {
        val targets = exclusivePropertyService.getExclusivePropertyInfo()
        val results = Collections.synchronizedList(mutableListOf<ExclusivePropertyInfo>())
        val errors = Collections.synchronizedList(mutableListOf<Throwable>())

        // マルチスレッドで連携対象外の先行公開情報を除外する
        runAsyncTasks(
            tasks = targets,
            maxThreads = exclusivePropertyDataParallelNum,
        ){ target ->
            try{
                exclusivePropertyService.filterDirectManagedExclusiveProperty(
                    target,
                    temporaryReservationService = temporaryReservationService,
                    propertyService =  propertyService
                )
                results.add(target)
            } catch(e: ServerValidationException) {
                // 対象外の先行公開物件
            } catch(e: Throwable) {
                log.error("Error processing exclusive property target: ${target.id}", e)
                errors.add(e)
            }
        }

        // 例外が発生した場合は最初の例外をthrow
        if (errors.isNotEmpty()) throw errors[0]

        return results
    }

    private fun executeOnlySendFile() {
        val latestCsvContent = s3Repository.getLatestFile(bucketName)
        val localFilePath = exclusivePropertyFileRepository.saveFromS3ForECloud(
            latestCsvContent,
        )
        ftpRepository.sendFileToECloud(
            localFilePath,
            localFilePath.fileName.toString()
        )
    }
}
