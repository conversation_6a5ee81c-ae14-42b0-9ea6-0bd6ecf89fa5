package jp.ne.simplex.mock

import jp.ne.simplex.application.repository.ftp.FtpRepositoryInterface
import java.nio.file.Path

class MockFtpRepository(
    val sendFileFunc: (localFilePath: Path, remoteFileName: String) -> Unit = { _, _ -> }
) : FtpRepositoryInterface {
    override fun sendFileToWelcomePark(localFilePath: Path, remoteFileName: String) {
        sendFileFunc(localFilePath, remoteFileName)
    }
    override fun sendFileToECloud(localFilePath: Path, remoteFileName: String) {
        sendFileFunc(localFilePath, remoteFileName)
    }

}
