package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException

/** 電話番号 */
class TelephoneNumber private constructor(val value: String) {
    companion object {
        fun of(value: String): TelephoneNumber {
            return if (isTELNum(value)) TelephoneNumber(value)
            else throw ModelCreationFailedException(ErrorMessage.INVALID_TELEPHONE_NUMBER.format())
        }

        private fun isTELNum(value: String): Boolean {
            return value.matches(Regex("^[0-9-]{0,15}$")) // 数字とハイフンのみ、かつ15桁以下であることを確認
        }
    }
}
