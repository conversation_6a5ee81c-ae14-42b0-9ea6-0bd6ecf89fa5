package jp.ne.simplex.authentication

import jakarta.xml.bind.DatatypeConverter
import jp.ne.simplex.application.model.LoginAccessKeyInfo
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.authentication.saml.SingleSignOnConfig
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.security.MessageDigest
import java.time.LocalDate

@Component
class AccessKeyVerifier(
    private val ssoConfig: SingleSignOnConfig,
    private val secretManager: SecretManagerRepository,
) {
    companion object {
        private val log = LoggerFactory.getLogger(AccessKeyVerifier::class.java)
        private val md = MessageDigest.getInstance("SHA-256")
    }

    fun verify(accessKey: LoginAccessKeyInfo.AccessKey) {
        if (accessKey.value != genAccessKey()) {
            throw ServerValidationException(ErrorMessage.AUTHENTICATION_FAILURE.format())
        }
    }

    private fun genAccessKey(): String {
        val hash = secretManager.getValue(ssoConfig.accessKey.secretId)
        val combinedString = hash + LocalDate.now().yyyyMMdd()
        val bytes = md.digest(combinedString.toByteArray())
        val hex = DatatypeConverter.printHexBinary(bytes).lowercase()
        val accessKey = hex.substring(3, 14)
        log.debug("generated sso accessKey:$accessKey")
        return accessKey
    }
}
