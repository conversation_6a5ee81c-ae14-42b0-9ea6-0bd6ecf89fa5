package jp.ne.simplex.application.service

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.db.jooq.gen.tables.references.BUILDING_MASTER
import jp.ne.simplex.mock.MockBuildingMasterRepository
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class LoginParkingServiceTest : AbstractTestContainerTest() {

    companion object {
        private const val BUILDING_CODE = "000000101"
    }

    override fun beforeEach() {
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(BUILDING_MASTER)
    }

    @Nested
    @DisplayName("駐車場詳細画面遷移")
    inner class Scenario1 {
        private lateinit var pdService: LoginParkingService

        @BeforeEach
        fun setup() {
            pdService = LoginParkingService(
                MockBuildingMasterRepository(
                    isExistFunc = { it.value == BUILDING_CODE }
                )
            )
        }

        @Test
        @DisplayName("存在する建物コードの場合は駐車場詳細画面へのパスを返却する")
        fun test1() {
            val path = pdService.getPath(Building.Code.of(BUILDING_CODE))
            assertEquals("/parkingDetails?buildingCd=${BUILDING_CODE}", path)
        }

        @Test
        @DisplayName("存在しない建物コードの場合はnullを返却する")
        fun test2() {
            val path = pdService.getPath(Building.Code.of("999999999"))
            assertEquals(null, path)
        }

        @Test
        @DisplayName("有効でない建物コードの場合はnullを返却する")
        fun test3() {
            val path = pdService.getPath(null)
            assertEquals(null, path)
        }
    }
}
