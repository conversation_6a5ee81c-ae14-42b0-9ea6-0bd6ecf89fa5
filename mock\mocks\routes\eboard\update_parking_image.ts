import {
  EboardResponseType,
  eboardResponseMapping,
} from '../../shared/eboard/eboard_response_type';
import { keysToLowerCase } from '../../shared/shared_function';

/**
 * いい物件ボード/駐車場配置図画像登録API
 *
 * サンプルリクエスト
 *  curl -X POST "http://localhost:8083/eboard/update_parking_image" \
 *  -H "Authorization: Bearer 51480924-5792-46ef-9503-c927b4e596a7" \
 *  -H "Content-Type:multipart/form-data" \
 *  -F "tatemonoCd=005003301" \
 *  -F "userId=071350" \
 *  -F "updateKbn=1" \
 *  -F "InputStream=@test.jpg"
 */
export default [
  {
    id: 'eboard_update_parking_image',
    url: '/eboard/newEboardApi/update_parking_image',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: (req, res) => {
            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const responseType = checkParameter(req);

            if (responseType != EboardResponseType.SUCCESS) {
              res.status(400);
              res.send({
                result: responseType,
                message: eboardResponseMapping[responseType],
              });

              return;
            }

            // status: 200 の場合
            res.status(200);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
              updateResult: 'OK',
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            const responseType = EboardResponseType.OTHER_ERROR;

            res.status(500);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する
 * responseCodeMapping（api_response_specification.js）定義のエラーコードを返却する
 * @param {*} req
 * @returns {EboardResponseType}
 */
function checkParameter(req): EboardResponseType {
  const headers = keysToLowerCase(req.headers);
  const contentType: string = headers['content-type']
  console.log(`content-type=${contentType}`)
  if (!new RegExp(`^multipart/form-data; *boundary=`).test(contentType)) {
    return EboardResponseType.PARAMETER_INVALID;
  }

//   Object.keys(req).forEach((key) => {
//     console.log(`req: key=${key},value=${req[key]}`);
//   });
//   Object.keys(req.query).forEach((key) => {
//     console.log(`req.query: key=${key},value=${req.query[key]}`);
//   });
//   Object.keys(req.params).forEach((key) => {
//     console.log(`req.params: key=${key},value=${req.params[key]}`);
//   });
//   const tatemonoCd = req.body.tatemonoCd
//   const userId = req.body.userId
//   const updateKbn = req.body.updateKbn
//   const InputStream = req.body.InputStream
//
//   if (!tatemonoCd || !userId) {
//     console.log('tatemonoCd=' + tatemonoCd)
//     console.log('userId=' + userId)
//     return EboardResponseType.PARAMETER_MISSING;
//   }
//   if (updateKbn === undefined) {
//     console.log('updateKbn=' + updateKbn)
//     return EboardResponseType.PARAMETER_MISSING;
//   }
//   if (updateKbn != 1 || updateKbn != 2) {
//     console.log('updateKbn=' + updateKbn)
//     return EboardResponseType.PARAMETER_INVALID;
//   }
//   if (updateKbn === 1 && InputStream === undefined) {
//     console.log('InputStream=' + InputStream)
//     return EboardResponseType.PARAMETER_INVALID;
//   }

  return EboardResponseType.SUCCESS;
}
