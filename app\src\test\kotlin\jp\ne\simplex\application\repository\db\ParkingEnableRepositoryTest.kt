package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.UpdateParkingLotAvailability
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingEnablePojo
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_ENABLE
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.shared.DSLContextEx.Companion.selectParkingEnableBy
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.stubJwtRequestUser
import jp.ne.simplex.stub.stubParkingEnablePojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDateTime

class ParkingEnableRepositoryTest : AbstractTestContainerTest() {
    private val currentDateTime = LocalDateTime.of(2024, 11, 12, 13, 45, 30)
    private lateinit var repository: ParkingEnableRepository

    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)
        repository = ParkingEnableRepository(dslContext)
    }

    @AfterEach
    fun tearDown() {
        MockLocalDateTime.close()
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_ENABLE)
    }

    @Nested
    @DisplayName("駐車場利用区画の利用状況を更新できること")
    inner class Scenario1 {
        private val buildingCode = "000130305"
        private val parkingLotCode = "001"

        private val existsData = stubParkingEnablePojo(
            buildingCode = buildingCode,
            parkingLotCode = parkingLotCode,
            parkingLotEnable = "0"
        )

        @BeforeEach
        fun setup() {
            dslContext.saveParkingEnablePojo(existsData)
        }

        @Test
        @DisplayName("変更対象の駐車場区画がある時、区画利用可否が設定した値に変更されていること")
        fun case1() {
            val status = "1"
            val user = stubJwtRequestUser()
            val updateData = UpdateParkingLotAvailability(
                parkingLotId = ParkingLot.Id(
                    Building.Code.of(buildingCode),
                    ParkingLot.Code.of(parkingLotCode)
                ),
                status = ParkingLot.Status.fromValue(status)!!
            )
            repository.upsert(user, updateData)

            val result = dslContext.selectParkingEnableBy(updateData.parkingLotId)

            // レコード数チェック
            assertEquals(1, result.size)

            // 全項目の突合
            val firstRecord = result.first()
            assertEquals(existsData.buildingCode, firstRecord.buildingCode)
            assertEquals(existsData.parkingLotCode, firstRecord.parkingLotCode)
            assertEquals(status, firstRecord.parkingLotEnable)
            assertEquals(existsData.creator, firstRecord.creator)
            assertEquals(existsData.creationDate, firstRecord.creationDate)
            assertEquals(existsData.creationTime, firstRecord.creationTime)
            assertEquals(user.value, firstRecord.updater)
            assertEquals(currentDateTime.yyyyMMdd().toInt(), firstRecord.updateDate)
            assertEquals(currentDateTime.HHmmss().toInt(), firstRecord.updateTime)
            assertEquals(existsData.deleteFlag, firstRecord.deleteFlag)
        }

        @Test
        @DisplayName("変更対象の駐車場区画がない時、設定した区画利用可否のレコードが追加されていること")
        fun case2() {
            val newBuildingCode = "000130306"
            val newParkingLotCode = "002"
            val status = "1"
            val user = stubJwtRequestUser()
            val updateData = UpdateParkingLotAvailability(
                parkingLotId = ParkingLot.Id(
                    Building.Code.of(newBuildingCode),
                    ParkingLot.Code.of(newParkingLotCode)
                ),
                status = ParkingLot.Status.fromValue(status)!!
            )
            repository.upsert(user, updateData)

            // レコード数チェック
            val countResult = dslContext.select().from(PARKING_ENABLE)
                .fetchInto(ParkingEnablePojo::class.java)
            assertEquals(2, countResult.size)

            // 全項目の突合
            val result = dslContext.selectParkingEnableBy(updateData.parkingLotId)
            val lastRecord = result.last()
            assertEquals(newBuildingCode, lastRecord.buildingCode)
            assertEquals(newParkingLotCode, lastRecord.parkingLotCode)
            assertEquals(status, lastRecord.parkingLotEnable)
            assertEquals(user.value, lastRecord.creator)
            assertEquals(currentDateTime.yyyyMMdd().toInt(), lastRecord.creationDate)
            assertEquals(currentDateTime.HHmmss().toInt(), lastRecord.creationTime)
            assertEquals(null, lastRecord.updater)
            assertEquals(null, lastRecord.updateDate)
            assertEquals(null, lastRecord.updateTime)
            assertEquals("0", lastRecord.deleteFlag)
        }
    }
}
