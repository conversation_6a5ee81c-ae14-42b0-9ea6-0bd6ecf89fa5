package jp.ne.simplex.application.batch

import jp.ne.simplex.application.repository.db.BatchExecuteHistoryRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import org.slf4j.LoggerFactory
import org.springframework.dao.DuplicateKeyException
import java.time.LocalDateTime

/** バッチ処理を行うインターフェース。 */
interface BatchInterface {
    companion object {
        val BATCH_USER = AuthInfo.Batch().getRequestUser()
        private val log = LoggerFactory.getLogger(BatchInterface::class.java)
    }

    val batchExecuteHistoryRepository: BatchExecuteHistoryRepositoryInterface
    val batchType: BatchType

    /** バッチ共通処理 */
    fun execute(executeOption: ExecuteOptionType?) {
        /**
         * 定期バッチの処理
         */
        if (executeOption == null) {
            executeOnScheduleTask()
            return
        }

        /**
         * 定期バッチの処理が失敗に終わった場合に、手動リランを行う場合を考慮する
         * 同一タイミングでのタスク起動は手動実行の場合は発生しないため、バッチ実行履歴の内容を確認せずに処理を実行する
         * */
        if (isAcceptableOption(executeOption)) {
            executeOnRerun(executeOption)
            return
        } else {
            throw IllegalArgumentException("not acceptable batch option.[EXECUTE_OPTION: ${executeOption}, BATCH_TYPE: ${batchType}]")
        }
    }

    /** バッチ処理(個別バッチの実装) */
    fun executeInternal(executeOption: ExecuteOptionType?)

    fun isAcceptableOption(executeOption: ExecuteOptionType): Boolean

    /**
     * 定期バッチの処理の最初にバッチ実行履歴を登録する
     * すでに同一のバッチ種別で同一の実行日時が登録されている場合は、EventBridgeの重複実行によるプロセス起動と判断し、バッチ処理を行わない
     * https://docs.aws.amazon.com/ja_jp/eventbridge/latest/userguide/eb-troubleshooting.html#eb-rule-triggered-more-than-once
     * */
    private fun executeOnScheduleTask() {
        val isExecutable = registerHistory()
        if (isExecutable) {
            executeInternal(null)
        }
        else if (BatchType.EXCLUSIVE_PROPERTY_FOR_E_CLOUD == batchType
            || BatchType.EXCLUSIVE_PROPERTY_FOR_FC_NYUKO == batchType ){
            executeOnRerun(null)
        }
    }

    private fun executeOnRerun(executeOption: ExecuteOptionType?) {
        upsertHistory()
        executeInternal(executeOption)
    }

    private fun registerHistory(): Boolean {
        try {
            batchExecuteHistoryRepository.register(
                batchType = batchType,
                executeDateTime = LocalDateTime.now(),
            )
            return true
        } catch (e: DuplicateKeyException) {
            log.info("Batch history already exists. Skipping registration.")
            return false
        }
    }

    private fun upsertHistory() {
        batchExecuteHistoryRepository.upsert(
            batchType = batchType,
            executeDateTime = LocalDateTime.now(),
        )

    }
}

