package jp.ne.simplex.mock

import org.flywaydb.core.Flyway
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.test.context.DynamicPropertyRegistry
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName
import java.util.function.Supplier

@Testcontainers
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class DatabaseTestContainer private constructor() {

    @Container
    var container: PostgreSQLContainer<*> =
        PostgreSQLContainer(
            DockerImageName.parse("testcontainers/local-postgres:latest")
                .asCompatibleSubstituteFor("postgres")
        )
            .withDatabaseName("gpb_dev")
            .withUsername("postgres")
            .withPassword("simplex")
            .withCommand("--max-connections=5000")
            .waitingFor(
                Wait.forLogMessage(".*ready for start up.*\\s", 1)
            )
            .withCommand()

    init {
        container.start()
    }

    companion object {
        private val INSTANCE = DatabaseTestContainer()

        private val maxConnectionPool = Supplier<Any> { "10" }

        fun getInstance(): DatabaseTestContainer = INSTANCE
    }

    fun overrideDatasource(registry: DynamicPropertyRegistry) {
        registry.add(
            "spring.datasource.url",
            container::getJdbcUrl,
        )
        registry.add(
            "spring.datasource.driver-class-name",
            container::getDriverClassName,
        )
        registry.add(
            "spring.datasource.username",
            container::getUsername,
        )
        registry.add(
            "spring.datasource.password",
            container::getPassword,
        )
        registry.add(
            "spring.datasource.hikari.maximum-pool-size",
            maxConnectionPool,
        )
    }

    fun cleanUpSchema() {
        val flyway = Flyway.configure()
            .dataSource(container.jdbcUrl, container.username, container.password)
            .cleanDisabled(true)
            .baselineOnMigrate(true)
            .load()
        flyway.migrate()
    }
}
