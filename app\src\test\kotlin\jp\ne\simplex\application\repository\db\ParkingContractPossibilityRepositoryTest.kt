package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.application.repository.db.extension.ParkingContractPossibilityEx.Companion.toParkingContractPossibility
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingContractPossibilityPojo
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_CONTRACT_POSSIBILITY
import jp.ne.simplex.exception.DBValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.shared.CoroutineHelper.Companion.runAsyncTasks
import jp.ne.simplex.shared.DSLContextEx.Companion.selectBy
import jp.ne.simplex.stub.stubExternalRequestUser
import jp.ne.simplex.stub.stubJwtRequestUser
import jp.ne.simplex.stub.stubParkingContractPossibility
import jp.ne.simplex.stub.stubParkingContractPossibilityPojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class ParkingContractPossibilityRepositoryTest : AbstractTestContainerTest() {
    private lateinit var repository: ParkingContractPossibilityRepository

    override fun beforeEach() {
        repository = ParkingContractPossibilityRepository(dslContext)
        dslContext.saveParkingContractPossibilityPojo(
            stubParkingContractPossibilityPojo(DEFAULT_ORDER_CODE.value)
        )
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_CONTRACT_POSSIBILITY)
    }

    companion object {
        private val DEFAULT_ORDER_CODE = Building.OrderCode.of("1000001")
        private val BATCH_USER = AuthInfo.Batch().getRequestUser()
    }

    @Nested
    @DisplayName("駐車場契約可否取得(ID指定)の検証")
    inner class Scenario1 {
        @Test
        @DisplayName("受注コードに一致するデータが存在しない場合nullを返すこと")
        fun case1() {
            val result = repository.findBy(Building.OrderCode.of("1000002"))
            assertNull(result)
        }

        @Test
        @DisplayName("受注コードに一致するデータが存在する場合ParkingContractPossibilityを返すこと")
        fun case2() {
            val result = repository.findBy(DEFAULT_ORDER_CODE)
            assertNotNull(result)
            assertEquals(stubParkingContractPossibility(DEFAULT_ORDER_CODE.value), result)
        }
    }

    @Nested
    @DisplayName("駐車場契約可否取得(ID指定)レコードロックありの検証")
    inner class Scenario2 {
        @Test
        @DisplayName("受注コードに一致するデータが存在しない場合nullを返すこと")
        fun case1() {
            dslContext.transaction { config ->
                val result = repository.findByIdForUpdate(
                    config,
                    Building.OrderCode.of("1000002")
                )
                assertNull(result)
            }
        }

        @Test
        @DisplayName("受注コードに一致するデータが存在する場合ParkingContractPossibilityを返すこと")
        fun case2() {
            dslContext.transaction { config ->
                val result = repository.findByIdForUpdate(config, DEFAULT_ORDER_CODE)
                assertNotNull(result)
                assertEquals(stubParkingContractPossibility(DEFAULT_ORDER_CODE.value), result)
            }
        }

        @Test
        @DisplayName("レコードロックでタイムアウトが発生した場合、DBValidationExceptionがthrowされること")
        fun case3() {
            // task1の実行後1秒後に、task2を実行する
            // task1は成功するが、task2はレコードロックで失敗する
            runAsyncTasks(
                task1 = {
                    assertDoesNotThrow {
                        dslContext.transaction { config ->
                            repository.findByIdForUpdate(config, DEFAULT_ORDER_CODE)
                            Thread.sleep(3000)
                        }
                    }
                },
                task2 = {
                    val err = assertThrows<DBValidationException> {
                        dslContext.transaction { config ->
                            repository.findByIdForUpdate(config, DEFAULT_ORDER_CODE, 1)
                        }
                    }
                    assertEquals(ErrorType.DB_UPDATE_FAILED, err.type)
                    assertEquals(
                        ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format().errorMessage,
                        err.detail.errorMessage
                    )
                },
                delayBetweenTasks = 1000,
            )
        }
    }

    @Nested
    @DisplayName("駐車場契約可否情報登録の検証")
    inner class Scenario3 {

        @Nested
        @DisplayName("実行ユーザー別駐車場契約可否情報登録の検証")
        inner class Scenario3x1 {

            val request = stubParkingContractPossibility(
                orderCode = "1000002",
                firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
            )

            private fun assertRegister(
                request: ParkingContractPossibility,
                result: ParkingContractPossibilityPojo?
            ) {
                assertNotNull(result)
                assertEquals(
                    request.orderCode.value,
                    result.orderCode
                )
                assertEquals(
                    request.firstParkingContractPossibility.value,
                    result.isFirstParkingContractPossible
                )
                assertEquals(
                    request.secondParkingContractPossibility.value,
                    result.isSecondParkingContractPossible
                )
                assertEquals(
                    request.isAutoJudge.value,
                    result.isAutoJudge
                )
                assertNotNull(result.creationDate)
                assertNotNull(result.creationTime)
                assertNotNull(result.updateDate)
                assertNotNull(result.updateTime)
                assertEquals("0", result.deleteFlag)

            }

            @Test
            @DisplayName("BATCHユーザーが駐車場契約可否情報を登録できること")
            fun case1() {
                val requester = BATCH_USER

                dslContext.transaction { config ->
                    repository.register(config, requester, request)
                }.run {
                    dslContext.selectBy(request.orderCode).let {
                        assertRegister(request, it)
                        assertEquals(requester.value, it!!.creator)
                        assertEquals(requester.value, it.updater)
                    }
                }
            }

            @Test
            @DisplayName("DKリンクユーザーが駐車場契約可否情報を登録できること")
            fun case2() {
                val requester = stubJwtRequestUser()

                dslContext.transaction { config ->
                    repository.register(config, requester, request)
                }.run {
                    dslContext.selectBy(request.orderCode).let {
                        assertRegister(request, it)
                        assertEquals(requester.value, it!!.creator)
                        assertEquals(requester.value, it.updater)
                    }
                }
            }

            @Test
            @DisplayName("DKリンクユーザーが駐車場契約可否情報を登録できること")
            fun case4() {
                val requester = stubJwtRequestUser()

                dslContext.transaction { config ->
                    repository.register(config, requester, request)
                }.run {
                    dslContext.selectBy(request.orderCode).let {
                        assertRegister(request, it)
                        assertEquals(requester.value, it!!.creator)
                        assertEquals(requester.value, it.updater)
                    }
                }
            }

            @Test
            @DisplayName("いい物件ユーザーが駐車場契約可否情報を登録できること")
            fun case5() {
                val requester = stubExternalRequestUser(
                    ExternalSystem.EBOARD,
                )

                dslContext.transaction { config ->
                    repository.register(config, requester, request)
                }.run {
                    dslContext.selectBy(request.orderCode).let {
                        assertRegister(request, it)
                        assertEquals(requester.value, it!!.creator)
                        assertEquals(requester.value, it.updater)
                    }
                }
            }

            @Test
            @DisplayName("キマサインユーザーが駐車場契約可否情報を登録できること")
            fun case6() {
                val requester = stubExternalRequestUser(
                    ExternalSystem.KIMAROOM_SIGN,
                )

                dslContext.transaction { config ->
                    repository.register(config, requester, request)
                }.run {
                    dslContext.selectBy(request.orderCode).let {
                        assertRegister(request, it)
                        assertEquals(requester.value, it!!.creator)
                        assertEquals(requester.value, it.updater)
                    }
                }
            }

            @Test
            @DisplayName("WelcomeParkユーザーが駐車場契約可否情報を登録できること")
            fun case7() {
                val requester = stubExternalRequestUser(
                    ExternalSystem.WELCOME_PARK,
                )

                dslContext.transaction { config ->
                    repository.register(config, requester, request)
                }.run {
                    dslContext.selectBy(request.orderCode).let {
                        assertRegister(request, it)
                        assertEquals(requester.value, it!!.creator)
                        assertEquals(requester.value, it.updater)
                    }
                }
            }

            @Test
            @DisplayName("DK-PORTALユーザーが駐車場契約可否情報を登録できること")
            fun case8() {
                val requester = stubExternalRequestUser(
                    ExternalSystem.DK_PORTAL,
                )

                dslContext.transaction { config ->
                    repository.register(config, requester, request)
                }.run {
                    dslContext.selectBy(request.orderCode).let {
                        assertRegister(request, it)
                        assertEquals(requester.value, it!!.creator)
                        assertEquals(requester.value, it.updater)
                    }
                }
            }
        }

        @Test
        @DisplayName("キー重複するデータを登録しようとした場合、DBValidationExceptionがthrowされること")
        fun case2() {
            val model = stubParkingContractPossibility(
                orderCode = DEFAULT_ORDER_CODE.value,
                firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
            )

            val err = assertThrows<DBValidationException> {
                dslContext.transaction { config ->
                    repository.register(config, BATCH_USER, model)
                }
            }
            assertEquals(
                ErrorMessage.DATABASE_UPDATE_CONFLICT_OCCURRED.format().errorMessage,
                err.detail.errorMessage
            )
        }
    }

    @Nested
    @DisplayName("駐車場契約可否情報更新の検証")
    inner class Scenario4 {
        @Test
        @DisplayName("駐車場契約可否情報が想定通り更新されること")
        fun case1() {
            val updateUser =
                AuthInfo.Jwt(Employee.Code("test"), Office.Code.of("xxx"), null).getRequestUser()
            val entity = dslContext.selectBy(DEFAULT_ORDER_CODE)
            assertNotNull(entity)
            var model = entity.toParkingContractPossibility()
            model = model.copy(
                firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                secondParkingContractPossibility = ContractPossibility.IMPOSSIBLE,
                isAutoJudge = ContractPossibilityAutoJudge.AUTO,
            )
            dslContext.transaction { config ->
                repository.update(config, updateUser, model)
            }

            val result = dslContext.selectBy(DEFAULT_ORDER_CODE)
            assertNotNull(result)
            assertEquals("0", result.isFirstParkingContractPossible)
            assertEquals("1", result.isSecondParkingContractPossible)
            assertEquals("1", result.isAutoJudge)
            assertEquals(entity.creationDate, result.creationDate)
            assertEquals(entity.creationTime, result.creationTime)
            assertEquals(entity.creator, result.creator)
            assertNotEquals(entity.updateDate, result.updateDate)
            assertNotEquals(entity.updateDate, result.updateDate)
            assertEquals(updateUser.value, result.updater)
            assertEquals("0", result.deleteFlag)
        }
    }
}
