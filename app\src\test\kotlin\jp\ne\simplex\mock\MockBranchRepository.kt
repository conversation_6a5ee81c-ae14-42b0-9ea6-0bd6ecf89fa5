package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.repository.db.BranchRepositoryInterface

class MockBranchRepository(
    val getKtBranchFunc: (branchCode: Branch.Code?) -> Branch? = { _ -> null },
    val getKtBranchListFunc: () -> List<Branch> = { -> emptyList() },
    val getBranchRelatedToLeasingFunc: (branchCode: Branch.Code?) -> Branch.Code? = { _ -> null },
    val getLeasingRelatedToBranchFunc: (branchCode: Branch.Code) -> Branch.Code? = { _ -> null },
) : BranchRepositoryInterface {
    override fun getKtBranch(branchCode: Branch.Code?): Branch? {
        return getKtBranchFunc(branchCode)
    }

    override fun getKtBranchList(): List<Branch> {
        return getKtBranchListFunc()
    }

    override fun getBranchRelatedToLeasing(branchCode: Branch.Code): Branch.Code? {
        return getBranchRelatedToLeasingFunc(branchCode)
    }

    override fun getLeasingRelatedToBranch(branchCode: Branch.Code): Branch.Code? {
        return getLeasingRelatedToBranchFunc(branchCode)
    }
}
