package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.extension.ParkingReservationEx.Companion.toParkingReservationInfo
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingReservationPojo
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_RESERVATION
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_RESERVATION_V
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.jooq.Field
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@Repository
class ParkingReservationRepository(private val context: DSLContext) :
    ParkingReservationRepositoryInterface {

    private val applicationTypeValueList = listOf(
        ParkingReservation.Type.AUTO_APPLICATION.value,
        ParkingReservation.Type.MANUAL_APPLICATION.value,
    )

    private val activeStatusValueList = listOf(
        ParkingReservation.Status.TENTATIVE.value,
        ParkingReservation.Status.RESERVATION.value,
    )

    override fun register(
        requestUser: AuthInfo.RequestUser,
        param: RegisterParkingReservation
    ) {

        val currentDateTime = LocalDateTime.now()

        // <!-- @formatter:off -->
        context.insertInto(PARKING_RESERVATION)
            .set(PARKING_RESERVATION.PARKING_RESERVATION_ID, param.parkingReservationId.value)
            .set(PARKING_RESERVATION.BUILDING_CODE, param.parkingLotId.buildingCode.value)
            .set(PARKING_RESERVATION.PARKING_LOT_CODE, param.parkingLotId.parkingLotCode.value)
            .set(PARKING_RESERVATION.RESERVE_TYPE, param.reservationType.value)
            .set(PARKING_RESERVATION.RESERVE_STATUS, param.parkingReservationStatus.value)
            .set(PARKING_RESERVATION.RESERVE_START_DATETIME, param.reserveStartDatetime?.truncatedTo(ChronoUnit.SECONDS))
            .set(PARKING_RESERVATION.RESERVE_END_DATETIME, param.reserveEndDatetime?.truncatedTo(ChronoUnit.SECONDS))
            .set(PARKING_RESERVATION.RECEPTION_STAFF, param.receptionStaff)
            .set(PARKING_RESERVATION.RESERVER_NAME, param.reserverName)
            .set(PARKING_RESERVATION.RESERVER_TEL, param.reserverTel?.value)
            .set(PARKING_RESERVATION.RESERVER_SYSTEM, param.requestSource.value)
            .set(PARKING_RESERVATION.LINKED_BUILDING_CODE, param.linkedBuildingCode?.value)
            .set(PARKING_RESERVATION.LINKED_ROOM_CODE, param.linkedRoomCode?.value)
            .set(PARKING_RESERVATION.REMARKS, param.remarks?.value)
            .set(PARKING_RESERVATION.RECEPTION_DATE, currentDateTime.yyyyMMdd().toInt())
            .set(PARKING_RESERVATION.EBOARD_PARKING_RESERVATION_ID, ParkingReservation.EBoardId.create().value)
            .set(PARKING_RESERVATION.DELETE_FLAG, "0")
            .set(createInputMetaData(requestUser, currentDateTime))
            .set(createUpdateMetaData(requestUser, currentDateTime))
            .execute()
        // <!-- @formatter:on -->
    }

    override fun update(
        requestUser: AuthInfo.RequestUser,
        param: UpdateParkingReservation,
        ovverideReservationType: ParkingReservation.Type?
    ) {
        val currentDateTime = LocalDateTime.now()

        // @formatter:off
       val query =  context.update(PARKING_RESERVATION)
            .set(PARKING_RESERVATION.RESERVE_STATUS, param.status.value)
            .set(PARKING_RESERVATION.RESERVE_START_DATETIME, param.reserveStartDatetime?.truncatedTo(ChronoUnit.SECONDS))
            .set(PARKING_RESERVATION.RESERVE_END_DATETIME, param.reserveEndDatetime?.truncatedTo(ChronoUnit.SECONDS))
            .set(PARKING_RESERVATION.RECEPTION_STAFF, param.receptionStaff)
            .set(PARKING_RESERVATION.RESERVER_NAME, param.reserverName)
            .set(PARKING_RESERVATION.RESERVER_TEL, param.reserverTel?.value)
            .set(PARKING_RESERVATION.RESERVER_SYSTEM, param.requestSource.value)
            .set(PARKING_RESERVATION.REMARKS, param.remarks?.value)
            .set(createUpdateMetaData(requestUser, currentDateTime))
        // @formatter:on

        // オーバーライド用の予約タイプがnullでない場合のみ設定する
        ovverideReservationType?.let { query.set(PARKING_RESERVATION.RESERVE_TYPE, it.value) }

        query.where(PARKING_RESERVATION.PARKING_RESERVATION_ID.eq(param.id.value))
            .execute()
    }

    override fun cancel(requestUser: AuthInfo.RequestUser, param: CancelParkingReservation) {
        cancelOrFinishImpl(
            requestUser,
            param.id,
            param.status,
            param.remarks
        )
    }

    override fun cancelApplication(
        requestUser: AuthInfo.RequestUser,
        parkingReservationId: ParkingReservation.Id,
        param: CancelApplicationParkingReservation
    ) {
        cancelOrFinishImpl(
            requestUser,
            parkingReservationId,
            param.status,
            param.remarks
        )
    }

    override fun finish(requestUser: AuthInfo.RequestUser, param: FinishParkingReservation) {
        cancelOrFinishImpl(
            requestUser,
            param.id,
            param.status,
            null,
            false
        )
    }

    private fun cancelOrFinishImpl(
        requestUser: AuthInfo.RequestUser,
        parkingReservationId: ParkingReservation.Id,
        status: ParkingReservation.Status,
        remarks: ParkingReservation.Remarks?,
        updateRemarks: Boolean = true,
    ) {
        context.update(PARKING_RESERVATION)
            .set(PARKING_RESERVATION.RESERVE_STATUS, status.value)
            .let { if (updateRemarks) it.set(PARKING_RESERVATION.REMARKS, remarks?.value) else it }
            .set(createUpdateMetaData(requestUser, LocalDateTime.now()))
            .where(PARKING_RESERVATION.PARKING_RESERVATION_ID.eq(parkingReservationId.value))
            .execute()
    }

    private fun createInputMetaData(
        requestUser: AuthInfo.RequestUser,
        dateTime: LocalDateTime
    ): Map<Field<*>, Any?> {
        return mapOf<Field<*>, Any?>(
            PARKING_RESERVATION.CREATION_DATE to dateTime.yyyyMMdd().toInt(),
            PARKING_RESERVATION.CREATION_TIME to dateTime.HHmmss().toInt(),
            PARKING_RESERVATION.CREATOR to requestUser.value,
        )
    }

    private fun createUpdateMetaData(
        requestUser: AuthInfo.RequestUser,
        dateTime: LocalDateTime
    ): Map<Field<*>, Any?> {
        return mapOf<Field<*>, Any?>(
            PARKING_RESERVATION.UPDATE_DATE to dateTime.yyyyMMdd().toInt(),
            PARKING_RESERVATION.UPDATE_TIME to dateTime.HHmmss().toInt(),
            PARKING_RESERVATION.UPDATER to requestUser.value,
        )
    }

    override fun findById(id: ParkingReservation.Id): ParkingReservationInfo? {

        return context.selectFrom(PARKING_RESERVATION_V)
            .where(PARKING_RESERVATION_V.PARKING_RESERVATION_ID.eq(id.value))
            .and(PARKING_RESERVATION_V.DELETE_FLAG.eq("0"))
            .fetchOneInto(ParkingReservationPojo::class.java)?.toParkingReservationInfo()
    }

    override fun findTentative(parkingLotId: ParkingLot.Id): ParkingReservationInfo? {
        return context.selectFrom(PARKING_RESERVATION_V)
            .where(PARKING_RESERVATION_V.BUILDING_CODE.eq(parkingLotId.buildingCode.value))
            .and(PARKING_RESERVATION_V.PARKING_LOT_CODE.eq(parkingLotId.parkingLotCode.value))
            .and(PARKING_RESERVATION_V.RESERVE_STATUS.eq(ParkingReservation.Status.TENTATIVE.value))
            .and(PARKING_RESERVATION_V.DELETE_FLAG.eq("0"))
            .fetchOneInto(ParkingReservationPojo::class.java)?.toParkingReservationInfo()
    }

    override fun findActiveApplication(parkingLotId: ParkingLot.Id): ParkingReservationInfo? {
        return context.selectFrom(PARKING_RESERVATION_V)
            .where(PARKING_RESERVATION_V.BUILDING_CODE.eq(parkingLotId.buildingCode.value))
            .and(PARKING_RESERVATION_V.PARKING_LOT_CODE.eq(parkingLotId.parkingLotCode.value))
            .and(PARKING_RESERVATION_V.RESERVE_TYPE.`in`(applicationTypeValueList))
            .and(PARKING_RESERVATION_V.RESERVE_STATUS.`in`(activeStatusValueList))
            .and(PARKING_RESERVATION_V.DELETE_FLAG.eq("0"))
            .fetchOneInto(ParkingReservationPojo::class.java)?.toParkingReservationInfo()
    }

    override fun findActiveReservations(
        buildingCode: Building.Code,
        parkingLotCode: ParkingLot.Code?
    ): List<ParkingReservationInfo> {

        return context.selectFrom(PARKING_RESERVATION_V)
            .where(PARKING_RESERVATION_V.BUILDING_CODE.eq(buildingCode.value))
            .apply {
                parkingLotCode?.value?.let { and(PARKING_RESERVATION_V.PARKING_LOT_CODE.eq(it)) }
            }
            .and(PARKING_RESERVATION_V.RESERVE_STATUS.`in`(activeStatusValueList))
            .and(PARKING_RESERVATION_V.DELETE_FLAG.eq("0"))
            .fetchInto(ParkingReservationPojo::class.java)
            .map { it.toParkingReservationInfo()!! }
    }

    override fun findActiveParkingReservationByOrderCode(orderCode: Building.OrderCode): List<ParkingReservationInfo> {
        return context.select().from(PARKING_RESERVATION_V)
            .where(PARKING_RESERVATION_V.BUILDING_CODE.like("${orderCode.value}%"))
            .and(PARKING_RESERVATION_V.PARKING_LOT_CODE.isNotNull())
            .and(PARKING_RESERVATION_V.RESERVE_STATUS.`in`(activeStatusValueList))
            .and(PARKING_RESERVATION_V.DELETE_FLAG.eq("0"))
            .orderBy(
                PARKING_RESERVATION_V.BUILDING_CODE.asc(),
                PARKING_RESERVATION_V.PARKING_LOT_CODE.asc(),
                // 受付日の降順で表示する
                PARKING_RESERVATION_V.RECEPTION_DATE.desc(),
                PARKING_RESERVATION_V.PARKING_RESERVATION_ID.asc()
            )
            .fetchInto(ParkingReservationPojo::class.java)
            .mapNotNull { it.toParkingReservationInfo() }
    }

    override fun findActiveReservationsForBatch(): List<ParkingReservationInfo> {
        // 区画と紐づき、受付かつ申込かつ過日の駐車場予約情報を取得する
        return context.select().from(PARKING_RESERVATION_V)
            .where(PARKING_RESERVATION_V.BUILDING_CODE.isNotNull())
            .and(PARKING_RESERVATION_V.PARKING_LOT_CODE.isNotNull())
            .and(PARKING_RESERVATION_V.RESERVE_STATUS.eq(ParkingReservation.Status.RESERVATION.value))
            .and(PARKING_RESERVATION_V.RESERVE_TYPE.`in`(applicationTypeValueList))
            .and(PARKING_RESERVATION_V.RESERVE_START_DATETIME.isNotNull())
            .and(PARKING_RESERVATION_V.RESERVE_START_DATETIME.lt(LocalDateTime.now().truncatedTo(ChronoUnit.DAYS)))
            .and(PARKING_RESERVATION_V.DELETE_FLAG.eq("0"))
            .orderBy(
                PARKING_RESERVATION_V.BUILDING_CODE.asc(),
                PARKING_RESERVATION_V.PARKING_LOT_CODE.asc(),
            )
            .fetchInto(ParkingReservationPojo::class.java)
            .mapNotNull { it.toParkingReservationInfo() }
    }
}

interface ParkingReservationRepositoryInterface {
    /** 駐車場予約情報登録 */
    fun register(requestUser: AuthInfo.RequestUser, param: RegisterParkingReservation)

    /** 駐車場予約情報更新 */
    fun update(
        requestUser: AuthInfo.RequestUser,
        param: UpdateParkingReservation,
        ovverideReservationType: ParkingReservation.Type? = null
    )

    /** 駐車場予約情報取消 */
    fun cancel(requestUser: AuthInfo.RequestUser, param: CancelParkingReservation)

    /** 駐車場申込予約情報取消(建物コード・駐車場コード指定) */
    fun cancelApplication(
        requestUser: AuthInfo.RequestUser,
        parkingReservationId: ParkingReservation.Id,
        param: CancelApplicationParkingReservation
    )

    /** 駐車場予約情報完了 */
    fun finish(requestUser: AuthInfo.RequestUser, param: FinishParkingReservation)

    /** 駐車場予約情報取得(ID指定) */
    fun findById(id: ParkingReservation.Id): ParkingReservationInfo?

    /** 有効な仮予約情報を取得する */
    fun findTentative(parkingLotId: ParkingLot.Id): ParkingReservationInfo?

    /** 有効な駐車場申込予約情報取得*/
    fun findActiveApplication(parkingLotId: ParkingLot.Id): ParkingReservationInfo?

    /** 有効な駐車場予約情報リスト取得(建物コード・駐車場コード指定) */
    fun findActiveReservations(
        buildingCode: Building.Code,
        parkingLotCode: ParkingLot.Code?
    ): List<ParkingReservationInfo>

    /** 処理中駐車場予約情報検索（受注コード指定） */
    fun findActiveParkingReservationByOrderCode(orderCode: Building.OrderCode): List<ParkingReservationInfo>

    /** 駐車場予約完了バッチ対象を取得 */
    fun findActiveReservationsForBatch(): List<ParkingReservationInfo>
}
