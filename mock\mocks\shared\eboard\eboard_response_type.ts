export const enum EboardResponseType {
  PARAMETER_INVALID = 201,
  PARAMETER_MISSING = 202,
  AUTH_FAILED = 203,
  TOKEN_EXPIRED = 204,
  DATABASE_ERROR = 101,
  MAIL_SEND_ERROR = 102,
  OTHER_ERROR = 103,
  MESSAGE_NONE = 11,
  NO_PROPERTIES_FOUND = 10,
  NO_STORES_FOUND = 20,
  MAX_COUNT_EXCEEDED = 30,
  DATA_ALREADY_EXISTS = 40,
  ROOM_NOT_FOUND = 1,
  SUCCESS = 0,
}

export const eboardResponseMapping: Record<EboardResponseType, string> = {
  //パラメータ値が規定外であった場合(数値のパラメータに数字以外の文字列が入っていた等)
  [EboardResponseType.PARAMETER_INVALID]: '【エラー】パラメータが規定外です。',

  // 必須となるパラメータ(auth_code/entry_date/function/(各機能で必須とするもの))が無い場合
  [EboardResponseType.PARAMETER_MISSING]: '【エラー】パラメータ不正です。',

  // 認証に失敗した場合
  [EboardResponseType.AUTH_FAILED]: '【エラー】認証に失敗しました。',

  // // アクセストークンの有効期限が切れていた場合
  [EboardResponseType.TOKEN_EXPIRED]:
    '【エラー】アクセストークンの有効期限が切れています。',

  // DAOがExceptionまたは、エラー値を返した場合
  [EboardResponseType.DATABASE_ERROR]: 'データベースにアクセスできませんでした。',

  // お問合わせ登録で、メール送信の失敗があった場合
  [EboardResponseType.MAIL_SEND_ERROR]: 'メール送信エラーが発生しました。',

  // その他の障害が発生した場合
  [EboardResponseType.OTHER_ERROR]: 'その他の障害が発生しました。',

  // 前回更新日時がDB更新日時(バッチによる作成時刻等)以降の場合
  [EboardResponseType.MESSAGE_NONE]: 'メッセージ無し',

  // 検索結果が0件であった場合
  [EboardResponseType.NO_PROPERTIES_FOUND]:
    '該当する物件が見つかりませんでした。条件を変えて検索してみてください。',

  // 検索結果が0件であった場合
  [EboardResponseType.NO_STORES_FOUND]:
    '該当する店舗が見つかりませんでした。条件を変えて検索してみてください。',

  // 要求された数が受け付け可能な最大件数をオーバーする場合
  [EboardResponseType.MAX_COUNT_EXCEEDED]:
    '最大件数をオーバーする為、受け付けできませんでした。',

  // 要求された物件または検索条件がすでに存在する場合
  [EboardResponseType.DATA_ALREADY_EXISTS]: 'そのデータはすでに存在します。',

  // 要求された物件が無い場合
  [EboardResponseType.ROOM_NOT_FOUND]: 'room not found',

  // 正常に処理が完了した場合
  [EboardResponseType.SUCCESS]: '',
};
