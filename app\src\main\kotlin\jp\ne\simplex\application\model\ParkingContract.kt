package jp.ne.simplex.application.model

class UpdateParkingContractIsAutoJudge(
    // 受注コード
    val orderCode: Building.OrderCode,
    // 駐車場契約可否自動判定有無
    val isAutoJudge: ParkingContractPossibility.ContractPossibilityAutoJudge
)

data class ParkingContractPossibility(
    // 受注コード
    val orderCode: Building.OrderCode,
    // 駐車場一台目契約可否ステータス
    val firstParkingContractPossibility: ContractPossibility,
    // 駐車場二台目契約可否ステータス
    val secondParkingContractPossibility: ContractPossibility,
    // 駐車場契約可否自動判定有無
    val isAutoJudge: ContractPossibilityAutoJudge,
) {
    /** 契約可否 */
    enum class ContractPossibility(val value: String) {
        POSSIBLE("0"), // 可
        IMPOSSIBLE("1"), // 不可
        REQUIRED_CONFIRM("2"); // 要問合せ

        companion object {
            fun fromValue(value: String?): ContractPossibility {
                return ContractPossibility.entries.find { it.value == value } ?: IMPOSSIBLE
            }
        }
    }

    /** 契約可否自動判定有無 */
    enum class ContractPossibilityAutoJudge(val value: String) {
        MANUAL("0"), // 自動判定なし
        AUTO("1"); // 自動判定あり

        companion object {
            fun fromValue(value: String?): ContractPossibilityAutoJudge {
                return entries.find { it.value == value } ?: AUTO
            }
        }
    }
}
