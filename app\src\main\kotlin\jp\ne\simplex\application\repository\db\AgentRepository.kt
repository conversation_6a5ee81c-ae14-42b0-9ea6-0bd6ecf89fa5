package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Agent
import jp.ne.simplex.application.repository.db.extension.AgentEx.Companion.getAgent
import jp.ne.simplex.db.jooq.gen.tables.pojos.AgentPojo
import jp.ne.simplex.db.jooq.gen.tables.references.AGENT
import org.jooq.DSLContext
import org.springframework.stereotype.Repository

@Repository
class AgentRepository(private val context: DSLContext) : AgentRepositoryInterface {

    override fun listBy(eCodeList: List<Agent.ECode>): List<Agent> {
        return context.select().from(AGENT)
            .where(AGENT.CHUKAI_GYOSHA_CD.`in`(eCodeList.map { it.value }))
            .fetchInto(AgentPojo::class.java)
            .mapNotNull { it.getAgent() }
    }
}

interface AgentRepositoryInterface {

    fun listBy(eCodeList: List<Agent.ECode>): List<Agent>
}
