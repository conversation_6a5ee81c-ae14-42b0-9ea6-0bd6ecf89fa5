package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.application.repository.db.OfficeRepositoryInterface

class MockOfficeRepository(
    val findByOfficeFunc: (officeCode: Office.Code?) -> Office? = { _ -> null },
    val findByBranchFunc: (branchCode: Branch.Code?) -> Office? = { _ -> null },
) : OfficeRepositoryInterface {
    override fun findBy(officeCode: Office.Code?): Office? {
        return findByOfficeFunc(officeCode)
    }

    override fun findBy(branchCode: Branch.Code?): Office? {
        return findByBranchFunc(branchCode)
    }
}
