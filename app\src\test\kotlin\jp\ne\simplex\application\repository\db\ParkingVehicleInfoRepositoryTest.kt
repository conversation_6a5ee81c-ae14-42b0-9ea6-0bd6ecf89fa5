package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingVehicleInfo
import jp.ne.simplex.db.jooq.gen.tables.pojos.ParkingVehicleInfoFilePojo
import jp.ne.simplex.db.jooq.gen.tables.records.ParkingVehicleInfoFileRecord
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_VEHICLE_INFO_FILE
import jp.ne.simplex.shared.CoroutineHelper.Companion.runAsyncTasks
import jp.ne.simplex.stub.stubJwtRequestUser
import jp.ne.simplex.stub.stubParkingVehicleInfo
import jp.ne.simplex.stub.stubParkingVehicleInfoFilePojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class ParkingVehicleInfoRepositoryTest : AbstractTestContainerTest() {

    private lateinit var repository: ParkingVehicleInfoRepository

    override fun beforeEach() {
        repository = ParkingVehicleInfoRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_VEHICLE_INFO_FILE)
    }

    private val firstUpdateData = stubParkingVehicleInfo(
        roomCd = "00000",
        parkingLotCategory = ParkingLot.Category.KEI,
        landTransportName = "仙台",
        type = "000",
        businessCategory = "た",
        leftNumber = "00",
        rightNumber = "00",
        manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.NISSAN,
        carModelName = "シンプレクス1",
        parkingCertIssueSign = ParkingVehicleInfo.ParkingCertIssueSign.NONE,
        parkingCertComment = "テストコメント1",
    )

    private val secondUpdateData = stubParkingVehicleInfo(
        roomCd = "00000",
        parkingLotCategory = ParkingLot.Category.KEI,
        landTransportName = "仙台",
        type = "000",
        businessCategory = "た",
        leftNumber = "00",
        rightNumber = "00",
        manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.NISSAN,
        carModelName = "シンプレクス2",
        parkingCertIssueSign = ParkingVehicleInfo.ParkingCertIssueSign.NONE,
        parkingCertComment = "テストコメント2",
    )

    // カラム突合をします(tandemSign, lightVehicleSignは除く)
    private fun assertEqualsAll(expected: ParkingVehicleInfo, actual: ParkingVehicleInfoFilePojo) {

        assertEquals(expected.tenantContractNumber.value, actual.tenantContractNumber)
        assertEquals(expected.roomCd?.value, actual.roomCode)
        assertEquals(expected.vehicleNumber.landTransportName, actual.landTransportName_1)
        assertEquals(expected.vehicleNumber.type, actual.type_1)
        assertEquals(expected.vehicleNumber.businessCategory, actual.businessCategory_1)
        assertEquals(expected.vehicleNumber.leftNumber, actual.leftNumber_1)
        assertEquals(expected.vehicleNumber.rightNumber, actual.rightNumber_1)
        assertEquals(
            expected.vehicleCategory.manufacturerDivision?.byte,
            actual.manufacturerDivision_1
        )
        assertEquals(expected.vehicleCategory.carModelName, actual.carModelName_1)
        assertEquals(expected.parkingCertIssueSign?.byte, actual.parkingCertIssueSign_1)
        assertEquals(expected.parkingCertComment, actual.parkingCertComment_1)
    }

    @Nested
    @DisplayName("駐車場車種情報更新APIの検証")
    inner class Scenario1 {

        @Nested
        @DisplayName("新規登録処理の検証")
        inner class Scenario1x1 {

            @Test
            @DisplayName("PK以外全てNULLの場合PARKING_VEHICLE_INFO_FILEにレコードがINSERTされること")
            fun case1() {
                // setup
                val parkingVehicleInfo = stubParkingVehicleInfo()

                try {
                    repository.upsert(stubJwtRequestUser(), parkingVehicleInfo)
                } catch (e: Exception) {
                    fail()
                }

                // verify
                val result = dslContext.select().from(PARKING_VEHICLE_INFO_FILE)
                    .where(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER.eq(parkingVehicleInfo.tenantContractNumber.value))
                    .fetchInto(ParkingVehicleInfoFilePojo::class.java)

                // レコード数の突合
                assertEquals(1, result.size)

                // 全項目の突合
                assertEqualsAll(parkingVehicleInfo, result.first())
            }

            @Test
            @DisplayName("PK以外全てデータがある場合PARKING_VEHICLE_INFO_FILEにレコードがINSERTされること")
            fun case2() {
                // setup
                val parkingVehicleInfo = stubParkingVehicleInfo(
                    roomCd = "00000",
                    parkingLotCategory = ParkingLot.Category.KEI,
                    landTransportName = "仙台",
                    type = "000",
                    businessCategory = "た",
                    leftNumber = "00",
                    rightNumber = "00",
                    manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA,
                    carModelName = "シンプレクス",
                    parkingCertIssueSign = ParkingVehicleInfo.ParkingCertIssueSign.ISSUED,
                    parkingCertComment = "テストコメント",
                )

                try {
                    repository.upsert(stubJwtRequestUser(), parkingVehicleInfo)
                } catch (e: Exception) {
                    fail()
                }

                // verify
                val result = dslContext.select().from(PARKING_VEHICLE_INFO_FILE)
                    .where(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER.eq(parkingVehicleInfo.tenantContractNumber.value))
                    .fetchInto(ParkingVehicleInfoFilePojo::class.java)

                // レコード数の突合
                assertEquals(1, result.size)

                // 全項目の突合
                assertEqualsAll(parkingVehicleInfo, result.first())
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の値で永続化されること")
            fun case3() {
                // task1の実行後1ミリ秒後に、task2を実行する
                // 両方とも成功し、task2の値で永続化される
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            repository.upsert(stubJwtRequestUser(), firstUpdateData)
                        }
                    },
                    task2 = {
                        assertDoesNotThrow {
                            repository.upsert(stubJwtRequestUser(), secondUpdateData)
                        }
                    },
                    delayBetweenTasks = 100,
                )

                // verify
                val result = dslContext.select().from(PARKING_VEHICLE_INFO_FILE)
                    .where(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER.eq(secondUpdateData.tenantContractNumber.value))
                    .fetchInto(ParkingVehicleInfoFilePojo::class.java)

                // レコード数の突合
                assertEquals(1, result.size)

                // 全項目の突合
                assertEqualsAll(secondUpdateData, result.first())
            }
        }

        @Nested
        @DisplayName("更新処理の検証")
        inner class Scenario1x2 {

            private val existsData = stubParkingVehicleInfoFilePojo(
                roomCd = "00000",
                tandemSign = "0",
                landTransportName = "仙台",
                type = "000",
                businessCategory = "た",
                leftNumber = "00",
                rightNumber = "00",
                manufacturerDivision = ParkingVehicleInfo.Category.ManufacturerDivision.TOYOTA,
                carModelName = "シンプレクス",
                lightVehicleSign = "0",
                parkingCertIssueSign = "0",
                parkingCertComment = "テストコメント",
            )

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.save(
                    table = PARKING_VEHICLE_INFO_FILE,
                    recordConstructor = { p: ParkingVehicleInfoFilePojo ->
                        ParkingVehicleInfoFileRecord(p)
                    },
                    pojos = listOf(existsData)
                )
            }

            @Test
            @DisplayName("リクエスト値が設定された(manufacturerDivision, carModelName, parkingCertComment)レコードが更新されること")
            fun case1() {
                repository.upsert(stubJwtRequestUser(), firstUpdateData)

                // verify
                val result = dslContext.select().from(PARKING_VEHICLE_INFO_FILE)
                    .where(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER.eq(firstUpdateData.tenantContractNumber.value))
                    .fetchInto(ParkingVehicleInfoFilePojo::class.java)

                // レコード数の突合
                assertEquals(1, result.size)

                // 全項目の突合
                assertEqualsAll(firstUpdateData, result.first())
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側で永続化されること")
            fun case2() {
                // task1の実行後1ミリ秒後に、task2を実行する
                // 両方とも成功し、task2の値で永続化される
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            repository.upsert(stubJwtRequestUser(), firstUpdateData)
                        }
                    },
                    task2 = {
                        assertDoesNotThrow {
                            repository.upsert(stubJwtRequestUser(), secondUpdateData)
                        }
                    },
                    delayBetweenTasks = 100,
                )

                // verify
                val result = dslContext.select().from(PARKING_VEHICLE_INFO_FILE)
                    .where(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER.eq(secondUpdateData.tenantContractNumber.value))
                    .fetchInto(ParkingVehicleInfoFilePojo::class.java)

                // レコード数の突合
                assertEquals(1, result.size)

                // 全項目の突合
                assertEqualsAll(secondUpdateData, result.first())
            }
        }
    }
}

class ParkingLotCategoryToTandemSignTest : AbstractTestContainerTest() {
    data class CaseSet(
        val parkingLotCategory: ParkingLot.Category,
        val tandemSign: String,
    )

    companion object {
        @JvmStatic
        fun testCases() = listOf(
            CaseSet(ParkingLot.Category.SINGLE, "0"),
            CaseSet(ParkingLot.Category.PARALLEL, "1"),
            CaseSet(ParkingLot.Category.KEI, "0"),
            CaseSet(ParkingLot.Category.MULTI_LEVEL, "1"),
        )
    }

    private lateinit var repository: ParkingVehicleInfoRepository

    override fun beforeEach() {
        repository = ParkingVehicleInfoRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_VEHICLE_INFO_FILE)
    }

    @ParameterizedTest
    @MethodSource("testCases")
    @DisplayName("DB永続時のparkingLotCategory -> tandemSign のマッピング")
    fun case(caseSet: CaseSet) {
        // setup
        val parkingVehicleInfo = stubParkingVehicleInfo(
            parkingLotCategory = caseSet.parkingLotCategory
        )

        try {
            repository.upsert(stubJwtRequestUser(), parkingVehicleInfo)
        } catch (e: Exception) {
            fail()
        }

        // verify
        val result = dslContext.select().from(PARKING_VEHICLE_INFO_FILE)
            .where(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER.eq(parkingVehicleInfo.tenantContractNumber.value))
            .fetchInto(ParkingVehicleInfoFilePojo::class.java)

        assertEquals(caseSet.tandemSign, result.first().tandemSign)
    }
}

class ParkingLotCategoryToLightVehicleSignTest : AbstractTestContainerTest() {
    data class CaseSet(
        val parkingLotCategory: ParkingLot.Category,
        val lightVehicleSign: Byte,
    )

    companion object {
        @JvmStatic
        fun testCases() = listOf(
            CaseSet(ParkingLot.Category.SINGLE, 0),
            CaseSet(ParkingLot.Category.PARALLEL, 0),
            CaseSet(ParkingLot.Category.KEI, 1),
            CaseSet(ParkingLot.Category.MULTI_LEVEL, 0),
        )
    }

    private lateinit var repository: ParkingVehicleInfoRepository

    override fun beforeEach() {
        repository = ParkingVehicleInfoRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PARKING_VEHICLE_INFO_FILE)
    }

    @ParameterizedTest
    @MethodSource("testCases")
    @DisplayName("DB永続時のparkingLotCategory -> lightVehicleSign のマッピング")
    fun case(caseSet: CaseSet) {
        // setup
        val parkingVehicleInfo = stubParkingVehicleInfo(
            parkingLotCategory = caseSet.parkingLotCategory
        )

        try {
            repository.upsert(stubJwtRequestUser(), parkingVehicleInfo)
        } catch (e: Exception) {
            fail()
        }

        // verify
        val result = dslContext.select().from(PARKING_VEHICLE_INFO_FILE)
            .where(PARKING_VEHICLE_INFO_FILE.TENANT_CONTRACT_NUMBER.eq(parkingVehicleInfo.tenantContractNumber.value))
            .fetchInto(ParkingVehicleInfoFilePojo::class.java)

        assertEquals(caseSet.lightVehicleSign, result.first().lightVehicleSign_1)
    }
}
