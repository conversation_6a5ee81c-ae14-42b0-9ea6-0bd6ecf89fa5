package jp.ne.simplex.mock

import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.Mockito.CALLS_REAL_METHODS
import java.time.LocalDate
import java.time.LocalDateTime

class MockLocalDateTime {

    companion object {
        private lateinit var dateTimeMock: MockedStatic<LocalDateTime>
        private lateinit var dateMock: MockedStatic<LocalDate>

        fun setNow(datetime: LocalDateTime) {
            close()

            dateTimeMock = Mockito.mockStatic(LocalDateTime::class.java, CALLS_REAL_METHODS)
            dateTimeMock.`when`<Any> { LocalDateTime.now() }.thenReturn(datetime)

            dateMock = Mockito.mockStatic(LocalDate::class.java, CALLS_REAL_METHODS)
            dateMock.`when`<Any> { LocalDate.now() }.thenReturn(datetime.toLocalDate())
        }

        fun close() {
            try {
                dateTimeMock.close()
                dateMock.close()
            } catch (e: Exception) {
                println(e)
            }
        }
    }
}
