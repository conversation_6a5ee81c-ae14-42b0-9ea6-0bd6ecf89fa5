package jp.ne.simplex.authentication

import jp.ne.simplex.application.model.ExternalSystem
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.web.server.Cookie.SameSite

@ConfigurationProperties("app.auth")
class AuthConfig(
    val jwt: Jwt,
    val apiKey: ApiKey
) {

    class Jwt(
        val accessToken: TokenConfig,
        val refreshToken: TokenConfig,
        val cookie: CookieConfig,
    ) {
        class TokenConfig(
            val secretId: String,
            val validityPeriod: Long
        )

        class CookieConfig(
            val httpOnly: Boolean,
            val secure: Boolean,
            private val sameSite: SameSite,
            val maxAge: Long
        ) {
            fun getSameSiteAttribute(): String? {
                return if (secure) sameSite.attributeValue() else null
            }
        }

        companion object {
            const val COOKIE_ACCESS_TOKEN_KEY = "accessToken"

            const val COOKIE_REFRESH_TOKEN_KEY = "refreshToken"
        }
    }

    class ApiKey(
        val secretId: String,
        val externalSystemName: ExternalSystemName
    ) {
        class ExternalSystemName(
            val eboard: String,
            val kimaroomSign: String,
            val dkPortal: String,
            val welcomePark: String,
        )

        fun getExternalSystem(secretName: String): ExternalSystem? {
            return when (secretName) {
                externalSystemName.eboard -> ExternalSystem.EBOARD
                externalSystemName.kimaroomSign -> ExternalSystem.KIMAROOM_SIGN
                externalSystemName.dkPortal -> ExternalSystem.DK_PORTAL
                externalSystemName.welcomePark -> ExternalSystem.WELCOME_PARK
                else -> null
            }
        }

    }
}

