# 外部APIの認証

## シーケンス

```mermaid
sequenceDiagram
    participant Client as RestClient
    participant Interceptor as RequestInterceptor
    participant To<PERSON><PERSON>ana<PERSON> as TokenManager
    box rgba(255, 255, 255, 0.1) 外部システム（いい物件ボード/DK-PORTAL...）
        participant AuthAPI as Authentication API
        participant OtherAPI as Other API
    end
    Client ->> Interceptor: Add Authorization Header (Token)
    Interceptor ->> TokenManager: Get Token
    TokenManager -->> Interceptor: Return Token or Null

    rect rgba(60, 179, 113, 0.2)
        alt Token is Valid
            Interceptor ->> OtherAPI: Call API with Existing Token

            rect rgba(255, 250, 0, 0.2)
                alt Token Expired (401 Unauthorized)
                    Interceptor ->> AuthAPI: Re-authenticate with ID/Pass
                    AuthAPI -->> Interceptor: Return New Token
                    Interceptor ->> TokenManager: Update Token
                    Interceptor ->> OtherAPI: Retry API Call with New Token
                end
            end
            OtherAPI -->> Interceptor: Return Response
            Interceptor -->> Client: Return Response
        end
    end

    rect rgba(200, 0, 0, 0.2)
        alt Token is Null
            Interceptor ->> AuthAPI: Authenticate with ID/Pass
            AuthAPI -->> Interceptor: Return Token
            Interceptor ->> TokenManager: Update Token
            Interceptor ->> OtherAPI: Call API with New Token
            OtherAPI -->> Interceptor: Return Response
            Interceptor -->> Client: Return Response
        end
    end



```

## 認証情報の管理方法

以下を考慮し、添付の設計にしている

- セキュリティ観点から、ソースコード上に、APIの認証情報などの機密情報を持たないようにするため
- システム稼働中に誤って認証情報が変わってしまった場合でも、ダウンタイムなしで認証情報の変更ができるようにするため

- ![](./images/external_api/auth_info_management.jpg)