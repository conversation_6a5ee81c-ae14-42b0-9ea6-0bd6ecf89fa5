package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.PropertyMaintenance
import jp.ne.simplex.application.model.Room
import jp.ne.simplex.db.jooq.gen.tables.pojos.PropertyMaintenanceInfoPojo
import jp.ne.simplex.db.jooq.gen.tables.records.PropertyMaintenanceInfoRecord
import jp.ne.simplex.db.jooq.gen.tables.references.PROPERTY_MAINTENANCE_INFO
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.shared.AssertionHelper.Companion.assertNullProperties
import jp.ne.simplex.shared.CoroutineHelper
import jp.ne.simplex.shared.DSLContextEx.Companion.selectPropertyMaintenanceBy
import jp.ne.simplex.stub.stubJwtRequestUser
import jp.ne.simplex.stub.stubProperty
import jp.ne.simplex.stub.stubPropertyMaintenanceInfoPojo
import jp.ne.simplex.stub.stubUpdatePropertyMaintenance
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import java.time.LocalDateTime
import kotlin.test.assertEquals

class PropertyMaintenanceRepositoryTest : AbstractTestContainerTest() {
    private lateinit var repository: PropertyMaintenanceRepository

    private val currentDateTime = LocalDateTime.of(2024, 11, 12, 13, 45, 30)

    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)

        repository = PropertyMaintenanceRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PROPERTY_MAINTENANCE_INFO)
    }

    companion object {
        private const val DEFAULT_BUILDING_CODE_1 = "123456789"
        private const val DEFAULT_BUILDING_CODE_2 = "987654321"
        private const val DEFAULT_EMPLOYEE_CODE_1 = "000123"
        private const val DEFAULT_EMPLOYEE_CODE_2 = "000456"
    }

    private val existsData = stubPropertyMaintenanceInfoPojo(
        buildingCode = DEFAULT_BUILDING_CODE_1,
        publishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
        adAmount = 1000,
        ffPeriod = 2F,
        applicationRent = 10000,
        applicationKeyMoney = 20000,
        applicationDeposit = 30000,
        creationDate = 20231112,
        creationTime = 121530,
        updater = DEFAULT_EMPLOYEE_CODE_1,
        updateDate = 20240101,
        updateTime = 120515
    )

    @AfterEach
    fun tearDown() {
        MockLocalDateTime.close()
    }

    @Nested
    @DisplayName("物件メンテナンスAPIの検証")
    inner class Scenario1 {
        @Nested
        @DisplayName("公開指示情報の新規登録の検証")
        inner class Scenario1x1 {
            @Test
            @DisplayName("リクエストパラメータの必須項目（buildingCode, roomCode, publishStatus）が全てある場合にPROPERTY_MAINTENANCE_INFO テーブルにレコードがINSERTされること")
            fun case1() {
                // setup
                val propertyMaintenancePublishStatus =
                    stubUpdatePropertyMaintenance().getUpdatePublishStatus()

                repository.updatePublishStatus(
                    stubJwtRequestUser(employeeCode = DEFAULT_EMPLOYEE_CODE_1),
                    listOf(propertyMaintenancePublishStatus)
                )

                // verify
                val result =
                    dslContext.selectPropertyMaintenanceBy(propertyMaintenancePublishStatus.id)
                assertEquals(1, result.size)

                val record = result.first()
                //公開指示情報が更新されていること
                assertEquals(
                    propertyMaintenancePublishStatus.id.buildingCode.value,
                    record.buildingCd
                )
                assertEquals(propertyMaintenancePublishStatus.id.roomCode.value, record.roomCd)
                assertEquals(
                    propertyMaintenancePublishStatus.publishStatus.value.toByte(),
                    record.listingCategoryGoodRoomNet
                )
                assertEquals("20241112", record.creationDate.toString())
                assertEquals("134530", record.creationTime.toString())
                assertEquals(DEFAULT_EMPLOYEE_CODE_1, record.updater)
                assertEquals("20241112", record.updateDate.toString())
                assertEquals("134530", record.updateTime.toString())
                //更新対象ではないレコードについては更新されていないこと
                assertNullProperties(
                    record,
                    listOf(
                        "comment", // コメント
                        "adAmount", // AD・FF情報カラム
                        "ffAmount", // AD・FF情報カラム
                        "rentalPrice", // 金額・備考情報カラム
                        "keyMoney", // 金額・備考情報カラム
                        "securityDeposit", // 金額・備考情報カラム
                        "listingCategory", // 未使用カラム
                        "homesPanoramaSendFlag", // 未使用カラム
                        "adUnit", // 未使用カラム
                        "lowRepairCostSpecification", // 未使用カラム
                    )
                )
            }
        }

        @Nested
        @DisplayName("公開指示情報の更新の検証")
        inner class Scenario1x2 {
            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.save(
                    table = PROPERTY_MAINTENANCE_INFO,
                    recordConstructor = { p: PropertyMaintenanceInfoPojo ->
                        PropertyMaintenanceInfoRecord(p)
                    },
                    pojos = listOf(existsData)
                )
            }

            private val firstUpdateData = stubUpdatePropertyMaintenance(
                buildingCode = DEFAULT_BUILDING_CODE_1,
                roomCode = existsData.roomCd,
                publishStatus = PropertyMaintenance.PublishStatus.PRIVATE
            ).getUpdatePublishStatus()

            private val secondUpdateData = stubUpdatePropertyMaintenance(
                buildingCode = DEFAULT_BUILDING_CODE_1,
                roomCode = existsData.roomCd,
                publishStatus = PropertyMaintenance.PublishStatus.PUBLIC
            ).getUpdatePublishStatus()

            @Test
            @DisplayName("リクエスト値が設定されたレコードが更新されること")
            fun case2() {
                repository.updatePublishStatus(
                    stubJwtRequestUser(employeeCode = DEFAULT_EMPLOYEE_CODE_2),
                    listOf(firstUpdateData)
                )

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(firstUpdateData.id)
                assertEquals(1, result.size)

                val record = result.first()
                //公開指示情報が更新されていること
                assertEquals(firstUpdateData.id.buildingCode.value, record.buildingCd)
                assertEquals(firstUpdateData.id.roomCode.value, record.roomCd)
                assertEquals(
                    firstUpdateData.publishStatus.value.toByte(),
                    record.listingCategoryGoodRoomNet
                )
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45, 30) で固定しているため、以下の値が設定される
                assertEquals(DEFAULT_EMPLOYEE_CODE_2, record.updater)
                assertEquals("20241112", record.updateDate.toString())
                assertEquals("134530", record.updateTime.toString())
                //更新対象ではないレコードについては更新されていないこと
                assertEquals(existsData.adAmount, record.adAmount)
                assertEquals(existsData.ffAmount, record.ffAmount)
                assertEquals(existsData.rentalPrice, record.rentalPrice)
                assertEquals(existsData.keyMoney, record.keyMoney)
                assertEquals(existsData.securityDeposit, record.securityDeposit)
                assertEquals(existsData.creationDate, record.creationDate)
                assertEquals(existsData.creationTime, record.creationTime)
            }

            @Test
            @DisplayName("同時リクエストが行われた際に後からリクエストされた値で永続化されること")
            fun case3() {
                // task1の実行後1ミリ秒後に、task2を実行する
                // 両方とも成功し、task2の値で永続化される
                CoroutineHelper.runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            repository.updatePublishStatus(
                                stubJwtRequestUser(),
                                listOf(firstUpdateData)
                            )
                        }
                    },
                    task2 = {
                        assertDoesNotThrow {
                            repository.updatePublishStatus(
                                stubJwtRequestUser(),
                                listOf(secondUpdateData)
                            )
                        }
                    },
                    delayBetweenTasks = 100,
                )

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(secondUpdateData.id)
                assertEquals(1, result.size)

                val record = result.first()
                //公開指示情報が後からリクエストされた側の値で更新されていること
                assertEquals(secondUpdateData.id.buildingCode.value, record.buildingCd)
                assertEquals(secondUpdateData.id.roomCode.value, record.roomCd)
                assertEquals(
                    secondUpdateData.publishStatus.value.toByte(),
                    record.listingCategoryGoodRoomNet
                )
                //更新対象ではないレコードについては更新されていないこと
                assertEquals(existsData.adAmount, record.adAmount)
                assertEquals(existsData.ffAmount, record.ffAmount)
                assertEquals(existsData.rentalPrice, record.rentalPrice)
                assertEquals(existsData.keyMoney, record.keyMoney)
                assertEquals(existsData.securityDeposit, record.securityDeposit)
            }
        }

        @Nested
        @DisplayName("リクエストが複数レコードの検証")
        inner class Scenario1x3 {

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.save(
                    table = PROPERTY_MAINTENANCE_INFO,
                    recordConstructor = { p: PropertyMaintenanceInfoPojo ->
                        PropertyMaintenanceInfoRecord(p)
                    },
                    pojos = listOf(existsData)
                )
            }

            @Test
            @DisplayName("リクエストが複数あり、PKの値がすでにDBに存在するリクエストはUPDATE、存在しないリクエストはINSERTされること")
            fun case4() {
                // setup
                // 更新対象のリクエスト
                val updateData = stubUpdatePropertyMaintenance(
                    buildingCode = DEFAULT_BUILDING_CODE_1,
                    roomCode = existsData.roomCd,
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC,
                ).getUpdatePublishStatus()

                // 新規登録対象のリクエスト
                val insertData = stubUpdatePropertyMaintenance(
                    buildingCode = DEFAULT_BUILDING_CODE_2,
                    roomCode = existsData.roomCd,
                    publishStatus = PropertyMaintenance.PublishStatus.PRIVATE,
                ).getUpdatePublishStatus()

                repository.updatePublishStatus(
                    stubJwtRequestUser(),
                    listOf(updateData, insertData)
                )

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(updateData.id)
                assertEquals(1, result.size)
                val record = result.first()
                //公開指示情報がリクエストの値で更新されていること
                assertEquals(
                    updateData.publishStatus.value.toByte(),
                    record.listingCategoryGoodRoomNet
                )

                val insertResult = dslContext.selectPropertyMaintenanceBy(insertData.id)
                assertEquals(1, insertResult.size)
                val insertRecord = insertResult.first()
                //公開指示情報がリクエストされた値で更新されていること
                assertEquals(
                    insertData.publishStatus.value.toByte(),
                    insertRecord.listingCategoryGoodRoomNet
                )
            }
        }

    }

    @Nested
    inner class Scenario2 {

        @Nested
        @DisplayName("AD・FF情報の新規登録の検証")
        inner class Scenario2x1 {
            @Test
            @DisplayName("リクエストパラメータの必須項目（buildingCode, roomCode, adAmount, ffPeriod）が全てある場合にPROPERTY_MAINTENANCE_INFO テーブルにレコードがINSERTされること")
            fun case1() {
                // setup
                val propertyMaintenanceAdFf = stubUpdatePropertyMaintenance(
                    buildingCode = DEFAULT_BUILDING_CODE_1,
                    roomCode = existsData.roomCd,
                    advertisementFee = existsData.adAmount!!.plus(1000),
                    frontFreerentPeriod = existsData.ffAmount!!.toInt().plus(1).toFloat()
                ).getUpdateAdFf(stubProperty())

                repository.updateAdFf(
                    stubJwtRequestUser(employeeCode = DEFAULT_EMPLOYEE_CODE_1),
                    listOf(propertyMaintenanceAdFf)
                )

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(propertyMaintenanceAdFf.id)
                Assertions.assertEquals(1, result.size)

                val record = result.first()
                //AD・FF情報が更新されていること
                assertEquals(propertyMaintenanceAdFf.id.buildingCode.value, record.buildingCd)
                assertEquals(propertyMaintenanceAdFf.id.roomCode.value, record.roomCd)
                assertEquals(propertyMaintenanceAdFf.adFf.advertisementFee, record.adAmount)
                assertEquals(
                    propertyMaintenanceAdFf.adFf.frontFreerentPeriod?.toBigDecimal(),
                    record.ffAmount
                )
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45, 30) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.creationDate.toString())
                assertEquals("134530", record.creationTime.toString())
                assertEquals(DEFAULT_EMPLOYEE_CODE_1, record.updater)
                assertEquals("20241112", record.updateDate.toString())
                assertEquals("134530", record.updateTime.toString())
                //更新対象ではないレコードについては更新されていないこと
                assertNullProperties(
                    record,
                    listOf(
                        "comment", // コメント
                        "listingCategoryGoodRoomNet", // 公開指示情報カラム
                        "rentalPrice", // 金額・備考情報カラム
                        "securityDeposit", // 金額・備考情報カラム
                        "keyMoney", // 金額・備考情報カラム
                        "listingCategory", // 未使用カラム
                        "homesPanoramaSendFlag", // 未使用カラム
                        "adUnit", // 未使用カラム
                        "lowRepairCostSpecification", // 未使用カラム
                    )
                )
            }
        }

        @Nested
        @DisplayName("AD・FF情報の更新の検証")
        inner class Scenario2x2 {
            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.save(
                    table = PROPERTY_MAINTENANCE_INFO,
                    recordConstructor = { p: PropertyMaintenanceInfoPojo ->
                        PropertyMaintenanceInfoRecord(p)
                    },
                    pojos = listOf(existsData)
                )
            }

            private val firstUpdateData = stubUpdatePropertyMaintenance(
                buildingCode = existsData.buildingCd,
                roomCode = existsData.roomCd,
                advertisementFee = existsData.adAmount!!.plus(1000),
                frontFreerentPeriod = existsData.ffAmount!!.toInt().plus(1).toFloat()
            ).getUpdateAdFf(stubProperty())

            private val secondUpdateData = stubUpdatePropertyMaintenance(
                buildingCode = DEFAULT_BUILDING_CODE_1,
                roomCode = existsData.roomCd,
                advertisementFee = existsData.adAmount!!.plus(2000),
                frontFreerentPeriod = existsData.ffAmount!!.toInt().plus(2).toFloat()
            ).getUpdateAdFf(stubProperty())

            @Test
            @DisplayName("リクエスト値が設定されたレコードが更新されること")
            fun case2() {
                repository.updateAdFf(
                    stubJwtRequestUser(employeeCode = DEFAULT_EMPLOYEE_CODE_2),
                    listOf(firstUpdateData)
                )

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(firstUpdateData.id)
                assertEquals(1, result.size)

                val record = result.first()
                //AD・FF情報が更新されていること
                assertEquals(firstUpdateData.id.buildingCode.value, record.buildingCd)
                assertEquals(firstUpdateData.id.roomCode.value, record.roomCd)
                assertEquals(firstUpdateData.adFf.advertisementFee, record.adAmount)
                assertEquals(
                    firstUpdateData.adFf.frontFreerentPeriod?.toBigDecimal(),
                    record.ffAmount
                )
                assertEquals(DEFAULT_EMPLOYEE_CODE_2, record.updater)
                assertEquals("20241112", record.updateDate.toString())
                assertEquals("134530", record.updateTime.toString())
                //更新対象ではないレコードについては更新されていないこと
                assertEquals(
                    existsData.listingCategoryGoodRoomNet,
                    record.listingCategoryGoodRoomNet
                )
                assertEquals(existsData.rentalPrice, record.rentalPrice)
                assertEquals(existsData.keyMoney, record.keyMoney)
                assertEquals(existsData.securityDeposit, record.securityDeposit)
                assertEquals(existsData.creationDate, record.creationDate)
                assertEquals(existsData.creationTime, record.creationTime)
            }

            @Test
            @DisplayName("AD・FF情報を未設定状態（adAmount = null, ffPeriod = null）に更新するリクエストの場合、DBにnullが永続化されること")
            fun case3() {
                val nullUpdateData = stubUpdatePropertyMaintenance(
                    buildingCode = DEFAULT_BUILDING_CODE_1,
                    roomCode = existsData.roomCd,
                    advertisementFee = null,
                    frontFreerentPeriod = null
                ).getUpdateAdFf(stubProperty())

                repository.updateAdFf(stubJwtRequestUser(), listOf(nullUpdateData))

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(nullUpdateData.id)
                assertEquals(1, result.size)

                val record = result.first()
                //AD・FF情報が更新されていること
                assertEquals(nullUpdateData.id.buildingCode.value, record.buildingCd)
                assertEquals(nullUpdateData.id.roomCode.value, record.roomCd)
                assertEquals(nullUpdateData.adFf.advertisementFee, record.adAmount)
                assertEquals(
                    nullUpdateData.adFf.frontFreerentPeriod?.toBigDecimal(),
                    record.ffAmount
                )
                //更新対象ではないレコードについては更新されていないこと
                assertEquals(
                    existsData.listingCategoryGoodRoomNet,
                    record.listingCategoryGoodRoomNet
                )
                assertEquals(existsData.rentalPrice, record.rentalPrice)
                assertEquals(existsData.keyMoney, record.keyMoney)
                assertEquals(existsData.securityDeposit, record.securityDeposit)
            }

            @Test
            @DisplayName("同時リクエストが行われた際に後からリクエストされた値で永続化されること")
            fun case4() {
                // task1の実行後1ミリ秒後に、task2を実行する
                // 両方とも成功し、task2の値で永続化される
                CoroutineHelper.runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            repository.updateAdFf(stubJwtRequestUser(), listOf(firstUpdateData))
                        }
                    },
                    task2 = {
                        assertDoesNotThrow {
                            repository.updateAdFf(stubJwtRequestUser(), listOf(secondUpdateData))
                        }
                    },
                    delayBetweenTasks = 100,
                )

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(secondUpdateData.id)
                assertEquals(1, result.size)

                val record = result.first()
                //AD・FF情報が後からリクエストされた側の値で更新されていること
                assertEquals(secondUpdateData.id.buildingCode.value, record.buildingCd)
                assertEquals(secondUpdateData.id.roomCode.value, record.roomCd)
                assertEquals(secondUpdateData.adFf.advertisementFee, record.adAmount)
                assertEquals(
                    secondUpdateData.adFf.frontFreerentPeriod?.toBigDecimal(),
                    record.ffAmount
                )
                //更新対象ではないレコードについては更新されていないこと
                assertEquals(
                    existsData.listingCategoryGoodRoomNet,
                    record.listingCategoryGoodRoomNet
                )
                assertEquals(existsData.rentalPrice, record.rentalPrice)
                assertEquals(existsData.keyMoney, record.keyMoney)
                assertEquals(existsData.securityDeposit, record.securityDeposit)
            }

            @Test
            @DisplayName("リクエストが複数あり、PKの値がすでにDBに存在するリクエストはUPDATE、存在しないリクエストはINSERTされること")
            fun case5() {
                // setup
                val updateData = stubUpdatePropertyMaintenance(
                    buildingCode = DEFAULT_BUILDING_CODE_1,
                    roomCode = existsData.roomCd,
                    advertisementFee = existsData.adAmount!!.plus(1000),
                    frontFreerentPeriod = existsData.ffAmount!!.toInt().plus(1).toFloat()
                ).getUpdateAdFf(stubProperty())

                val insertData = stubUpdatePropertyMaintenance(
                    buildingCode = DEFAULT_BUILDING_CODE_2,
                    roomCode = existsData.roomCd,
                    advertisementFee = existsData.adAmount!!.plus(2000),
                    frontFreerentPeriod = existsData.ffAmount!!.toInt().plus(2).toFloat()
                ).getUpdateAdFf(stubProperty())

                repository.updateAdFf(stubJwtRequestUser(), listOf(updateData, insertData))

                // verify
                val result = dslContext.selectPropertyMaintenanceBy(updateData.id)
                assertEquals(1, result.size)
                val record = result.first()
                //AD・FF情報がリクエストの値で更新されていること
                assertEquals(updateData.adFf.advertisementFee, record.adAmount)
                assertEquals(updateData.adFf.frontFreerentPeriod?.toBigDecimal(), record.ffAmount)
                val insertResult = dslContext.selectPropertyMaintenanceBy(insertData.id)
                assertEquals(1, insertResult.size)
                val insertRecord = insertResult.first()
                //AD・FF情報がリクエストの値で更新されていること
                assertEquals(insertData.adFf.advertisementFee, insertRecord.adAmount)
                assertEquals(
                    insertData.adFf.frontFreerentPeriod?.toBigDecimal(),
                    insertRecord.ffAmount
                )
            }
        }

    }

    @Nested
    @DisplayName("指定された物件IDの物件メンテナンス情報を取得できること")
    inner class Scenario3 {

        @Test
        @DisplayName("指定したIDの物件メンテナンス情報のみが取得できること")
        fun case1() {
            // setup
            val data1 = existsData.copy(
                buildingCd = DEFAULT_BUILDING_CODE_1
            )
            val data2 = existsData.copy(
                buildingCd = DEFAULT_BUILDING_CODE_2
            )

            dslContext.savePropertyMaintenanceInfoPojo(data1, data2)

            // execute
            val actual = repository.listBy(
                listOf(
                    Property.Id(
                        Building.Code.of(data1.buildingCd),
                        Room.Code.of(data1.roomCd)
                    )
                )
            )

            // verify
            Assertions.assertEquals(1, actual.size)
            Assertions.assertEquals(data1.buildingCd, actual.first().propertyId.buildingCode.value)
            Assertions.assertEquals(data1.roomCd, actual.first().propertyId.roomCode.value)
            Assertions.assertEquals(
                PropertyMaintenance.PublishStatus.fromValue(data1.listingCategoryGoodRoomNet!!.toInt()),
                actual.first().publishStatus
            )
        }
    }
}
