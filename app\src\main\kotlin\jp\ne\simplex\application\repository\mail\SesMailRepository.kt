package jp.ne.simplex.application.repository.mail

import jp.ne.simplex.application.model.MailProperty
import jp.ne.simplex.application.repository.aws.SimpleEmailServiceClient
import jp.ne.simplex.application.repository.mail.MailRepository.Companion.ENCODE_UTF_8
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Repository
import software.amazon.awssdk.services.ses.model.Body
import software.amazon.awssdk.services.ses.model.Content
import software.amazon.awssdk.services.ses.model.Destination
import software.amazon.awssdk.services.ses.model.Message

@Profile("!dev")
@Repository
class SesMailRepository(
    @Value("\${mail.app.url}")
    private val appUrl: String,
    private val sesClient: SimpleEmailServiceClient,
) : MailRepository {

    override fun sendMailUP(mailProperty: MailProperty) {
        if (mailProperty.fromAddress.value.isEmpty() || mailProperty.toList.isEmpty()) {
            throw Exception("メールの送信に失敗しました。")
        }

        val (subject, body) = createSubjectAndBodyFromFile(mailProperty)

        val destination = Destination.builder()
            .toAddresses(mailProperty.toList.map { it.value })
            .ccAddresses(mailProperty.ccList.map { it.value })
            .build()

        val message = Message.builder()
            .subject(
                Content.builder().charset(ENCODE_UTF_8)
                    .data(subject.ifEmpty { mailProperty.subject })
                    .build()
            ).body(
                Body.builder().text(
                    Content.builder().charset(ENCODE_UTF_8).data(
                        buildString {
                            append(body)
                            if (mailProperty.url.isNotEmpty()) {
                                append("\n\n以下のアドレスをクリックしてください。\n$appUrl${mailProperty.url}")
                            }
                        }
                    ).build()
                ).build()
            ).build()

        return sesClient.send(
            destination = destination,
            msg = message,
            sender = mailProperty.getFrom().address,
        )
    }
}
