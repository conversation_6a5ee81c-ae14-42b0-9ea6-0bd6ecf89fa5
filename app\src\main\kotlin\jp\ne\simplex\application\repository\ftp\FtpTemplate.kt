package jp.ne.simplex.application.repository.ftp

import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.shared.StringExtension.Companion.toMap
import org.apache.commons.net.ftp.FTP
import org.apache.commons.net.ftp.FTPClient
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.io.FileInputStream
import java.nio.file.Path

@Profile("batch")
@Component
class FtpTemplate(
    private val ftpConfigForWelcomePark: FtpConfigForWelcomePark,
    private val ftpConfigForECloud: FtpConfigForECloud,
    private val ftpClient: FTPClient = FTPClient(),
    private val secretManager: SecretManagerRepository,
) {
    private val log = LoggerFactory.getLogger(FtpTemplate::class.java)

    fun connectToWelcomePark(
    ) {
        val secrets = secretManager.getValue(ftpConfigForWelcomePark.secretId).toMap()
        val username = secrets["username"] as String
        val password = secrets["password"] as String

        ftpClient.connect(ftpConfigForWelcomePark.host, 21)
        ftpClient.login(username, password)
        ftpClient.enterLocalPassiveMode()
    }

    fun sendFileToWelcomePark(localFilePath: Path, remoteFileName: String) {
        ftpClient.setFileType(FTP.BINARY_FILE_TYPE)
        ftpClient.changeWorkingDirectory(ftpConfigForWelcomePark.sendDirectory)
        FileInputStream(localFilePath.toFile()).use { inputStream ->
            val success = ftpClient.storeFile(remoteFileName, inputStream)
            if (success) {
                log.info("File successfully uploaded to Welcome Park: $remoteFileName")
            } else {
                log.error("Failed to upload file to Welcome Park: $remoteFileName")
            }
        }
    }

    fun connectToECloud(
    ) {
        val secrets = secretManager.getValue(ftpConfigForECloud.secretId).toMap()
        val username = secrets["username"] as String
        val password = secrets["password"] as String

        ftpClient.connect(ftpConfigForECloud.host, 21)
        ftpClient.login(username, password)
        ftpClient.enterLocalPassiveMode()
    }

    fun sendFileToECloud(localFilePath: Path, remoteFileName: String) {
        ftpClient.setFileType(FTP.BINARY_FILE_TYPE)
        // ディレクトリの指定は不要
        FileInputStream(localFilePath.toFile()).use { inputStream ->
            val success = ftpClient.storeFile(remoteFileName, inputStream)
            if (success) {
                log.info("File successfully uploaded to ECloud: $remoteFileName")
            } else {
                log.error("Failed to upload file to ECloud: $remoteFileName")
            }
        }
    }

    fun disconnect() {
        ftpClient.logout()
        ftpClient.disconnect()
    }
}
