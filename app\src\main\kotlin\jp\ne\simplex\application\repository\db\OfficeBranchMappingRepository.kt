package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.db.jooq.gen.tables.pojos.DaitoBuildingManagementTableVPojo
import jp.ne.simplex.db.jooq.gen.tables.references.DAITO_BUILDING_MANAGEMENT_TABLE_V
import jp.ne.simplex.exception.DataInconsistencyException
import jp.ne.simplex.exception.ErrorMessage
import org.jooq.DSLContext
import org.springframework.stereotype.Repository

@Repository
class OfficeBranchMappingRepository(private val context: DSLContext) :
    OfficeBranchMappingRepositoryInterface {

    private var cache: Map<Office.Code, Branch.Code> = emptyMap()

    override fun get(officeCode: Office.Code?): Branch.Code {
        return try {
            cache.take(officeCode)
        } catch (ex: DataInconsistencyException) {
            updateCache().let { cache.take(officeCode) }
        }
    }

    override fun get(branchCode: Branch.Code?): Office.Code {
        return try {
            cache.take(branchCode)
        } catch (ex: DataInconsistencyException) {
            updateCache().let { cache.take(branchCode) }
        }
    }

    /** DBからデータを取得して、内部キャッシュを更新する */
    private fun updateCache() {
        cache = context.select().from(DAITO_BUILDING_MANAGEMENT_TABLE_V)
            .fetchInto(DaitoBuildingManagementTableVPojo::class.java)
            .filter {
                it.daikenTerminalInstallation != null && it.constructionTerminalInstallation != null
            }
            .associate {
                Office.Code.of(it.daikenTerminalInstallation!!) to
                        Branch.Code.of(it.constructionTerminalInstallation!!)

            }
    }

    private fun Map<Office.Code, Branch.Code>.take(officeCode: Office.Code?): Branch.Code {
        return this[officeCode]
            ?: throw DataInconsistencyException(
                ErrorMessage.OFFICE_BRANCH_MAPPING_ERROR.format("営業所コード:${officeCode?.value}")
            )
    }

    private fun Map<Office.Code, Branch.Code>.take(branchCode: Branch.Code?): Office.Code {

        return this.entries
            .find { entry ->
                entry.value == branchCode?.getPrefix()
                    ?.let { Branch.Code.of(it) }
            }
            ?.key
            ?: throw DataInconsistencyException(
                ErrorMessage.OFFICE_BRANCH_MAPPING_ERROR.format("支店コード:${branchCode?.getPrefix()}")
            )
    }
}

interface OfficeBranchMappingRepositoryInterface {
    fun get(officeCode: Office.Code?): Branch.Code
    fun get(branchCode: Branch.Code?): Office.Code
}
