package jp.ne.simplex.application.repository.db.pojos


import org.slf4j.LoggerFactory


// Jooqによって生成された jp.ne.simplex.db.jooq.gen.tables.pojos.VacantHouseHpPojo は、
// 引数超過（カラム数が多すぎる）で、インスタンス化した際に実行時エラーが発生するため、特別に Pojo を定義している
data class VacantHousePojo(
    val propertyBuildingCd: String,
    val propertyRoomCd: String,
    var changeDivision: String? = null,
    var customerCompletionFlag: String? = null,
) {

    companion object {
        private val log = LoggerFactory.getLogger(VacantHousePojo::class.java)
    }

}
