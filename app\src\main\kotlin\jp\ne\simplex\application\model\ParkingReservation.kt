package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import kotlin.random.Random

/** 「駐車場予約情報更新」を操作する際の Interface */
interface ParkingReservation {
    data class Id private constructor(val value: String) {

        companion object {
            private const val LENGTH = 36
            fun of(value: String): Id {
                return if (value.length == LENGTH) Id(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("駐車場予約ID", LENGTH)
                )
            }

            fun create(): Id {
                return of(UUID.randomUUID().toString())
            }
        }
    }

    data class EBoardId private constructor(val value: String) {
        companion object {
            private const val LENGTH = 3
            fun of(value: String): EBoardId {
                return if (value.length == LENGTH) EBoardId(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_LENGTH.format("いい物件ボード用駐車場予約ID", LENGTH)
                )
            }

            fun create(): EBoardId {
                return of("%03d".format(Random.nextInt(0, 1000)))
            }
        }
    }

    data class Remarks private constructor(val value: String) {
        companion object {
            private const val MAX_LENGTH = 100
            fun of(value: String): Remarks {
                return if (value.length <= MAX_LENGTH) Remarks(value)
                else throw ModelCreationFailedException(
                    ErrorMessage.STRING_MAX_LENGTH.format("予約メモ", MAX_LENGTH)
                )
            }
        }
    }

    enum class Status(val value: String) {
        TENTATIVE("0"), // 仮申込
        RESERVATION("1"), // 受付
        FINISHED("2"), // 完了
        CANCEL("3"); // キャンセル

        companion object {
            fun fromValue(value: String): Status? {
                return entries.find { it.value == value }
            }
        }
    }

    enum class Type(val value: String) {
        AUTO_APPLICATION("0"), // 申込(自動)
        MANUAL_APPLICATION("1"), // 申込(手動)
        WORK("2"), // 作業
        REPLACE("3"), // 場所変更
        ONE_DAY("4"); // 1日利用

        companion object {
            fun fromValue(value: String): Type? {
                return entries.find { it.value == value }
            }
        }
    }

    enum class RequestSource(val value: String) {
        DK_LINK("0"),
        KIMA_SIGN("1"),
        WELCOME_PARK("2");

        companion object {
            fun fromValue(value: String?): RequestSource? {
                return value?.let { entries.find { it.value == value } }
            }

            fun ExternalSystem.toRequestSource(): RequestSource {
                return when (this) {
                    ExternalSystem.KIMAROOM_SIGN -> KIMA_SIGN
                    ExternalSystem.WELCOME_PARK -> WELCOME_PARK
                    ExternalSystem.EBOARD ->
                        throw IllegalArgumentException("Parking reservation cannot be processed from EBOARD.")

                    ExternalSystem.DK_PORTAL ->
                        throw IllegalArgumentException("Parking reservation cannot be processed from DK PORTAL.")
                }
            }
        }
    }
}

class ParkingReservationInfo(
    // 駐車場予約ID
    val id: ParkingReservation.Id,
    // 建物コード
    val buildingCode: Building.Code,
    // 駐車場コード
    val parkingLotCode: ParkingLot.Code?,
    // 予約状態(0: 仮申込, 1: 受付, 2: 完了, 3: キャンセル)
    val status: ParkingReservation.Status,
    // 予約種別(0: 申込(自動), 1: 申込(手動), 2: 作業, 3: 場所変更, 4: 1日利用)
    val reservationType: ParkingReservation.Type,
    // 利用開始日時(yyyyMMdd)
    val reserveStartDatetime: LocalDateTime? = null,
    // 利用終了日時(yyyyMMdd)
    val reserveEndDatetime: LocalDateTime? = null,
    // 予約メモ
    val remarks: ParkingReservation.Remarks? = null,
    // 予約システム
    val requestSource: ParkingReservation.RequestSource? = null,
    // 受付日(yyyyMMdd)
    val receptionDate: LocalDate,
    // いい物件ボード用ID
    val eBoardParkingReservationId: ParkingReservation.EBoardId,
    // 受付担当者氏名
    val receptionStaff: String? = null,
    // 予約者氏名
    val reserverName: String? = null,
    // 予約者電話番号
    val reserverTel: TelephoneNumber? = null,
    // 更新日時
    val updateDateTime: String? = null,
) : ParkingReservation {

    fun getParkingLotId(): ParkingLot.Id? {
        if (parkingLotCode == null) {
            return null
        }
        return ParkingLot.Id(buildingCode, parkingLotCode)
    }

}

// 駐車場予約を操作する(新規・更新・削除)際に使用するインターフェース
sealed interface ParkingReservationAction : ParkingReservation

// 駐車場予約を一括で操作する(更新・削除)際に使用するインターフェース
// 一括操作の際は駐車場の新規登録はできない
sealed interface UpdateOrCancelParkingReservation : ParkingReservationAction

class RegisterParkingReservation private constructor(
    // 駐車場予約ID
    val parkingReservationId: ParkingReservation.Id,
    // 駐車場区画のID
    val parkingLotId: ParkingLot.Id,
    // 予約状態(0: 仮申込, 1: 受付, 2: 完了, 3: キャンセル)
    val parkingReservationStatus: ParkingReservation.Status,
    // 予約種別(0: 申込(自動), 1: 申込(手動), 2: 作業, 3: 場所変更, 4: 1日利用)
    val reservationType: ParkingReservation.Type,
    // 利用開始日時(yyyyMMdd)
    val reserveStartDatetime: LocalDateTime? = null,
    // 利用終了日時(yyyyMMdd)
    val reserveEndDatetime: LocalDateTime? = null,
    // 受付担当者氏名
    val receptionStaff: String? = null,
    // 予約者氏名
    val reserverName: String? = null,
    // 予約者電話番号
    val reserverTel: TelephoneNumber? = null,
    // 予約システム
    val requestSource: ParkingReservation.RequestSource,
    // 同時に仮押さえした物件の建物コード
    val linkedBuildingCode: Building.Code? = null,
    // 同時に仮押さえした物件の部屋コード
    val linkedRoomCode: Room.Code? = null,
    // 予約メモ
    val remarks: ParkingReservation.Remarks? = null,
) : ParkingReservationAction {

    fun isFromWelcomePark(): Boolean {
        return this.requestSource == ParkingReservation.RequestSource.WELCOME_PARK
    }
    
    companion object {
        fun of(
            parkingLotId: ParkingLot.Id,
            parkingReservationStatus: ParkingReservation.Status,
            reservationType: ParkingReservation.Type,
            reserveStartDatetime: LocalDateTime? = null,
            reserveEndDatetime: LocalDateTime? = null,
            receptionStaff: String? = null,
            reserverName: String? = null,
            reserverTel: TelephoneNumber? = null,
            requestSource: ParkingReservation.RequestSource,
            linkedBuildingCode: Building.Code? = null,
            linkedRoomCode: Room.Code? = null,
            remarks: ParkingReservation.Remarks? = null,
        ): RegisterParkingReservation {
            return RegisterParkingReservation(
                parkingReservationId = ParkingReservation.Id.create(),
                parkingLotId = parkingLotId,
                parkingReservationStatus = parkingReservationStatus,
                reservationType = reservationType,
                reserveStartDatetime = reserveStartDatetime,
                reserveEndDatetime = reserveEndDatetime,
                receptionStaff = receptionStaff,
                reserverName = reserverName,
                reserverTel = reserverTel,
                requestSource = requestSource,
                linkedBuildingCode = linkedBuildingCode,
                linkedRoomCode = linkedRoomCode,
                remarks = remarks,
            )
        }
    }
}

class UpdateParkingReservation(
    // 駐車場予約ID
    val id: ParkingReservation.Id,
    // 予約状態(0: 仮申込, 1: 受付, 2: 完了, 3: キャンセル)
    val status: ParkingReservation.Status,
    // 予約種別(0: 申込(自動), 1: 申込(手動), 2: 作業, 3: 場所変更, 4: 1日利用)
    val reservationType: ParkingReservation.Type,
    // 利用開始日時(yyyyMMdd)
    val reserveStartDatetime: LocalDateTime?,
    // 利用終了日時(yyyyMMdd)
    val reserveEndDatetime: LocalDateTime?,
    // 受付担当者氏名
    val receptionStaff: String?,
    // 予約者氏名
    val reserverName: String?,
    // 予約者電話番号
    val reserverTel: TelephoneNumber?,
    // 予約システム
    val requestSource: ParkingReservation.RequestSource,
    // 予約メモ
    val remarks: ParkingReservation.Remarks?,
) : UpdateOrCancelParkingReservation

class CancelParkingReservation private constructor(
    // 駐車場予約ID
    val id: ParkingReservation.Id,
    // 予約状態(0: 仮申込, 1: 受付, 2: 完了, 3: キャンセル)
    val status: ParkingReservation.Status,
    // 予約メモ
    val remarks: ParkingReservation.Remarks?,
) : UpdateOrCancelParkingReservation {
    companion object {
        fun of(
            id: ParkingReservation.Id,
            remarks: ParkingReservation.Remarks?
        ): CancelParkingReservation {
            return CancelParkingReservation(
                id = id, status = ParkingReservation.Status.CANCEL, remarks = remarks
            )
        }
    }
}

class FinishParkingReservation private constructor(
    // 駐車場予約ID
    val id: ParkingReservation.Id,
    // 予約状態(0: 仮申込, 1: 受付, 2: 完了, 3: キャンセル)
    val status: ParkingReservation.Status,
) : UpdateOrCancelParkingReservation {
    companion object {
        fun of(
            id: ParkingReservation.Id,
        ): FinishParkingReservation {
            return FinishParkingReservation(
                id = id, status = ParkingReservation.Status.FINISHED
            )
        }
    }
}

// 申込予約は建物コードと駐車場コードで一意に定まる
class CancelApplicationParkingReservation(
    // 駐車場区画のID
    val parkingLotId: ParkingLot.Id,
    // 予約状態(0: 仮申込, 1: 受付, 2: 完了, 3: キャンセル)
    val status: ParkingReservation.Status,
    // 予約種別(0: 申込(自動), 1: 申込(手動), 2: 作業, 3: 場所変更, 4: 1日利用)
    val reservationType: ParkingReservation.Type,
    // 予約メモ
    val remarks: ParkingReservation.Remarks?,
) : ParkingReservationAction
