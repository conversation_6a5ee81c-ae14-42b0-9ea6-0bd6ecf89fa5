server:
  port: ${SERVER_PORT:8082}
  servlet:
    context-path: /api/v2

spring:
  main:
    banner-mode: off
  application:
    name: app
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${SPRING_DATASOURCE_URL:****************************************}
    username: ${SPRING_DATASOURCE_USERNAME:app}
    password: ${SPRING_DATASOURCE_PASSWORD:simplex}
    hikari:
      maximum-pool-size: 500
      max-lifetime: 1800000
      keepalive-time: 30000
      connection-test-query: select 1
  flyway:
    enabled: true
    baseline-on-migrate: true

swagger:
  enabled: false

springdoc:
  api-docs:
    path: /api-docs

app:
  auth:
    jwt:
      access-token:
        secret-id: access_token_key
        validity-period: 300
      refresh-token:
        secret-id: refresh_token_key
        validity-period: 10800
      cookie:
        http-only: true
        secure: true
        same-site: STRICT
        max-age: 86400
    api-key:
      secret-id: external_api_key_list
      external-system-name:
        eboard: eboard
        kimaroom-sign: kimaroomSign
        dk-portal: dkPortal
        welcome-park: welcomePark
    single-sign-on:
      enabled: ${SAML_ENABLED:false}
      saml:
        entity-id: ${SAML_ENTITY_ID:https://dnabiamfpl201.kentaku.co.jp}
        dk-link-url: ${SAML_DK_LINK_URL:https://stg-dk-link.kentaku.co.jp}
        secret-id: saml_verification_cert
      access-key:
        secret-id: access_key_hash
  log:
    # デフォルトはJSON形式でログ出力する（フォーマットは、logback-spring.xml参照）
    appender: console-json
  parking:
    detail:
      # 駐車場配置図ベースURL
      layout-image-base-path: ${PARKING_LAYOUT_IMAGE_BASE_FILE_PATH:https://image.eheya.net/eboard/}
    contract-possibility.update.parallel-num: ${PARKING_CONTRACT_POSSIBILITY_UPDATE_PARALLEL_NUM:30}
  property:
    exclusive-property-data.parallel-num: ${EXCLUSIVE_PROPERTY_DATA_PARALLEL_NUM:10}
management:
  endpoints:
    web:
      base-path: /
      exposure:
        include: health

mail:
  parking:
    reservation:
      send: ${MAIL_PARKING_RESERVATION_SEND:false}
      from.name: ${MAIL_PARKING_RESERVATION_FROM_NAME:いい物件ボード}
      recept.address: ${MAIL_PARKING_RESERVATION_RECEPT_ADDRESS:<EMAIL>}
  app.url: ${MAIL_APP_URL:http://uh008195/e-board/}
  smtp:
    host: ${MAIL_SMTP_HOST:localhost}
    port: ${MAIL_SMTP_HOST:1025}

batch:
  s3:
    vacancy-parking-batch-bucket: ${VACANCY_PARKING_BATCH_BUCKET:dummy}
    parking-summary-batch-bucket: ${PARKING_SUMMARY_BATCH_BUCKET:dummy}
    exclusive-property-batch-bucket: ${EXCLUSIVE_PROPERTY_BATCH_BUCKET:dummy}
    exclusive-property-for-fc-nyuko-batch-backup-bucket: ${EXCLUSIVE_PROPERTY_FOR_FC_NYUKO_BATCH_BACKUP_BUCKET:dummy}
    exclusive-property-for-fc-nyuko-batch-bucket: ${EXCLUSIVE_PROPERTY_FOR_FC_NYUKO_BATCH_BUCKET:dummy}
  #DK-PORTAL向け
  sftp:
    host: ${SFTP_HOST:*************}
    secret-id: batch_sftp
    send-directory: ${DK_PORTAL_SFTP_DIRECTORY:/opt/ftpuser/staging}
  ftp:
    #WelcomePark向け
    welcome-park:
      #NWアカウントから提供されるprivateLinkのVPCエンドポイント
      #https://simplex.ent.box.com/file/1773124568630?s=******************************** (詳細はインフラ構成図を参照)
      host: ${FTP_HOST:vpce-050ddef83f4dd95d5-fqpxojq7.vpce-svc-02fa4f5a8b8dad605.ap-northeast-1.vpce.amazonaws.com}
      secret-id: batch_ftp
      send-directory: /share/app/convert/import
    #E-Cloud向け
    e-cloud:
      host: ${FTP_HOST:**************}
      secret-id: batch_ftp_e_cloud

external:
  use-proxy: ${EXTERNAL_USE_PROXY:true}
  eboard:
    secret-id: eboard_api_secret
    endpoint: ${EBOARD_ENDPOINT:https://dev-api.kentaku.co.jp/le}
  dkportal:
    endpoint: ${DK_PORTAL_ENDPOINT:http://*************/daitokima/api}

aws:
  endpoint: dummy

proxy:
  host: ${PROXY_HOST:dummy}
  port: ${PROXY_PORT:0}
