package jp.ne.simplex.application.repository.external.eboard

import jp.ne.simplex.application.repository.external.ExternalApiRestClient
import jp.ne.simplex.application.repository.external.eboard.config.EboardApiPath
import jp.ne.simplex.application.repository.external.eboard.config.EboardRequest
import jp.ne.simplex.application.repository.external.eboard.config.EboardResponse
import jp.ne.simplex.application.repository.external.eboard.config.EboardRestConfig
import jp.ne.simplex.exception.ErrorType
import org.junit.jupiter.api.BeforeEach
import org.springframework.http.HttpStatusCode
import org.springframework.test.web.client.MockRestServiceServer
import org.springframework.test.web.client.ResponseCreator
import org.springframework.test.web.client.response.DefaultResponseCreator
import org.springframework.test.web.client.response.MockRestResponseCreators
import org.springframework.web.client.RestClient

abstract class EboardRestConfigTestBase {
    companion object {
        const val ENDPOINT = "http://example.com"
    }

    private val builder = RestClient.builder()

    lateinit var restClient: ExternalApiRestClient<EboardRequest, EboardResponse>

    lateinit var server: MockRestServiceServer

    @BeforeEach
    fun setup() {
        server = MockRestServiceServer.bindTo(builder).build()
        restClient = EboardRestConfig(ENDPOINT).restClient(builder)
    }
}

class TestRequest : EboardRequest {
    override fun getApiPath(): EboardApiPath {
        return EboardApiPath.UPDATE_KARIOSAE
    }

    override fun getErrorType(): ErrorType {
        return ErrorType.UNEXPECTED_ERROR
    }
}

class TestResponse : EboardResponse

class TestResponseCreator(statusCode: HttpStatusCode) : DefaultResponseCreator(statusCode) {
    companion object {
        fun withServerError(body: String): ResponseCreator {
            return MockRestResponseCreators.withServerError()
                .body(body)
        }
    }
}
