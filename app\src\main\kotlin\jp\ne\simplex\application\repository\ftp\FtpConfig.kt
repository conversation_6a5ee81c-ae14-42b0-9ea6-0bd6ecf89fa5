package jp.ne.simplex.application.repository.ftp

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Profile

@Profile("batch")
@ConfigurationProperties(prefix = "batch.ftp.welcome-park")
class FtpConfigForWelcomePark(
    val host: String,
    val secretId: String,
    val sendDirectory: String,
)

@Profile("batch")
@ConfigurationProperties(prefix = "batch.ftp.e-cloud")
class FtpConfigForECloud(
    val host: String,
    val secretId: String,
)
