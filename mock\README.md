# mock-server

Mocks Serverを利用して、外部サービスのAPIをモックし開発を進める
参考 : https://www.mocks-server.org/docs/overview/

## 起動手順

```bash
# $HOME/Projects/propetech/propetech-server/mock
# propetech-server リポジトリの mock ディレクトリ配下であること
pwd

node -v
# v23.3.0

npm install

npm run mock

────────────────────────────────────────────────────────────
ℹ️  CURRENT SETTINGS
────────────────────────────────────────────────────────────
‧ Server listening at: http://localhost:8083
‧ Delay: 0
‧ Current collection: base
‧ Collections: 2
‧ Routes: 3
‧ Route variants: 5
‧ Log level: info
‧ Watch enabled: true

────────────────────────────────────────────────────────────
↕️  ACTIONS
────────────────────────────────────────────────────────────
? Select action: (Use arrow keys)
❯ Select collection
  Use route variant
  Restore route variants
  Change delay
  Restart server
  Change log level
  Switch watch
(Move up and down to reveal more choices)
```

---

## CLI / Swagger UIの使い方

CLIの使用方法は、公式の[このページ](https://www.mocks-server.org/docs/integrations/command-line/)
に記載があります。

- CLI は、`npm run mock` で起動後に、コマンド実行したインターフェース上で使用できます
- Swagger UIは、`npm run mock` で起動後に、http://localhost:3110/docs/
  にアクセスすることで、SwaggerでMockの設定を更新できます

以下、使用頻度の高そうな、遅延設定とモード（コレクション）の変更をCLIとSwaggerで記載します。

### 遅延設定を更新したい場合

<details>
<summary>Swagger UI での変更方法</summary>

**`PATCH`** `/config` APIで、以下（2000 ms に設定する場合）をリクエストボディに設定して、`Execute` する

```json
{
  "mock": {
   "routes": {
      "delay": 2000
    }
  }
}
```

</details>

<details>
<summary>CLI での変更方法</summary>

以下のように、メインメニュー > **Change delay** から、遅延させたいミリ秒を入力することで設定できる
![alt text](docs/images/menu_delay.png)
![alt text](docs/images/choose_delay.png)
</details>

### モードを切り替えたい場合

<details>
<summary>Swagger UI での変更方法</summary>

**`PATCH`** `/config` APIで、以下（error モード に設定する場合）をリクエストボディに設定して、`Execute`
する
※ 以下 `selected` に指定可能な値は、`mocks/collections.ts` に記載の `id`

```json
{
  "mock": {
    "collections": {
      "selected": "error"
    }
  }
}
```

</details>

<details>
<summary>CLI での変更方法</summary>

以下のように、メインメニュー > **Select collection** で `base`/`error`を切り替えられる
![alt text](docs/images/menu_collection.png)
![alt text](docs/images/choose_collection.png)

</details>

---

## API追加手順

### 1. routes 配下にAPIに対応する jsファイルを作成する

```javascript
module.exports = [
  {
    id: "instruct_public",
    url: "/instruct_public",
    method: "POST",
    variants: [
      {
        id: "base", // ← CLIで、モードを切り替えることができるので、正常系/異常系を定義する
        delay: 0,
        type: "middleware",
        options: {
          middleware: (req, res) => {
            // 認証失敗した場合 or アクセストークンの有効期限切れの場合

            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const responseType = checkParameter(req);

            if (responseType) {
              res.status(400);
              res.send({
                result: responseType,
                message: responseCodeMapping[responseType],
              });

              return;
            }

            // status: 200 の場合
            res.status(200);
            res.send({
              result: 0,
              message: "",
              ...
            });
          },
        },
      },
      {
        id: "error",
        delay: 0,
        type: "middleware",
        options: {
          middleware: (req, res) => {
            // 障害発生時
            const responseType = ResponseType.OTHER_ERROR;

            res.status(500);
            res.send({
              result: responseType,
              message: responseMapping[responseType],
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する
 * responseCodeMapping（api_response_specification.js）定義のエラーコードを返却する
 * @param {*} req
 * @returns {ResponseType}
 */
function checkParameter(req) { ... }

```

### 2. collections.json に上記で追加したAPIの id を追記する

```json
[
  {
    "id": "base", // ← この部分を、CLIで切り替えることができる
    "routes": [
      "instruct_public:base", // ${id}:${variant.id}
      "update_kariosae:base"
    ]
  },
  {
    "id": "error",
    "routes": [
      "instruct_public:error",
      "update_kariosae:error"
    ]
  }
]
```
