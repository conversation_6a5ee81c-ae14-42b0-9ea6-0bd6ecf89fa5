package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingInfoMasterPojo
import org.slf4j.LoggerFactory

class BuildingInfoMasterEx {

    companion object {

        private val log = LoggerFactory.getLogger(BuildingInfoMasterEx::class.java)

        fun BuildingInfoMasterPojo.getBuilding(): Building? {
            return try {
                Building(
                    code = Building.Code.of(this.buildingCode),
                    name = Building.Name.of(this.propertyName ?: ""),
                    leasingStoreCode = leasingStoreCd?.let { Branch.Code.of(it) },
                    reviewBranchCode = reviewBranchCd?.let { Branch.Code.of(it) },
                    businessOfficeCode = marketingBranchOfficeCd?.let { Office.Code.of(it) },
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize BuildingInfoMaster record. $this")
                null
            }
        }
    }
}
