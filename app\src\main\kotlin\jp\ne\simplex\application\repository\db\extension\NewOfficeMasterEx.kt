package jp.ne.simplex.application.repository.db.extension

import jp.ne.simplex.application.model.Office
import jp.ne.simplex.db.jooq.gen.tables.pojos.NewOfficeMasterPojo
import org.slf4j.LoggerFactory

class NewOfficeMasterEx {

    companion object {

        private val log = LoggerFactory.getLogger(NewOfficeMasterEx::class.java)

        fun NewOfficeMasterPojo.getOffice(): Office? {
            return try {
                Office(
                    code = Office.Code.of(this.businessOfficeCode),
                    name = Office.Name.of(this.businessOfficeName),
                )
            } catch (_: Exception) {
                // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
                log.warn("Failed to deserialize NewOfficeMaster record. $this")
                null
            }
        }
    }
}
