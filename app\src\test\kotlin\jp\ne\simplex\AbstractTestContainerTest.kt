package jp.ne.simplex

import jp.ne.simplex.application.repository.db.pojos.*
import jp.ne.simplex.application.repository.db.pojos.ContractPojo
import jp.ne.simplex.application.repository.db.pojos.RoomInfoMasterPojo
import jp.ne.simplex.application.repository.db.pojos.TemporaryContractPojo
import jp.ne.simplex.application.repository.db.pojos.TenantContractPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.*
import jp.ne.simplex.db.jooq.gen.tables.pojos.BuildingMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.pojos.LatestRentEvaluationPojo
import jp.ne.simplex.db.jooq.gen.tables.records.*
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.mock.DatabaseTestContainer
import jp.ne.simplex.support.IntegrationTest
import org.jooq.DSLContext
import org.jooq.impl.TableImpl
import org.jooq.impl.TableRecordImpl
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jooq.JooqTest
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import java.io.Serializable

@JooqTest
@IntegrationTest
abstract class AbstractTestContainerTest {

    @Autowired
    lateinit var dslContext: DSLContext

    @BeforeEach
    fun setupEach() {
        beforeEach()
    }

    abstract fun beforeEach()

    @AfterEach
    fun tearDownEach() {
        targetTables().forEach { t: TableImpl<*>? ->
            dslContext.truncateTable(t).execute()
        }
        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }
    }

    abstract fun targetTables(): List<TableImpl<*>>

    fun DSLContext.saveTemporaryReservationFilePojo(vararg pojo: TemporaryReservationFilePojo) {
        return this.save(
            table = TEMPORARY_RESERVATION_FILE,
            recordConstructor = { p: TemporaryReservationFilePojo ->
                TemporaryReservationFileRecord(p)
            },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveParkingPojo(vararg pojo: ParkingPojo) {
        return this.save(
            table = PARKING,
            recordConstructor = { p: ParkingPojo -> ParkingRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveParkingReservationPojo(vararg pojo: ParkingReservationPojo) {
        return this.save(
            table = PARKING_RESERVATION,
            recordConstructor = { p: ParkingReservationPojo -> ParkingReservationRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveParkingContractPossibilityPojo(vararg pojo: ParkingContractPossibilityPojo) {
        return this.save(
            table = PARKING_CONTRACT_POSSIBILITY,
            recordConstructor = { p: ParkingContractPossibilityPojo ->
                ParkingContractPossibilityRecord(p)
            },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveBuildingMasterPojo(vararg pojo: BuildingMasterPojo) {
        return this.save(
            table = BUILDING_MASTER,
            recordConstructor = { p: BuildingMasterPojo -> BuildingMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveAddressMasterPojo(vararg pojo: AddressMasterPojo) {
        return this.save(
            table = ADDRESS_MASTER,
            recordConstructor = { p: AddressMasterPojo -> AddressMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveCustomerPojo(vararg pojo: CustomerPojo) {
        return this.save(
            table = CUSTOMER,
            recordConstructor = { p: CustomerPojo -> CustomerRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveBuildingInfoMasterPojo(vararg pojo: BuildingInfoMasterPojo) {
        return this.save(
            table = BUILDING_INFO_MASTER,
            recordConstructor = { p: BuildingInfoMasterPojo -> BuildingInfoMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveNewOfficeMasterPojo(vararg pojo: NewOfficeMasterPojo) {
        return this.save(
            table = NEW_OFFICE_MASTER,
            recordConstructor = { p: NewOfficeMasterPojo -> NewOfficeMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveParkingInfoMasterPojo(vararg pojo: ParkingInfoMasterPojo) {
        return this.save(
            table = PARKING_INFO_MASTER,
            recordConstructor = { p: ParkingInfoMasterPojo -> ParkingInfoMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveTenantContractPojo(vararg pojo: TenantContractPojo) {
        pojo.forEach {
            dslContext.insertInto(TENANT_CONTRACT)
                .set(TENANT_CONTRACT.TENANT_CONTRACT_CHANGE_SEQ, it.tenantContractChangeSeq)
                .set(TENANT_CONTRACT.TENANT_CONTRACT_NUMBER, it.tenantContractNumber)
                .set(TENANT_CONTRACT.BUILDING_CODE, it.buildingCode)
                .set(TENANT_CONTRACT.PARKING_CODE, it.parkingCode)
                .set(TENANT_CONTRACT.ROOM_CODE, it.roomCode)
                .set(TENANT_CONTRACT.TENANT_NAME, it.tenantName)
                .set(TENANT_CONTRACT.TENANT_CODE, it.tenantCode)
                .set(TENANT_CONTRACT.CURRENT_STATE_DIVISION, it.currentStateDivision)
                .set(TENANT_CONTRACT.MODIFICATION_STATE_DIVISION, it.modificationStateDivision)
                .set(TENANT_CONTRACT.CANCELLATION_SIGN, it.cancellationSign?.toByte())
                .set(
                    TENANT_CONTRACT.MOVE_IN_START_PROCESSED_SIGN,
                    it.moveInStartProcessedSign?.toByte()
                )
                .set(TENANT_CONTRACT.MOVE_OUT_DATE, it.moveOutDate)
                .set(TENANT_CONTRACT.MOVE_IN_SCHEDULED_DATE, it.moveInScheduledDate)
                .set(TENANT_CONTRACT.CONTRACT_EFFECTIVE_START_DATE, it.contractEffectiveStartDate)
                .set(TENANT_CONTRACT.CONTRACT_EFFECTIVE_END_DATE, it.contractEffectiveEndDate)
                .set(TENANT_CONTRACT.CONTRACT_EXPIRY_DATE, it.contractEffectiveEndDate)
                .set(TENANT_CONTRACT.VACATE_SCHEDULED_DATE, it.vacateScheduledDate)
                .set(TENANT_CONTRACT.VACATE_NOTICE_DATE, it.vacateNoticeDate)
                .set(TENANT_CONTRACT.AGGREGATE_CONTRACT_NUMBER, it.aggregateContractNumber)
                .set(TENANT_CONTRACT.LOGICAL_DELETE_SIGN, it.logicalDeleteSign?.toByte())
                .execute()
        }

        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }
    }

    fun DSLContext.saveTenantPojo(vararg pojo: TenantPojo) {
        return this.save(
            table = TENANT,
            recordConstructor = { p: TenantPojo -> TenantRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveParkingEnablePojo(vararg pojo: ParkingEnablePojo) {
        return this.save(
            table = PARKING_ENABLE,
            recordConstructor = { p: ParkingEnablePojo -> ParkingEnableRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveLatestRentEvaluationPojo(vararg pojo: LatestRentEvaluationPojo) {
        return this.save(
            table = LATEST_RENT_EVALUATION,
            recordConstructor = { p: LatestRentEvaluationPojo -> LatestRentEvaluationRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveParkingHourlyRentalApprovalPojo(vararg pojo: ParkingHourlyRentalApprovalPojo) {
        return this.save(
            table = PARKING_HOURLY_RENTAL_APPROVAL,
            recordConstructor = { p: ParkingHourlyRentalApprovalPojo ->
                ParkingHourlyRentalApprovalRecord(
                    p
                )
            },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveBulkLeaseParkingPojo(vararg pojo: BulkLeaseParkingPojo) {
        return this.save(
            table = BULK_LEASE_PARKING,
            recordConstructor = { p: BulkLeaseParkingPojo -> BulkLeaseParkingRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveOffSiteParkingPojo(vararg pojo: OffSiteParkingPojo) {
        return this.save(
            table = OFF_SITE_PARKING,
            recordConstructor = { p: OffSiteParkingPojo -> OffSiteParkingRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveRegionMasterPojo(vararg pojo: RegionMasterPojo) {
        return this.save(
            table = REGION_MASTER,
            recordConstructor = { p: RegionMasterPojo -> RegionMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveAffiliationMasterPojo(vararg pojo: AffiliationMasterPojo) {
        return this.save(
            table = AFFILIATION_MASTER,
            recordConstructor = { p: AffiliationMasterPojo -> AffiliationMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveBranchFilePojo(vararg pojo: BranchFilePojo) {
        return this.save(
            table = BRANCH_FILE,
            recordConstructor = { p: BranchFilePojo -> BranchFileRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveKtAllBranchPojo(vararg pojo: KtAllBranchPojo) {
        return this.save(
            table = KT_ALL_BRANCH,
            recordConstructor = { p: KtAllBranchPojo -> KtAllBranchRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveHrCategoryTableBPojo(vararg pojo: HrCategoryTableBPojo) {
        return this.save(
            table = HR_CATEGORY_TABLE_B,
            recordConstructor = { p: HrCategoryTableBPojo -> HrCategoryTableBRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveEmployeeMasterPojo(vararg pojo: EmployeeMasterPojo) {
        return this.save(
            table = EMPLOYEE_MASTER,
            recordConstructor = { p: EmployeeMasterPojo -> EmployeeMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.savePasswordMasterPojo(vararg pojo: PasswordMasterPojo) {
        return this.save(
            table = PASSWORD_MASTER,
            recordConstructor = { p: PasswordMasterPojo -> PasswordMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveExclusivePropertyPojo(vararg pojo: ExclusivePropertyPojo) {
        return this.save(
            table = EXCLUSIVE_PROPERTY,
            recordConstructor = { p: ExclusivePropertyPojo -> ExclusivePropertyRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveExclusivePropertyECodePojo(vararg pojo: ExclusivePropertyECodePojo) {
        return this.save(
            table = EXCLUSIVE_PROPERTY_E_CODE,
            recordConstructor = { p: ExclusivePropertyECodePojo -> ExclusivePropertyECodeRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveAgentPojo(vararg pojo: AgentPojo) {
        return this.save(
            table = AGENT,
            recordConstructor = { p: AgentPojo -> AgentRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveRoomInfoMasterPojo(vararg pojo: RoomInfoMasterPojo) {
        pojo.forEach {
            dslContext.insertInto(ROOM_INFO_MASTER)
                .set(ROOM_INFO_MASTER.RECORD_TYPE, it.recordType)
                .set(ROOM_INFO_MASTER.PROPERTY_CD_TYPE, it.propertyCdType)
                .set(ROOM_INFO_MASTER.PROPERTY_BUILDING_CD, it.propertyBuildingCd)
                .set(ROOM_INFO_MASTER.PROPERTY_ROOM_CD, it.propertyRoomCd)
                .set(ROOM_INFO_MASTER.RECORD_STATUS_TYPE, it.recordStatusType)
                .set(ROOM_INFO_MASTER.ROOM_NUMBER, it.roomNumber)
                .set(ROOM_INFO_MASTER.BUILDING_NAME, it.buildingName)
                .set(ROOM_INFO_MASTER.DIRECTION, it.direction)
                .set(ROOM_INFO_MASTER.MARKETING_BRANCH_OFFICE_CD, it.marketingBranchOfficeCd)
                .execute()
        }

        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }

    }

    fun DSLContext.saveVacantHousePojo(vararg pojo: VacantHousePojo) {
        pojo.forEach {
            dslContext.insertInto(VACANT_HOUSE_HP)
                .set(VACANT_HOUSE_HP.PROPERTY_BUILDING_CD, it.propertyBuildingCd)
                .set(VACANT_HOUSE_HP.PROPERTY_ROOM_CD, it.propertyRoomCd)
                .set(VACANT_HOUSE_HP.CHANGE_DIVISION, it.changeDivision)
                .set(VACANT_HOUSE_HP.CUSTOMER_COMPLETION_FLAG, it.customerCompletionFlag)
                .execute()
        }

        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }

    }

    fun DSLContext.savePropertyMaintenanceInfoPojo(vararg pojo: PropertyMaintenanceInfoPojo) {
        pojo.forEach {
            dslContext.insertInto(PROPERTY_MAINTENANCE_INFO)
                .set(PROPERTY_MAINTENANCE_INFO.BUILDING_CD, it.buildingCd)
                .set(PROPERTY_MAINTENANCE_INFO.ROOM_CD, it.roomCd)
                .set(PROPERTY_MAINTENANCE_INFO.AD_AMOUNT, it.adAmount)
                .set(PROPERTY_MAINTENANCE_INFO.FF_AMOUNT, it.ffAmount)
                .set(
                    PROPERTY_MAINTENANCE_INFO.LISTING_CATEGORY_GOOD_ROOM_NET,
                    it.listingCategoryGoodRoomNet
                )
                .execute()
        }

        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }

    }

    fun DSLContext.saveRoomMasterPojo(vararg pojo: RoomMasterPojo) {
        return this.save(
            table = ROOM_MASTER,
            recordConstructor = { p: RoomMasterPojo -> RoomMasterRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveParkingVehicleInfoFilePojo(vararg pojo: ParkingVehicleInfoFilePojo) {
        return this.save(
            table = PARKING_VEHICLE_INFO_FILE,
            recordConstructor = { p: ParkingVehicleInfoFilePojo -> ParkingVehicleInfoFileRecord(p) },
            pojos = pojo.toList()
        )
    }

    fun DSLContext.saveContractPojo(vararg pojo: ContractPojo) {
        pojo.forEach {
            dslContext.insertInto(CONTRACT)
                .set(CONTRACT.BUILDING_CD, it.buildingCd)
                .set(CONTRACT.EFFECTIVE_START_DATE, it.effectiveStartDate)
                .set(CONTRACT.EFFECTIVE_END_DATE, it.effectiveEndDate)
                .set(CONTRACT.INITIAL_SETUP_SIGN, it.initialSetupSign)
                .set(CONTRACT.CONTRACT_TYPE, it.contractType)
                .set(CONTRACT.LOGICAL_DELETE_SIGN, it.logicalDeleteSign)
                .execute()
        }

        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }
    }

    fun DSLContext.saveTemporaryContractPojo(vararg pojo: TemporaryContractPojo) {
        pojo.forEach {
            dslContext.insertInto(TEMPORARY_CONTRACT)
                .set(TEMPORARY_CONTRACT.BUILDING_CD, it.buildingCd)
                .set(TEMPORARY_CONTRACT.EFFECTIVE_START_DATE, it.effectiveStartDate)
                .set(TEMPORARY_CONTRACT.EFFECTIVE_END_DATE, it.effectiveEndDate)
                .set(TEMPORARY_CONTRACT.INITIAL_SETUP_FLAG, it.initialSetupFlag)
                .set(TEMPORARY_CONTRACT.CONTRACT_TYPE, it.contractType)
                .set(TEMPORARY_CONTRACT.LOGICAL_DELETE_FLAG, it.logicalDeleteFlag)
                .execute()
        }

        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }
    }

    fun <P : Serializable, R : TableRecordImpl<R>> DSLContext.save(
        table: TableImpl<R>,
        recordConstructor: (P) -> R,
        pojos: List<P>
    ) {
        pojos.map { pojo ->
            insertInto(table)
                .set(recordConstructor(pojo))
                .execute()
        }
        dslContext.connection { connection ->
            connection.commit() // 明示的にコミット
        }
    }

    companion object {

        private val container = DatabaseTestContainer.getInstance()

        @DynamicPropertySource
        @JvmStatic
        fun setupDatasource(registry: DynamicPropertyRegistry) {
            container.overrideDatasource(registry)
        }

        @BeforeAll
        @JvmStatic
        fun setupAll() {
            container.cleanUpSchema()
        }
    }
}
