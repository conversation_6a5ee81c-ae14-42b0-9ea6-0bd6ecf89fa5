package jp.ne.simplex.application.repository.db.pojos


// 引数超過対策

data class LatestRentEvaluationPojo(
    val buildingCode: String,
    val propertyCode: String,
    val roomParkingDivision: String = "2",
    override val keyMoneyAmount: Int? = null,
    override val depositAmount: Int? = null,
    override val parkingFee: Int? = null,
    override val brokerApplicationCollectionDivision: String? = null,
    val taxDivision: String? = null,
    val keyMoneyInoutDivision: String? = null,
    val parkingFeeInoutDivision: String? = null,
    override val standardRentForCoop: Int? = null,
) : LatestRentEvaluationPojoInterface

interface LatestRentEvaluationPojoInterface {
    val keyMoneyAmount: Int?
    val depositAmount: Int?
    val parkingFee: Int?
    val brokerApplicationCollectionDivision: String?
    val standardRentForCoop: Int?
}

interface ParkingLatestRentEvaluationInterface : LatestRentEvaluationPojoInterface {
    val parkingFeeInTax: String?
    val keyMoneyInTax: String?
}
