# propetech-server : app

## デプロイ

### 仕組み

![deploy-flow.jpg](docs/images/deploy/architecture.jpg)

### 手順

<!-- @formatter:off -->
1. [こちら](https://github.com/sxi-propetech/propetech-server/actions/workflows/deploy-app-image.yml)
   から、以下のように、デプロイするブランチ/環境/タグを入力して、「Run
   workflow」を押下する
    1. **※ Dockerイメージのタグは一意なものにすること。すでにデプロイしたタグと重複している場合、デプロイは失敗します。※**
    ![](docs/images/deploy/workflow1.png)

<br>

2. ステップ1のワークフローが成功したら、リリースする環境の `Deploy CDK` ワークフローを実行する
   1. IT 環境の場合： https://github.com/sxi-propetech/propetech/actions/workflows/deployCdkIt.yaml
   2. STG 環境の場合：https://github.com/sxi-propetech/propetech/actions/workflows/deployCdkStg.yaml
   3. PROD 環境の場合：https://github.com/sxi-propetech/propetech/actions/workflows/deployCdkPrd.yaml
    ![](docs/images/deploy/workflow2.png)

## ドキュメント

- [ログ出力](./docs/ログ出力.md)
- [外部APIの認証](./docs/外部APIの認証.md)