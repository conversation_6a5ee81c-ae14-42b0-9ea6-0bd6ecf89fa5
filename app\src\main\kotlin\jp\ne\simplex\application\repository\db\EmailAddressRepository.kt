package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.EmailAddress
import jp.ne.simplex.db.jooq.gen.tables.references.DAITO_BUILDING_MANAGEMENT_TABLE
import jp.ne.simplex.db.jooq.gen.tables.references.EMAIL_ADDRESS_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.EMPLOYEE_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.MAIL_FORWARD_MASTER
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Repository

@Repository
class EmailAddressRepository(
    private val context: DSLContext,
) : EmailAddressRepositoryInterface {

    override fun getTenantAddressList(branchCode: Branch.Code): List<EmailAddress> {
        return context
            .select(EMAIL_ADDRESS_MASTER.ADDRESS)
            .from(EMPLOYEE_MASTER) // XXEMPP
            .leftJoin(EMAIL_ADDRESS_MASTER) // XXMADP
            .on(EMAIL_ADDRESS_MASTER.EMPLOYEE_NUMBER.eq(EMPLOYEE_MASTER.EMPLOYEE_NUMBER)) //XXE10K = XMA01A
            .leftJoin(DAITO_BUILDING_MANAGEMENT_TABLE) // FXX20P
            .on(
                DSL.substring(EMPLOYEE_MASTER.AFFILIATION_CODE, 1, 3)
                    .eq(DAITO_BUILDING_MANAGEMENT_TABLE.CONSTRUCTION_TERMINAL_INSTALLATION)
            ) // SUBSTR(XXE32C,1,3) = FXX23C
            .where(
                EMPLOYEE_MASTER.JOB_TYPE_CODE.`in`(
                    listOf("050", "052", "053", "054", "174", "056", "170", "175", "172", "173")
                )
            ) // XXE33C
            .and(DAITO_BUILDING_MANAGEMENT_TABLE.CONSTRUCTION_TERMINAL_INSTALLATION.like("${branchCode.getValue()}%")) // FXX23C
            .and(EMPLOYEE_MASTER.RESIGNATION_DATE.eq(0)) // XXE16D
            .fetch(EMAIL_ADDRESS_MASTER.ADDRESS)
            .mapNotNull { it?.let { EmailAddress.of(it) } }
    }

    override fun getDaitateAddressList(branchCode: Branch.Code): List<EmailAddress> {
        return context
            .select(EMAIL_ADDRESS_MASTER.ADDRESS)
            .from(EMPLOYEE_MASTER) // XXEMPP
            .leftJoin(EMAIL_ADDRESS_MASTER) // XXMADP
            .on(EMAIL_ADDRESS_MASTER.EMPLOYEE_NUMBER.eq(EMPLOYEE_MASTER.EMPLOYEE_NUMBER)) // XXE10K = XMA01A
            .leftJoin(DAITO_BUILDING_MANAGEMENT_TABLE) // FXX20P
            .on(
                DSL.substring(EMPLOYEE_MASTER.AFFILIATION_CODE, 1, 3)
                    .eq(DAITO_BUILDING_MANAGEMENT_TABLE.DAIKEN_TERMINAL_INSTALLATION)
            ) // SUBSTR(XXE32C,1,3) = FXX24C
            .where(EMPLOYEE_MASTER.JOB_TYPE_CODE.`in`(listOf("950"))) // XXE33C
            .and(DAITO_BUILDING_MANAGEMENT_TABLE.CONSTRUCTION_TERMINAL_INSTALLATION.like("${branchCode.getValue()}%")) // FXX23C
            .and(EMPLOYEE_MASTER.RESIGNATION_DATE.eq(0)) // XXE16D
            .fetch(EMAIL_ADDRESS_MASTER.ADDRESS)
            .mapNotNull { it?.let { EmailAddress.of(it) } }
    }

    override fun getAddressListByBranchCode(branchCodes: List<Branch.Code>): List<EmailAddress> {
        return context
            .selectDistinct(MAIL_FORWARD_MASTER.ADDRESS)
            .from(MAIL_FORWARD_MASTER) // ERAMSP
            .where(
                DSL.left(MAIL_FORWARD_MASTER.BRANCH_CD, 3)
                    .`in`(branchCodes.map { it.getValue() })
            ) // LEFT(ERA04C, 3) in (?,?)
            .and(MAIL_FORWARD_MASTER.ADDRESS.isNotNull) // ERA06C
            .orderBy(MAIL_FORWARD_MASTER.ADDRESS) // ERA06C
            .fetch(EMAIL_ADDRESS_MASTER.ADDRESS)
            .mapNotNull { it?.let { EmailAddress.of(it) } }
    }
}

interface EmailAddressRepositoryInterface {
    /** テナント営業のアドレス一覧を取得する */
    fun getTenantAddressList(branchCode: Branch.Code): List<EmailAddress>

    /** 大東建物管理のアドレス一覧を取得する */
    fun getDaitateAddressList(branchCode: Branch.Code): List<EmailAddress>

    /** 送信アドレス一覧を取得する */
    fun getAddressListByBranchCode(branchCodes: List<Branch.Code>): List<EmailAddress>
}
