package jp.ne.simplex.shared

import kotlinx.coroutines.*
import java.util.concurrent.Executors

class MultiThreadRunBlocking {

    companion object {
        fun <E : Any> runAsyncTasks(
            maxThreads: Int,
            tasks: List<E>,
            execute: suspend CoroutineScope.(E) -> Unit,
        ) {
            val dispatcher = Executors.newFixedThreadPool(maxThreads).asCoroutineDispatcher()

            runBlocking {
                coroutineScope {
                    tasks.forEach { task ->
                        launch(dispatcher) {
                            execute(task)
                        }
                    }
                }
            }

            dispatcher.close()
        }
    }
}
