package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.UpdateParkingLotAvailability
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.references.PARKING_ENABLE
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.jooq.Field
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class ParkingEnableRepository(private val context: DSLContext) : ParkingEnableRepositoryInterface {

    override fun isAvailable(parkingLotId: ParkingLot.Id): Boolean {
        // 利用不可レコードが明示的に存在する場合以外は利用可能
        return context.selectFrom(PARKING_ENABLE)
            .where(PARKING_ENABLE.BUILDING_CODE.eq(parkingLotId.buildingCode.value))
            .and(PARKING_ENABLE.PARKING_LOT_CODE.eq(parkingLotId.parkingLotCode.value))
            .and(PARKING_ENABLE.PARKING_LOT_ENABLE.eq(ParkingLot.Status.DISABLE.value))
            .count() == 0

    }

    override fun upsert(requestUser: AuthInfo.RequestUser, param: UpdateParkingLotAvailability) {
        val currentDateTime = LocalDateTime.now()

        val insertTargets = mapOf<Field<*>, Any?>(
            PARKING_ENABLE.BUILDING_CODE to param.parkingLotId.buildingCode.value,
            PARKING_ENABLE.PARKING_LOT_CODE to param.parkingLotId.parkingLotCode.value,
            PARKING_ENABLE.PARKING_LOT_ENABLE to param.status.value,
            PARKING_ENABLE.CREATION_DATE to currentDateTime.yyyyMMdd().toInt(),
            PARKING_ENABLE.CREATION_TIME to currentDateTime.HHmmss().toInt(),
            PARKING_ENABLE.CREATOR to requestUser.value,
            PARKING_ENABLE.DELETE_FLAG to false.toInt().toString()
        )

        val updateTargets = mapOf<Field<*>, Any?>(
            PARKING_ENABLE.PARKING_LOT_ENABLE to param.status.value,
            PARKING_ENABLE.UPDATE_DATE to currentDateTime.yyyyMMdd().toInt(),
            PARKING_ENABLE.UPDATE_TIME to currentDateTime.HHmmss().toInt(),
            PARKING_ENABLE.UPDATER to requestUser.value,
        )

        context.insertInto(PARKING_ENABLE)
            .set(insertTargets)
            .onDuplicateKeyUpdate()
            .set(updateTargets)
            .execute()
    }
}

interface ParkingEnableRepositoryInterface {
    fun isAvailable(parkingLotId: ParkingLot.Id): Boolean
    fun upsert(requestUser: AuthInfo.RequestUser, param: UpdateParkingLotAvailability)
}
