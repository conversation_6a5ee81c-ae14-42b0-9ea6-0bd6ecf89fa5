package jp.ne.simplex.application.controller.client.employee.dto

import jp.ne.simplex.application.controller.client.shared.ClientEmployeeDto
import jp.ne.simplex.application.model.Employee

data class ClientEmployeesOfBranchResponse private constructor(
    val list: List<ClientEmployeeDto>,
) {
    companion object {
        fun of(employees: List<Employee>): ClientEmployeesOfBranchResponse {
            return ClientEmployeesOfBranchResponse(
                list = employees.map { ClientEmployeeDto.of(it) }
            )
        }
    }
}
