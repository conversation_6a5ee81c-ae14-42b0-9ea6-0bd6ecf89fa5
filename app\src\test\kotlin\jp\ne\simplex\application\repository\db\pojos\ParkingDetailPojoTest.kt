package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo.Companion.LocalDate
import jp.ne.simplex.application.repository.db.pojos.ParkingDetailPojo.Companion.toParking
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.stubApiKeyAuthInfo
import jp.ne.simplex.stub.stubParkingReservationInfo
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class ParkingDetailPojoTest {
    @Nested
    @DisplayName("ParkingDetailPojoからの変換検証")
    inner class Scenario1 {
        @Test
        @DisplayName("査定区分取得の全パターン確認")
        fun testConvertToAssessmentDivision() {
            var result = ParkingDetailPojo.convertToAssessmentDivision("1", 4)
            assertEquals(ParkingLot.AssessmentDivision.ASSESSMENT, result)

            result = ParkingDetailPojo.convertToAssessmentDivision("1", 7)
            assertEquals(ParkingLot.AssessmentDivision.ASSESSMENT, result)

            result = ParkingDetailPojo.convertToAssessmentDivision("1", 1)
            assertNull(result)

            result = ParkingDetailPojo.convertToAssessmentDivision("2", 4)
            assertEquals(ParkingLot.AssessmentDivision.OUTSIDE_ASSESSMENT, result)

            result = ParkingDetailPojo.convertToAssessmentDivision("2", 7)
            assertEquals(ParkingLot.AssessmentDivision.OUTSIDE_ASSESSMENT, result)

            result = ParkingDetailPojo.convertToAssessmentDivision("2", 1)
            assertNull(result)

            result = ParkingDetailPojo.convertToAssessmentDivision("3", 4)
            assertNull(result)

            result = ParkingDetailPojo.convertToAssessmentDivision("3", 7)
            assertNull(result)

            result = ParkingDetailPojo.convertToAssessmentDivision("3", 1)
            assertNull(result)
        }

        @Test
        @DisplayName("駐車場空き区画ステータス取得の全パターン確認")
        fun testGetVacancyParkingStatus() {
            var result =
                ParkingDetailPojo.toEboardParkingStatusDivision(null, null, "1", null, null)
            assertEquals(ParkingLot.StatusDivision.CANCEL, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("20", null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.APPLIED, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("29", null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.APPLIED, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("25", null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.CONFIRMED, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("80", null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.CONFIRMED, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("30", null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.COLLECTING, result)

            result = ParkingDetailPojo.toEboardParkingStatusDivision("35", null, null, "0", null)
            assertEquals(ParkingLot.StatusDivision.COLLECTING, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("35", "35", null, null, null)
            assertEquals(ParkingLot.StatusDivision.OCCUPIED, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("35", "36", null, null, null)
            assertEquals(ParkingLot.StatusDivision.OCCUPIED, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("35", "37", null, null, null)
            assertEquals(ParkingLot.StatusDivision.OCCUPIED, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("35", "40", null, null, null)
            assertEquals(ParkingLot.StatusDivision.PLANNED_MOVE_OUT, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("45", null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.PLANNED_MOVE_OUT, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision("47", null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.PLANNED_MOVE_OUT, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision(
                    "90",
                    null,
                    null,
                    null,
                    "20250303".LocalDate()
                )
            assertEquals(ParkingLot.StatusDivision.PLANNED_MOVE_OUT, result)

            result =
                ParkingDetailPojo.toEboardParkingStatusDivision(null, null, null, null, null)
            assertEquals(ParkingLot.StatusDivision.VACANT, result)
        }

        @Test
        @DisplayName("ParkingDetailPojoとParkingReservationのリストからParkingLotへ変換確認(退去日なし)")
        fun testToParkingLot() {
            var source = ParkingDetailPojo(
                buildingCode = "*********",
                buildingName = "テスト建物名1",
                postalCode = "1234567",
                prefectureKanjiName = "テスト建物1東京都",
                cityKanjiName = "テスト建物1世田谷区",
                townKanjiName = "テスト建物1三軒茶屋",
                addressDetail = "テスト建物12-1-1",
                businessOfficeCode = "101",
                parkingLotCode = "701",
                parkingLotNumber = "1",
                parkingCategory = "1",
                currentStateDivision = "20",
                modificationStateDivision = "10",
                propertyTenant = PropertyTenantContractPojo(
                    tenantBuildingCode = "*********",
                    tenantRoomCode = "02030",
                    tenantRoomNumber = "203",
                    tenantNameKanji = "テスト部屋契約者名",
                    tenantName = "テスト部屋入居者名",
                ),
                cancellationSign = 0,
                moveInStartProcessedSign = 1,
                parkingLotEnable = "1",
                parkingFee = 25000,
                parkingFeeInTax = "1",
                assessmentDivision = "1",
                bulkLeaseFlag = 4,
                moveOutDate = 0,
                vacateScheduledDate = 20250101,
                moveInScheduledDate = 20250102,
                specialContractFlag = 0,
                brokerApplicationPossibility = "1",
                offSiteParkingCategory = "0",
                distance = 300,
                tenantContractNumber = "*********02030",
                tenantName = "テスト入居者名",
                tenantNameKanji = "テスト駐車場契約者名",
                landTransportName = "テスト陸事名",
                type = "テスト車両種別",
                businessCategory = "テスト業態",
                leftNumber = "テスト左ナンバー",
                rightNumber = "テスト右ナンバー",
                manufacturerDivision = "テストメーカー区分",
                carModelName = "テスト車両名",
                parkingCertIssueSign = "1",
                parkingCertComment = "テスト車庫証明コメント",
            )
            source = source.computeParkingStatusDivision()

            val result = source.toParkingLot(emptyMap(), emptyList())
            assertEquals("*********", result.id.buildingCode.value)
            assertEquals("701", result.id.parkingLotCode.value)
            assertEquals("1", result.localDisplayNumber)
            assertEquals(ParkingLot.Category.SINGLE, result.parkingLotCategory)
            assertEquals(ParkingLot.StatusDivision.APPLIED, result.parkingStatusDivision)
            assertEquals(ParkingLot.VacancyStatus.IMPOSSIBLE, result.vacancyParkingStatus)
            assertEquals(true, result.isAvailable)
            assertEquals(25000, result.parkingFee)
            assertEquals(1, result.parkingFeeInTax)
            assertEquals(
                ParkingLot.AssessmentDivision.ASSESSMENT,
                result.assessmentDivision
            ) //状態はそのまま
            assertEquals(ParkingLot.SpecialContractFlag.NO, result.specialContractFlag)
            assertEquals("4", result.contractForm)
            assertEquals("20250102", result.moveInScheduledDate?.yyyyMMdd())
            assertNull(result.expectedMoveOutDate?.yyyyMMdd()) //退去予定日はなし
            assertEquals(
                ParkingLot.BrokerApplicationPossibility.POSSIBLE,
                result.brokerApplicationPossibility
            )
            assertEquals(ParkingLot.OffSiteCategory.INSIDE, result.offSiteParkingLotCategory)
            assertEquals(300, result.offSiteParkingDistance)
            assertEquals("*********", result.linkedPropertyId!!.buildingCode.value)
            assertEquals("02030", result.linkedPropertyId!!.roomCode.value)
            assertEquals("203", result.linkedRoomNumber!!.getValue())
            assertEquals(emptyList(), result.reservationList)
            assertEquals("*********02030", result.tenant?.tenantContractNumber)
            assertEquals("*********02030", result.tenant?.originalContractNumber)
            assertEquals("テスト部屋入居者名", result.tenant?.propertyTenantName)
            assertEquals("テスト部屋契約者名", result.tenant?.propertyTenantContractName)
            assertEquals("テスト駐車場契約者名", result.tenant?.tenantContractName)
            assertEquals("テスト陸事名", result.tenant?.landTransportName)
            assertEquals("テスト車両種別", result.tenant?.type)
            assertEquals("テスト業態", result.tenant?.businessCategory)
            assertEquals("テスト左ナンバー", result.tenant?.leftNumber)
            assertEquals("テスト右ナンバー", result.tenant?.rightNumber)
            assertEquals("テストメーカー区分", result.tenant?.manufacturerDivision)
            assertEquals("テスト車両名", result.tenant?.carModelName)
            assertEquals("1", result.tenant?.parkingCertIssueSign)
            assertEquals("テスト車庫証明コメント", result.tenant?.parkingCertComment)
        }

        @Test
        @DisplayName("ParkingDetailPojoとParkingReservationのリストからParkingLotへ変換確認(退去日あり)")
        fun testToParkingLotWithMoveOutDate() {
            var source = ParkingDetailPojo(
                buildingCode = "*********",
                buildingName = "テスト建物名1",
                postalCode = "1234567",
                prefectureKanjiName = "テスト建物1東京都",
                cityKanjiName = "テスト建物1世田谷区",
                townKanjiName = "テスト建物1三軒茶屋",
                addressDetail = "テスト建物12-1-1",
                businessOfficeCode = "101",
                parkingLotCode = "701",
                parkingLotNumber = "1",
                parkingCategory = "1",
                currentStateDivision = "20",
                modificationStateDivision = "10",
                propertyTenant = PropertyTenantContractPojo(
                    tenantBuildingCode = "*********",
                    tenantRoomCode = "02030",
                    tenantRoomNumber = "203",
                    tenantNameKanji = "テスト部屋契約者名",
                    tenantName = "テスト部屋入居者名",
                ),
                cancellationSign = 0,
                moveInStartProcessedSign = 1,
                parkingLotEnable = "1",
                parkingFee = 25000,
                parkingFeeInTax = "1",
                assessmentDivision = "1",
                bulkLeaseFlag = 4,
                moveOutDate = 20250101,
                vacateScheduledDate = 20250101,
                moveInScheduledDate = 20250102,
                specialContractFlag = 0,
                brokerApplicationPossibility = "1",
                offSiteParkingCategory = "0",
                distance = 300,
                tenantContractNumber = "*********02030",
            )
            source = source.computeParkingStatusDivision()

            val result = source.toParkingLot(emptyMap(), emptyList())
            assertEquals("*********", result.id.buildingCode.value)
            assertEquals("701", result.id.parkingLotCode.value)
            assertEquals("1", result.localDisplayNumber)
            assertEquals(ParkingLot.Category.SINGLE, result.parkingLotCategory)
            assertEquals(ParkingLot.StatusDivision.VACANT, result.parkingStatusDivision) //空きとなる
            assertEquals(ParkingLot.VacancyStatus.POSSIBLE, result.vacancyParkingStatus)
            assertEquals(true, result.isAvailable)
            assertEquals(25000, result.parkingFee)
            assertEquals(1, result.parkingFeeInTax)
            assertEquals(ParkingLot.AssessmentDivision.ASSESSMENT, result.assessmentDivision)
            assertEquals(ParkingLot.SpecialContractFlag.NO, result.specialContractFlag)
            assertEquals("4", result.contractForm)
            assertNull(result.moveInScheduledDate?.yyyyMMdd()) // 空きの場合は空になる
            assertNull(result.expectedMoveOutDate?.yyyyMMdd())//退去予定日はなし
            assertEquals(
                ParkingLot.BrokerApplicationPossibility.POSSIBLE,
                result.brokerApplicationPossibility
            )
            assertEquals(ParkingLot.OffSiteCategory.INSIDE, result.offSiteParkingLotCategory)
            assertEquals(300, result.offSiteParkingDistance)
            assertNull(result.linkedPropertyId)// 空きの場合は空になる
            assertNull(result.linkedRoomNumber)// 空きの場合は空になる
            assertEquals(emptyList(), result.reservationList)
            assertNull(result.tenant) // 空きの場合は空になる
        }

        @Test
        @DisplayName("ParkingDetailPojoとParkingReservationのリストからParkingLotへ変換確認(退去予定)")
        fun testToParkingLotWithBeFlg3() {
            var source = ParkingDetailPojo(
                buildingCode = "*********",
                buildingName = "テスト建物名1",
                postalCode = "1234567",
                prefectureKanjiName = "テスト建物1東京都",
                cityKanjiName = "テスト建物1世田谷区",
                townKanjiName = "テスト建物1三軒茶屋",
                addressDetail = "テスト建物12-1-1",
                businessOfficeCode = "101",
                parkingLotCode = "701",
                parkingLotNumber = "1",
                parkingCategory = "1",
                currentStateDivision = "20",
                modificationStateDivision = "10",
                propertyTenant = PropertyTenantContractPojo(
                    tenantBuildingCode = "*********",
                    tenantRoomCode = "02030",
                    tenantRoomNumber = "203",
                    tenantNameKanji = "テスト部屋契約者名",
                    tenantName = "テスト部屋入居者名",
                ),
                cancellationSign = 0,
                moveInStartProcessedSign = 1,
                parkingLotEnable = "1",
                parkingFee = 25000,
                parkingFeeInTax = "0",
                assessmentDivision = "1",
                bulkLeaseFlag = 4,
                thirtyFiveYearLumpSumCategory = "2",
                moveOutDate = 0,
                vacateScheduledDate = 20250101,
                moveInScheduledDate = 20250102,
                specialContractFlag = 0,
                brokerApplicationPossibility = "1",
                offSiteParkingCategory = "0",
                distance = 300,
                beFlg = "3",
            )
            source = source.computeParkingStatusDivision()

            val result = source.toParkingLot(emptyMap(), emptyList())
            assertEquals("*********", result.id.buildingCode.value)
            assertEquals("701", result.id.parkingLotCode.value)
            assertEquals("1", result.localDisplayNumber)
            assertEquals(ParkingLot.Category.SINGLE, result.parkingLotCategory)
            //APPLIEDにならない
            assertEquals(
                ParkingLot.StatusDivision.PLANNED_MOVE_OUT,
                result.parkingStatusDivision
            ) //beFlg=3は退去予定になる
            assertEquals(ParkingLot.VacancyStatus.POSSIBLE, result.vacancyParkingStatus)
            assertEquals(true, result.isAvailable)
            assertEquals(25000, result.parkingFee)
            assertEquals(0, result.parkingFeeInTax)
            assertEquals(ParkingLot.AssessmentDivision.ASSESSMENT, result.assessmentDivision)
            assertEquals(ParkingLot.SpecialContractFlag.NO, result.specialContractFlag)
            assertEquals("42", result.contractForm)
            assertEquals("20250102", result.moveInScheduledDate?.yyyyMMdd())
            assertEquals("20250101", result.expectedMoveOutDate?.yyyyMMdd()) //明け渡し予定日がセットされる
            assertEquals(
                ParkingLot.BrokerApplicationPossibility.POSSIBLE,
                result.brokerApplicationPossibility
            )
            assertEquals(ParkingLot.OffSiteCategory.INSIDE, result.offSiteParkingLotCategory)
            assertEquals(300, result.offSiteParkingDistance)
            assertEquals("*********", result.linkedPropertyId!!.buildingCode.value)
            assertEquals("02030", result.linkedPropertyId!!.roomCode.value)
            assertEquals("203", result.linkedRoomNumber!!.getValue())
            assertEquals(emptyList(), result.reservationList)
        }

        @Test
        @DisplayName("ParkingDetailPojoとParkingReservationのリストからParkingLotへ変換確認(キマサイン)")
        fun testToParkingLotFromKimaSign() {
            var source = ParkingDetailPojo(
                buildingCode = "*********",
                buildingName = "テスト建物名1",
                postalCode = "1234567",
                prefectureKanjiName = "テスト建物1東京都",
                cityKanjiName = "テスト建物1世田谷区",
                townKanjiName = "テスト建物1三軒茶屋",
                addressDetail = "テスト建物12-1-1",
                businessOfficeCode = "101",
                parkingLotCode = "701",
                parkingLotNumber = "1",
                parkingCategory = "1",
                currentStateDivision = "20",
                modificationStateDivision = "10",
                propertyTenant = PropertyTenantContractPojo(
                    tenantBuildingCode = "*********",
                    tenantRoomCode = "02030",
                    tenantRoomNumber = "203",
                    tenantNameKanji = "テスト部屋契約者名",
                    tenantName = "テスト部屋入居者名",
                ),
                cancellationSign = 0,
                moveInStartProcessedSign = 1,
                parkingLotEnable = "1",
                parkingFee = 25000,
                parkingFeeInTax = "0",
                assessmentDivision = "1",
                bulkLeaseFlag = 4,
                thirtyFiveYearLumpSumCategory = "2",
                moveOutDate = 0,
                vacateScheduledDate = 20250101,
                moveInScheduledDate = 20250102,
                specialContractFlag = 0,
                brokerApplicationPossibility = "1",
                offSiteParkingCategory = "0",
                distance = 300,
                beFlg = "3",
            )
            source = source.computeParkingStatusDivision()

            // キマサインからのリクエストの場合、仮予約のみの場合は、利用不可として判断する
            assertEquals(
                ParkingLot.VacancyStatus.POSSIBLE,
                source.toParkingLot(
                    mapOf(
                        ParkingLot.Id(
                            buildingCode = Building.Code.of(source.buildingCode),
                            parkingLotCode = ParkingLot.Code.of(source.parkingLotCode!!),
                        ) to listOf(
                            stubParkingReservationInfo(
                                parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                            )
                        ),
                    ), emptyList(),
                    stubApiKeyAuthInfo(ExternalSystem.KIMAROOM_SIGN)
                ).vacancyParkingStatus
            )

            // キマサインからのリクエストの場合、仮予約と本予約の場合は、利用不可として判断する
            assertEquals(
                ParkingLot.VacancyStatus.IMPOSSIBLE,
                source.toParkingLot(
                    mapOf(
                        ParkingLot.Id(
                            buildingCode = Building.Code.of(source.buildingCode),
                            parkingLotCode = ParkingLot.Code.of(source.parkingLotCode!!),
                        ) to listOf(
                            stubParkingReservationInfo(
                                parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                            ),
                            stubParkingReservationInfo(
                                parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                            )
                        ),
                    ), emptyList(),
                    stubApiKeyAuthInfo(ExternalSystem.KIMAROOM_SIGN)
                ).vacancyParkingStatus
            )

            // ウェルカムパークからのリクエストの場合、仮押さえがある場合は、利用不可として判断する
            assertEquals(
                ParkingLot.VacancyStatus.IMPOSSIBLE, source.toParkingLot(
                    mapOf(
                        ParkingLot.Id(
                            buildingCode = Building.Code.of(source.buildingCode),
                            parkingLotCode = ParkingLot.Code.of(source.parkingLotCode!!),
                        ) to listOf(
                            stubParkingReservationInfo(
                                parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                            )
                        ),
                    ), emptyList(),
                    stubApiKeyAuthInfo(ExternalSystem.WELCOME_PARK)
                ).vacancyParkingStatus
            )
        }

        @Test
        @DisplayName("ParkingDetailPojoからParkingへ変換確認")
        fun testToParking() {
            val groupingList = mutableListOf(
                ParkingDetailPojo(
                    buildingCode = "*********",
                    buildingName = "テスト建物名1",
                    postalCode = "1234567",
                    prefectureKanjiName = "テスト建物1東京都",
                    cityKanjiName = "テスト建物1世田谷区",
                    townKanjiName = "テスト建物1三軒茶屋",
                    addressDetail = "テスト建物12-1-1",
                    businessOfficeCode = "101",
                    parkingLotCode = "701",
                    propertyTenant = PropertyTenantContractPojo(
                        tenantBuildingCode = "*********",
                        tenantRoomCode = "02030",
                    ),
                    buildingBulkLeaseFlag = 1,
                    clientNameKanji = "テスト家主名",
                    completionDeliveryDate = 20250101,
                    allowAllParkingLotAvailabilityEdit = "1",
                ),
                ParkingDetailPojo(
                    buildingCode = "*********",
                    buildingName = "テスト建物名1",
                    postalCode = "1234567",
                    prefectureKanjiName = "テスト建物1東京都",
                    cityKanjiName = "テスト建物1世田谷区",
                    townKanjiName = "テスト建物1三軒茶屋",
                    addressDetail = "テスト建物12-1-1",
                    businessOfficeCode = "101",
                    parkingLotCode = "702",
                    propertyTenant = PropertyTenantContractPojo(
                        tenantBuildingCode = "000024401",
                        tenantRoomCode = "01010",
                    ),
                ),
            )

            val result = groupingList.toParking(emptyMap(), emptyList())
            assertNotNull(result)
            assertEquals(Building.Code.of("*********"), result.building.code)
            assertEquals("テスト建物名1", result.building.name.value)
            assertEquals("1234567", result.building.postalCode)
            assertEquals(
                "テスト建物1東京都 テスト建物1世田谷区 テスト建物1三軒茶屋 テスト建物12-1-1",
                result.building.location
            )
            assertEquals(Office.Code.of("101"), result.building.businessOfficeCode)
            assertEquals(2, result.parkingLotList.size)
            assertEquals("1", result.building.buildingContractForm)
            assertEquals("テスト家主名", result.building.landlordName)
            assertEquals("20250101", result.building.completionDeliveryDate?.yyyyMMdd())
            assertEquals(true, result.allowAllParkingLotAvailabilityEdit)

            val firstParkingLot = result.parkingLotList[0]
            assertEquals("*********", firstParkingLot.id.buildingCode.value)
            assertEquals("701", firstParkingLot.id.parkingLotCode.value)
            assertEquals(emptyList(), firstParkingLot.reservationList)
            assertEquals("*********", firstParkingLot.linkedPropertyId?.buildingCode?.value)
            assertEquals("02030", firstParkingLot.linkedPropertyId?.roomCode?.value)

            val secondParkingLot = result.parkingLotList[1]
            assertEquals("*********", result.parkingLotList[1].id.buildingCode.value)
            assertEquals("702", result.parkingLotList[1].id.parkingLotCode.value)
            assertEquals(emptyList(), result.parkingLotList[1].reservationList)
            assertEquals("000024401", secondParkingLot.linkedPropertyId?.buildingCode?.value)
            assertEquals("01010", secondParkingLot.linkedPropertyId?.roomCode?.value)
        }

        @Test
        @DisplayName("ParkingDetailPojoからParkingへ変換確認（建物完成日の値不正の場合）")
        fun testToParking2() {
            val groupingList = mutableListOf(
                ParkingDetailPojo(
                    buildingCode = "*********",
                    buildingName = "テスト建物名1",
                    postalCode = "1234567",
                    prefectureKanjiName = "テスト建物1東京都",
                    cityKanjiName = "テスト建物1世田谷区",
                    townKanjiName = "テスト建物1三軒茶屋",
                    addressDetail = "テスト建物12-1-1",
                    businessOfficeCode = "101",
                    parkingLotCode = "701",
                    propertyTenant = PropertyTenantContractPojo(
                        tenantBuildingCode = "*********",
                        tenantRoomCode = "02030",
                    ),
                    buildingBulkLeaseFlag = 1,
                    clientNameKanji = "テスト家主名",
                    completionDeliveryDate = 0, // 不正な値
                    allowAllParkingLotAvailabilityEdit = "1",
                ),
                ParkingDetailPojo(
                    buildingCode = "*********",
                    buildingName = "テスト建物名1",
                    postalCode = "1234567",
                    prefectureKanjiName = "テスト建物1東京都",
                    cityKanjiName = "テスト建物1世田谷区",
                    townKanjiName = "テスト建物1三軒茶屋",
                    addressDetail = "テスト建物12-1-1",
                    businessOfficeCode = "101",
                    parkingLotCode = "702",
                    propertyTenant = PropertyTenantContractPojo(
                        tenantBuildingCode = "000024401",
                        tenantRoomCode = "01010",
                    ),
                ),
            )

            val result = groupingList.toParking(emptyMap(), emptyList())
            assertNotNull(result)
            assertEquals(Building.Code.of("*********"), result.building.code)
            assertEquals("テスト建物名1", result.building.name.value)
            assertEquals("1234567", result.building.postalCode)
            assertEquals(
                "テスト建物1東京都 テスト建物1世田谷区 テスト建物1三軒茶屋 テスト建物12-1-1",
                result.building.location
            )
            assertEquals(Office.Code.of("101"), result.building.businessOfficeCode)
            assertEquals(2, result.parkingLotList.size)
            assertEquals("1", result.building.buildingContractForm)
            assertEquals("テスト家主名", result.building.landlordName)
            assertEquals(null, result.building.completionDeliveryDate?.yyyyMMdd())
            assertEquals(true, result.allowAllParkingLotAvailabilityEdit)

            val firstParkingLot = result.parkingLotList[0]
            assertEquals("*********", firstParkingLot.id.buildingCode.value)
            assertEquals("701", firstParkingLot.id.parkingLotCode.value)
            assertEquals(emptyList(), firstParkingLot.reservationList)
            assertEquals("*********", firstParkingLot.linkedPropertyId?.buildingCode?.value)
            assertEquals("02030", firstParkingLot.linkedPropertyId?.roomCode?.value)

            val secondParkingLot = result.parkingLotList[1]
            assertEquals("*********", result.parkingLotList[1].id.buildingCode.value)
            assertEquals("702", result.parkingLotList[1].id.parkingLotCode.value)
            assertEquals(emptyList(), result.parkingLotList[1].reservationList)
            assertEquals("000024401", secondParkingLot.linkedPropertyId?.buildingCode?.value)
            assertEquals("01010", secondParkingLot.linkedPropertyId?.roomCode?.value)
        }
    }

}
