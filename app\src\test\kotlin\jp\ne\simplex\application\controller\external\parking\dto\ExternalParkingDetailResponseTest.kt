package jp.ne.simplex.application.controller.external.parking.dto

import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.model.ParkingReservation
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMddHHmmss
import jp.ne.simplex.stub.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class ExternalParkingDetailResponseTest {
    @Nested
    @DisplayName("ParkingReservationの変換検証")
    inner class Scenario1 {
        @Test
        @DisplayName("ParkingReservationからParkingDetailResponseへ変換確認")
        fun testFrom() {
            val result = ExternalParkingDetailResponse.from(
                stubParkingReservationInfo(
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION,
                    reserveStartDatetime = "20250304000000".yyyyMMddHHmmss(),
                    reserveEndDatetime = "20250404235959".yyyyMMddHHmmss(),
                    remarks = "メモ1",
                    requestSource = ParkingReservation.RequestSource.WELCOME_PARK,
                )
            )
            assertEquals(1, result.reservationStatus)
            assertEquals(0, result.reservationType)
            assertEquals("20250304", result.reserveStartDate)
            assertEquals("20250404", result.reserveEndDate)
            assertEquals("メモ1", result.remarks)
            assertEquals(2, result.reserverSystem)
        }
    }

    @Nested
    @DisplayName("ParkingLotの変換検証")
    inner class Scenario2 {
        @Test
        @DisplayName("ParkingLotからParkingLotResponseへ変換確認")
        fun testFrom() {
            val reservationList = mutableListOf(
                stubParkingReservationInfo(
                    id = ParkingReservation.Id.of("012345678901234567890123456789000001"),
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION,
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                ),
                stubParkingReservationInfo(
                    id = ParkingReservation.Id.of("012345678901234567890123456789000002"),
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                )
            )
            val result = ExternalParkingDetailResponse.from(
                stubParkingLot(
                    parkingLotCode = "701",
                    localDisplayNumber = "A",
                    parkingLotCategory = ParkingLot.Category.KEI,
                    vacancyParkingStatus = ParkingLot.VacancyStatus.IMPOSSIBLE,
                    isAvailable = true,
                    parkingFee = 25000,
                    parkingFeeInTax = 1,
                    assessmentDivision = ParkingLot.AssessmentDivision.OUTSIDE_ASSESSMENT,
                    specialContractFlag = ParkingLot.SpecialContractFlag.YES,
                    expectedMoveOutDate = "20250304",
                    brokerApplicationPossibility = ParkingLot.BrokerApplicationPossibility.POSSIBLE,
                    offSiteParkingLotCategory = ParkingLot.OffSiteCategory.INSIDE,
                    offSiteParkingDistance = 250,
                    linkedBuildingCode = "*********",
                    linkedRoomCode = "02030",
                    linkedRoomNumber = "203",
                    reservationList = reservationList,
                ),
                stubConsumptionTaxRate()
            )
            assertEquals("701", result.parkingLotCode)
            assertEquals("A", result.localDisplayNumber)
            assertEquals(3, result.parkingCategory)
            assertEquals(1, result.vacancyParkingStatus)
            assertEquals(1, result.isAvailable)
            assertEquals(25000, result.fee)
            assertEquals(2, result.assessmentDivision)
            assertEquals(1, result.specialContractFlag)
            assertEquals("20250304", result.expectedMoveOutDate)
            assertEquals(1, result.brokerApplicationPossibility)
            assertEquals(2, result.offSiteParkingCategory)
            assertEquals(250, result.offSiteParkingDistance)
            assertEquals("*********", result.linkedBuildingCode)
            assertEquals("02030", result.linkedRoomCode)
            assertEquals("203", result.linkedRoomNumber)
            assertEquals(2, result.reservationList.size)
        }
    }

    @Nested
    @DisplayName("Parkingの変換検証")
    inner class Scenario3 {
        @Test
        @DisplayName("ParkingからParkingResponseへ変換確認")
        fun testToResponse() {
            val reservationList1 = mutableListOf(
                stubParkingReservationInfo(
                    id = ParkingReservation.Id.of("012345678901234567890123456789000001"),
                    parkingReservationStatus = ParkingReservation.Status.RESERVATION,
                    reservationType = ParkingReservation.Type.AUTO_APPLICATION,
                    requestSource = ParkingReservation.RequestSource.DK_LINK,
                ),
                stubParkingReservationInfo(
                    id = ParkingReservation.Id.of("012345678901234567890123456789000002"),
                    parkingReservationStatus = ParkingReservation.Status.TENTATIVE,
                    reservationType = ParkingReservation.Type.MANUAL_APPLICATION,
                    requestSource = ParkingReservation.RequestSource.KIMA_SIGN,
                )
            )
            val parkingLotList = mutableListOf(
                stubParkingLot(
                    parkingLotCode = "701",
                    localDisplayNumber = "A",
                    parkingLotCategory = ParkingLot.Category.KEI,
                    vacancyParkingStatus = ParkingLot.VacancyStatus.IMPOSSIBLE,
                    isAvailable = true,
                    parkingFee = 25000,
                    assessmentDivision = ParkingLot.AssessmentDivision.OUTSIDE_ASSESSMENT,
                    specialContractFlag = ParkingLot.SpecialContractFlag.YES,
                    expectedMoveOutDate = "20250304",
                    brokerApplicationPossibility = ParkingLot.BrokerApplicationPossibility.POSSIBLE,
                    offSiteParkingLotCategory = ParkingLot.OffSiteCategory.INSIDE,
                    offSiteParkingDistance = 250,
                    linkedBuildingCode = "*********",
                    linkedRoomCode = "02030",
                    linkedRoomNumber = "203",
                    reservationList = reservationList1,
                ),
                stubParkingLot(
                    parkingLotCode = "702",
                    localDisplayNumber = "B",
                    parkingLotCategory = ParkingLot.Category.MULTI_LEVEL,
                    vacancyParkingStatus = ParkingLot.VacancyStatus.POSSIBLE,
                    isAvailable = false,
                    parkingFee = 30000,
                    assessmentDivision = ParkingLot.AssessmentDivision.ASSESSMENT,
                    specialContractFlag = ParkingLot.SpecialContractFlag.NO,
                    expectedMoveOutDate = "20250305",
                    brokerApplicationPossibility = ParkingLot.BrokerApplicationPossibility.IMPOSSIBLE,
                    offSiteParkingLotCategory = ParkingLot.OffSiteCategory.OUTSIDE,
                    offSiteParkingDistance = 300,
                    linkedBuildingCode = "*********",
                    linkedRoomCode = "02030",
                    linkedRoomNumber = "203",
                    reservationList = emptyList(),
                ),

                )
            val consumptionTaxRate = stubConsumptionTaxRate()
            var result = ExternalParkingDetailResponse.from(
                stubParking(
                    buildingCode = "*********",
                    buildingName = "テスト建物名1",
                    postalCode = "1234567",
                    location = "テスト建物1の所在地",
                    businessOfficeCode = "101",
                    parkingLotList = parkingLotList
                ), "https://image.eheya.net/eboard/", consumptionTaxRate
            )
            assertEquals("*********", result.buildingCode)
            assertEquals("テスト建物名1", result.buildingName)
            assertEquals("123-4567", result.postalCode)
            assertEquals("テスト建物1の所在地", result.location)
            assertEquals(
                "https://image.eheya.net/eboard/000/024/*********.jpg",
                result.parkingLayoutImageUrl
            )
            assertEquals("101", result.officeCode)
            assertEquals(2, result.parkingLotList.size)

            result = ExternalParkingDetailResponse.from(
                stubParking(
                    buildingCode = "*********",
                    buildingName = "テスト建物名1",
                    postalCode = "123456",
                    location = "テスト建物1の所在地１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１",
                    businessOfficeCode = "101",
                    parkingLotList = parkingLotList
                ), "https://image.eheya.net/eboard/", consumptionTaxRate
            )
            assertEquals("123456", result.postalCode)
            assertEquals(
                "テスト建物1の所在地１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０１２３４５６７８９０",
                result.location
            )
        }
    }

    @Nested
    @DisplayName("ParkingContractPossibilityの変換検証")
    inner class Scenario4 {
        @Test
        @DisplayName("ParkingContractPossibilityからContractPossibleStatusResponseへ変換確認")
        fun testToResponse() {
            var result = ExternalParkingDetailResponse.from(
                stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.REQUIRED_CONFIRM,
                    isAutoJudge = ContractPossibilityAutoJudge.MANUAL
                )
            )
            assertEquals(2, result.firstParking)
            assertEquals(2, result.secondParking)
            assertEquals(0, result.isAutoJudge)

            result = ExternalParkingDetailResponse.from(
                stubParkingContractPossibility(
                    firstParkingContractPossibility = ContractPossibility.POSSIBLE,
                    secondParkingContractPossibility = ContractPossibility.REQUIRED_CONFIRM,
                    isAutoJudge = ContractPossibilityAutoJudge.AUTO
                )
            )
            assertEquals(0, result.firstParking)
            assertEquals(2, result.secondParking)
            assertEquals(1, result.isAutoJudge)
        }
    }
}
