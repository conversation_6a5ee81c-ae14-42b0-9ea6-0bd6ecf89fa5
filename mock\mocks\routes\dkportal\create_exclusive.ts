/**
 * DKポータル/先行物件登録API
 *
 * サンプルリクエスト（居住用先行物件登録）
 *  curl -i -X POST -H "Content-Type: application/json" http://localhost:8083/dkportal -d '{"query": "mutation { createExclusiveHousing(dk_link_id: 1, sales_office_id: 645, kentaku_building_code: \"*********\", kentaku_room_code:\"01010\", exclusive_from: \"20250502\", exclusive_to: \"20250509\", e_code: \"E12345678\", company_type: 0) { status } }"}'
 *
 * サンプルリクエスト（事業用先行物件登録）
 *  curl -i -X POST -H "Content-Type: application/json" http://localhost:8083/dkportal -d '{"query": "mutation { createExclusiveBusiness(dk_link_id: 1, sales_office_id: 645, kentaku_building_code: \"*********\", kentaku_room_code:\"01010\", exclusive_from: \"20250502\", exclusive_to: \"20250509\", e_code: \"E12345678\", company_type: 0) { status } }"}'
*
 */

import { DKPortalValidationError } from '../../shared/dkportal/dkportal_response';
import { deserializeGraphqlBody, getOperationName } from '../../shared/shared_function';

// 居住用先行物件登録APIのoperation名
const targetOperationHousing = 'createExclusiveHousing';

// 事業用先行物件登録APIのoperation名
const targetOperationBusiness = 'createExclusiveBusiness';

export default [
  {
    id: 'dkportal_create_exclusive',
    url: '/dkportal',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: async (req, res, next) => {
            const query = req.body['query'];
            // 対象の関数ではない場合は処理を終了
            const operation = getOperationName(query);
            if (targetOperationBusiness !== operation && targetOperationHousing !== operation) {
              next();
              return;
            }

            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const body = deserializeGraphqlBody(query);
            const result = await checkParameter(body);

            if (result) {
              res.status(400);
              res.send({
                errors: [result],
                data: { [operation]: null },
              });
              return;
            }

            const { dk_link_id, kentaku_building_code, kentaku_room_code ,exclusive_from, exclusive_to, company_type, e_code} = body;

            res.status(200);
            res.send({
              data: {
                [operation]: [
                  {
                    id: dk_link_id.value,
                    kentaku_building_code: kentaku_building_code.value,
                    kentaku_room_code: kentaku_room_code.value,
                    exclusive_from: exclusive_from.value,
                    exclusive_to: exclusive_to.value,
                    e_code: e_code?.value,
                    auto_delete_flg: false,
                    company_type: parseInt(company_type.value)
                 }
              ]
              },
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            res.status(500);
            res.send({
              error_message: '予期せぬエラーが発生しました。',
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する。適切な場合はtrueを返す。
 * @param {*} body
 * @returns {Boolean}
 */
async function checkParameter(body): Promise<DKPortalValidationError> {
  const { dk_link_id, sales_office_id, kentaku_building_code, kentaku_room_code ,exclusive_from, exclusive_to, company_type, e_code} = body;

  // 必須チェック
  if (!dk_link_id) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'dk_link_id',
      '先行物件IDは必須です。',
    );
  }
  if (!sales_office_id) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'sales_office_id',
      '営業所コードは必須です。',
    );
  }
  if (!kentaku_building_code) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'kentaku_building_code',
      '建物コードは必須です。',
    );
  }

  if (!kentaku_room_code) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'kentaku_room_code',
      '部屋コードは必須です。',
    );
  }

  if (!exclusive_from) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'exclusive_from',
      '先行期間fromは必須です。',
    );
  }

  if (!exclusive_to) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'exclusive_to',
      '先行期間toは必須です。',
    );
  }

  if (!company_type) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'company_type',
      '先行先種別は必須です。',
    );
  }

  if (company_type.value == 0 && !e_code) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'e_code',
      '先行先種別が0の場合、Eコードは必須です。',
    );
  }

  // ex) *********	9桁
  if (kentaku_building_code.value.length !== 9) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'kentaku_building_code',
      '建物コードは9桁で指定してください。',
    );
  }

  // ex) 01010	5桁
  if (kentaku_room_code.value.length !== 5) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'kentaku_room_code',
      '部屋コードは5桁で指定してください。',
    );
  }

  return undefined;
}
