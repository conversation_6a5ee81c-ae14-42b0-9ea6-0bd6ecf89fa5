package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.repository.db.BuildingMasterRepositoryInterface
import org.springframework.stereotype.Service

@Service
class LoginParkingService(
    private val buildingMasterRepository: BuildingMasterRepositoryInterface,
) {
    /**
     * SSOAPI用
     * 有効な建物コードであれば駐車場詳細画面、そうでなければnullを返します
     */
    fun getPath(buildingCode: Building.Code?): String? {
        if (buildingCode != null && buildingMasterRepository.isExist(buildingCode)) {
            return "/parkingDetails?buildingCd=${buildingCode.value}"
        }
        return null
    }
}
