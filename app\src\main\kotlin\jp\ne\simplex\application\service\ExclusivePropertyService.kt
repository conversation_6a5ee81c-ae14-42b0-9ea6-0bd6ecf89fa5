package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ExclusivePropertyAction.Status
import jp.ne.simplex.application.repository.db.*
import jp.ne.simplex.application.repository.external.dkportal.DKPortalRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.shared.MultiThreadRunBlocking.Companion.runAsyncTasks
import org.jooq.DSLContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ExclusivePropertyService(
    private val dslContext: DSLContext,
    private val repository: ExclusivePropertyRepository,
    private val agentRepository: AgentRepositoryInterface,
    private val propertyRepository: PropertyRepositoryInterface,
    private val dkPortalRepository: DKPortalRepositoryInterface,
    private val temporaryReservationRepository: TemporaryReservationRepositoryInterface,
    private val propertyMaintenanceRepository: PropertyMaintenanceRepositoryInterface,
    private val employeeRepository: EmployeeRepositoryInterface,
) {

    companion object {
        private val log = LoggerFactory.getLogger(ExclusivePropertyService::class.java)
    }

    fun search(parameter: ExclusivePropertiesSearch): Pair<List<ExclusivePropertyInfo>, Int> {

//        val employee = employeeRepository.findBy(authInfo.getEmployeeCode())?:
//        return Pair(
//            emptyList<ExclusivePropertyInfo>(),
//            0
//        )
//        val officeCode = employee.getOfficeCode() ?:
//        return Pair(
//            emptyList<ExclusivePropertyInfo>(),
//            0
//        )

        return repository.search(parameter)
    }

    fun register(
        authInfo: AuthInfo.Jwt,
        parameter: RegisterExclusiveProperty
    ): ExclusivePropertyAction.Result<ExclusiveProperty.ExclusiveTarget> {
        val property = propertyRepository.findBy(parameter.propertyId)
            ?: throw ServerValidationException(ErrorMessage.PROPERTY_NOT_FOUND.format())

        if (property.marketingBranchOfficeCode == null) {
            throw ServerValidationException(ErrorMessage.REGISTER_EXCLUSIVE_PROPERTY_MARKETING_BRANCH_NOT_SET.format())
        }
        //空き家ではないため、先行公開はできません
        if (property.changeDivision == null || property.customerCompletionFlag) {
            throw ServerValidationException(ErrorMessage.REGISTER_EXCLUSIVE_PROPERTY_EMPTY_NOT.format())
        }

        // 方位未設定物件は先行公開不可
        if (property.direction == null) {
            throw ServerValidationException(ErrorMessage.REGISTER_EXCLUSIVE_PROPERTY_DIRECTION_NOT_SET.format())
        }

        // Eコードが不正な値でないかチェックし、チェックに失敗した場合は、即時リターン
        parameter.exclusiveTargetWithIds.mapNotNull { it.target.eCode }
            .checkECodes().takeIf { it.isFailed() }?.let { return it }

        //val temporaryReservation = temporaryReservationRepository.findBy(parameter.propertyId)

        //if (property.isTemporaryReserved(temporaryReservation)) {
        if (property.isTemporaryReserved()) {
            // 仮押さえ中物件は先行公開不可
            throw ServerValidationException(ErrorMessage.REGISTER_EXCLUSIVE_UNAVAILABLE_STATUS_TEMPORARY_RESERVATION.format())
        }
        //if (property.isApplicationSubmitted(temporaryReservation)) {
        if (property.isApplicationSubmitted()) {
            // 申込済み物件は先行公開不可
            throw ServerValidationException(ErrorMessage.REGISTER_EXCLUSIVE_UNAVAILABLE_STATUS_APPLICATION_SUBMITTED.format())
        }

        val propertyMaintenance = propertyMaintenanceRepository.findBy(parameter.propertyId)
        if (propertyMaintenance?.publishStatus == PropertyMaintenance.PublishStatus.PUBLIC) {
            // 公開指示済の物件は先行公開できない
            throw ServerValidationException(ErrorMessage.REGISTER_EXCLUSIVE_PROPERTY_NOT_ALLOWED.format())
        }

        repository.listBy(parameter.propertyId)
            // 先行先が重複していないかチェック
            .checkConflictExclusiveTarget(parameter.exclusiveTargetWithIds)
            // 上記いずれかのチェックに失敗した場合は、即時リターン
            .takeIf { it.isFailed() }?.let { return it }

        val updateFailedList = mutableListOf<ExclusiveProperty.ExclusiveTarget>()

        runAsyncTasks(maxThreads = 10, tasks = parameter.getRecords()) { record ->
            runCatching {
                dslContext.transaction { config ->
                    repository.register(config, authInfo, property, record)
                        .run { dkPortalRepository.createExclusive(property, record) }
                }
            }.onFailure {
                val exclusiveTarget = record.exclusiveTargetWithId.target
                log.warn("Failed to update exclusive property: $exclusiveTarget", it)
                updateFailedList.add(exclusiveTarget)
            }
        }

        val isSuccess = updateFailedList.isEmpty()

        return ExclusivePropertyAction.Result(
            status = if (isSuccess) Status.SUCCESS else Status.FAILED,
            reason = if (isSuccess) null else ExclusivePropertyAction.StatusReason.DB_UPDATE_FAILED,
            failedList = updateFailedList
        )
    }

    fun update(
        authInfo: AuthInfo.Jwt,
        parameter: UpdateExclusiveProperty
    ): ExclusivePropertyAction.Result<ExclusiveProperty.ExclusiveTarget> {
        // 不正な先行公開IDが指定された場合は、Exceptionをスローする
        val targetInfo = repository.listBy(listOf(parameter.id), ComeFromType.Update).firstOrNull()
            ?: throw ServerValidationException(ErrorMessage.EXCLUSIVE_PROPERTY_NOT_FOUND.format())

        val targetProperty =
            propertyRepository.findBy(targetInfo.propertyId)
                ?: throw ServerValidationException(ErrorMessage.PROPERTY_NOT_FOUND.format())

        if (targetInfo.isAlreadyFinished()) {
            throw ServerValidationException(ErrorMessage.EXCLUSIVE_PROPERTY_IS_EXPIRED.format())
        }

        // 既に公開済みの場合
        if (targetInfo.isAlreadyPublished()) {
            // 先行先が変更されていないことを確認する
            if (parameter.getExclusiveTargetList().size != 1 ||
                parameter.getExclusiveTargetList().firstOrNull() != targetInfo.exclusiveTarget
            ) {
                throw ServerValidationException(ErrorMessage.UPDATE_EXCLUSIVE_PROPERTY_NOT_ALLOWED.format())
            }

            // 先行期間の開始日付が更新されていないことを確認する
            if (!parameter.exclusiveRange.from.isEqual(targetInfo.exclusiveRange.from)) {
                throw ServerValidationException(ErrorMessage.UPDATE_EXCLUSIVE_PROPERTY_NOT_ALLOWED.format())
            }
        }
        // まだ公開済みでない場合
        else {
            // Eコードが不正な値でないかチェックし、チェックに失敗した場合は、即時リターン
            parameter.exclusiveTargetWithIds.mapNotNull { it.target.eCode }
                .checkECodes().takeIf { it.isFailed() }?.let { return it }
        }
        //物件Xと先行先αは組み合わせで1つのみ有効。期間が重複していなくてエラーを返す
        repository.listBy(targetInfo.propertyId).filter { it.id != parameter.id }
            // 先行先が重複していないかチェック
            .checkConflictExclusiveTarget(parameter.exclusiveTargetWithIds)
            // 上記いずれかのチェックに失敗した場合は、即時リターン
            .takeIf { it.isFailed() }?.let { return it }

        val updateFailedList = mutableListOf<ExclusiveProperty.ExclusiveTarget>()

        // 先行先が変更されている場合（先行先A → 先行先B）、既存先行先レコードを論理削除する
        if (!parameter.exclusiveTargetWithIds.any { it.target == targetInfo.exclusiveTarget }) {
            runCatching {
                dslContext.transaction { config ->
                    repository.cancel(config, authInfo, targetInfo)
                        .run {
                            dkPortalRepository.deleteExclusive(targetProperty, parameter.id)
                        }
                }
            }.onFailure {
                val exclusiveTarget = targetInfo.exclusiveTarget
                log.warn("Failed to delete exclusive property: $exclusiveTarget", it)
                updateFailedList.add(exclusiveTarget)
            }
        }

        runAsyncTasks(maxThreads = 10, tasks = parameter.getRecords(targetProperty)) { record ->
            runCatching {
                dslContext.transaction { config ->
                    if (record.exclusiveTargetWithId.target == targetInfo.exclusiveTarget) {
                        repository.update(config, authInfo, targetInfo.id, record)
                            .run {
                                dkPortalRepository.updateExclusive(
                                    targetProperty,
                                    targetInfo.id,
                                    record
                                )
                            }
                        return@transaction
                    }
                    repository.register(config, authInfo, targetProperty, record)
                        .run {
                            dkPortalRepository.createExclusive(targetProperty, record)
                        }
                    return@transaction
                }
            }.onFailure {
                val exclusiveTarget = record.exclusiveTargetWithId.target
                log.warn("Failed to update exclusive property: $exclusiveTarget", it)
                updateFailedList.add(exclusiveTarget)
            }
        }

        val isSuccess = updateFailedList.isEmpty()

        return ExclusivePropertyAction.Result(
            status = if (isSuccess) Status.SUCCESS else Status.FAILED,
            reason = if (isSuccess) null else ExclusivePropertyAction.StatusReason.DB_UPDATE_FAILED,
            failedList = updateFailedList
        )
    }

    fun cancel(
        authInfo: AuthInfo.Jwt,
        ids: List<ExclusiveProperty.Id>
    ): ExclusivePropertyAction.Result<ExclusiveProperty.Id> {
        val exclusiveProperties =
            repository.listBy(ids, ComeFromType.Cancel).also { list ->
                list.checkIds(ids).takeIf { it.isFailed() }?.let { return it }
                list.checkExclusiveRangeExpired().takeIf { it.isFailed() }?.let { return it }
            }

        val propertyIds = exclusiveProperties.map { it.propertyId }.distinct()

        val properties = propertyRepository.list(propertyIds)
            .takeIf { it.size == propertyIds.size }?.associate { it.id to it }
            ?: throw ServerValidationException(ErrorMessage.PROPERTY_NOT_FOUND.format())

        val updateFailedList = mutableListOf<ExclusiveProperty.Id>()

        runAsyncTasks(maxThreads = 10, tasks = exclusiveProperties) { exclusive ->
            runCatching {
                val targetProperty = properties[exclusive.propertyId]!!

                dslContext.transaction { config ->
                    repository.cancel(config, authInfo, exclusive)
                        .run {
                            dkPortalRepository.deleteExclusive(targetProperty, exclusive.id)
                        }
                }
            }.onFailure {
                val exclusiveId = exclusive.id
                log.warn("Failed to delete exclusive property: $exclusiveId", it)
                updateFailedList.add(exclusiveId)
            }
        }

        val isSuccess = updateFailedList.isEmpty()

        return ExclusivePropertyAction.Result(
            status = if (isSuccess) Status.SUCCESS else Status.FAILED,
            reason = if (isSuccess) null else ExclusivePropertyAction.StatusReason.DB_DELETE_FAILED,
            failedList = updateFailedList
        )
    }

    /**
     * リクエストのEコードの不正チェックを行う
     *
     * 一つでも不正なEコードが存在する場合は、エラーとする
     */
    private fun List<Agent.ECode>.checkECodes(): ExclusivePropertyAction.Result<ExclusiveProperty.ExclusiveTarget> {
        return this
            .filter { it !in agentRepository.listBy(this).map(Agent::eCode) }
            .takeIf { it.isNotEmpty() }
            ?.let {
                ExclusivePropertyAction.Result(
                    status = Status.FAILED,
                    reason = ExclusivePropertyAction.StatusReason.INVALID_ECODE,
                    failedList = it.map { eCode ->
                        ExclusiveProperty.ExclusiveTarget(
                            companyType = ExclusiveProperty.CompanyType.RealEstate,
                            eCode = eCode
                        )
                    },
                )
            }
            ?: ExclusivePropertyAction.Result.success()
    }

    /**
     * 先行公開操作を行ったユーザーの営業所がバッティングしていないかを確認する
     *
     * 対象物件に対して、既に複数の営業所によって先行公開されている（基本あり得ない）か
     * 自身の営業所と異なる営業所によって先行公開されている場合は、エラーとする（公開前か後かは関係ない）
     */
    private fun List<ExclusivePropertyInfo>.checkConflictOffice(authInfo: AuthInfo.Jwt) {
        // 既に先行公開されている（予定）物件の営業所コードを重複排除して取得
        val officeCodesInUse = this.map { it.creatorAffiliationOfficeCode }.distinct()

        // あり得ないが、すでに複数営業所によって先行公開されているので、NG
        if (officeCodesInUse.size > 1) {
            throw ServerValidationException(ErrorMessage.EXCLUSIVE_PROPERTY_OFFICE_CODE_DUPLICATE.format())
        }
        if (officeCodesInUse.size == 1) {
            if (officeCodesInUse.first() != authInfo.businessOfficeCode) {
                throw ServerValidationException(ErrorMessage.EXCLUSIVE_PROPERTY_OFFICE_CODE_DUPLICATE.format())
            }
            return
        }
        return
    }

    /**
     * 物件Xと先行先αは組み合わせで1つのみ有効。期間が重複していなくてエラーを返す
     *
     */
    private fun List<ExclusivePropertyInfo>.checkConflictExclusiveTarget(
        exclusiveTargetWithIds: List<ExclusiveProperty.ExclusiveTargetWithId>,
    ): ExclusivePropertyAction.Result<ExclusiveProperty.ExclusiveTarget> {
        return this
            .mapNotNull { info ->
                val index =
                    exclusiveTargetWithIds.map { it.target }.indexOf(info.exclusiveTarget)
                if (index == -1) {
                    return@mapNotNull null
                }
//                if (!info.exclusiveRange.isConflict(exclusiveDateRange)) {
//                    return@mapNotNull null
//                }
                return@mapNotNull exclusiveTargetWithIds[index]
            }
            .distinct()
            .takeIf { it.isNotEmpty() }
            ?.let {
                ExclusivePropertyAction.Result(
                    status = Status.FAILED,
                    reason = ExclusivePropertyAction.StatusReason.EXCLUSIVE_TARGET_DUPLICATE,
                    failedList = it.map(ExclusiveProperty.ExclusiveTargetWithId::target),
                )
            }
            ?: ExclusivePropertyAction.Result.success()
    }

    /**
     * リクエストパラメータの先行公開IDのチェック
     *
     * レシーバーのリストに、引数で指定されたIDが存在しない場合、エラーとする
     */
    private fun List<ExclusivePropertyInfo>.checkIds(requestedIds: List<ExclusiveProperty.Id>): ExclusivePropertyAction.Result<ExclusiveProperty.Id> {
        return requestedIds
            .filter { id -> this.none { it.id == id } }
            .takeIf { it.isNotEmpty() }
            ?.let {
                ExclusivePropertyAction.Result(
                    status = Status.FAILED,
                    reason = ExclusivePropertyAction.StatusReason.INVALID_ID_NOT_FOUND,
                    failedList = it
                )
            }
            ?: ExclusivePropertyAction.Result.success()
    }

    /**
     * 先行公開期間が終了しているかどうかをチェックする
     *
     * レシーバーのリストに、先行期間が終了している物件が存在する場合、エラーとする
     */
    private fun List<ExclusivePropertyInfo>.checkExclusiveRangeExpired(): ExclusivePropertyAction.Result<ExclusiveProperty.Id> {
        return this
            .filter { it.isAlreadyFinished() }
            .map { it.id }
            .takeIf { it.isNotEmpty() }
            ?.let {
                ExclusivePropertyAction.Result(
                    status = Status.FAILED,
                    reason = ExclusivePropertyAction.StatusReason.INVALID_ID_COMPLETED,
                    failedList = it
                )
            }
            ?: ExclusivePropertyAction.Result.success()
    }

    fun getExclusivePropertyInfo(): List<ExclusivePropertyInfo> {
        val param = ExclusivePropertiesSearchForBatch(
            listingSituationTypeList = listOf(PublishStatus.InProgress),
            companyTypeList = listOf(ExclusiveProperty.CompanyType.Leasing)
        )
        return repository.findExclusivePropertyForECloudBatch(param)
    }

    fun getExclusivePropertyInfoForFcNyuKoBatch(): List<ExclusivePropertyInfo> {
        val param = ExclusivePropertiesSearchForBatch(
            listingSituationTypeList = listOf(PublishStatus.InProgress),
            companyTypeList = listOf(ExclusiveProperty.CompanyType.RealEstate)
        )
        return repository.findExclusivePropertyForFcNyuKoBatch(param)
    }

    fun filterDirectManagedExclusiveProperty(
        target: ExclusivePropertyInfo,
        temporaryReservationService: TemporaryReservationService,
        propertyService: PropertyService
    ) {
        // 仮押さえチェック
        val id = Property.Id(
            buildingCode = Building.Code.of(target.propertyId.buildingCode.value),
            roomCode = Room.Code.of(target.propertyId.roomCode.value),
        )
        val temporaryReservationInfo = temporaryReservationService.get(id)
        val property = propertyService.get(id)
        if (property.isTemporaryReserved(temporaryReservationInfo)) {
            throw ServerValidationException(ErrorMessage.PUBLISH_UNAVAILABLE_STATUS_TEMPORARY_RESERVATION.format())
        }
    }
}
