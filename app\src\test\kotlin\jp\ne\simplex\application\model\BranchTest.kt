package jp.ne.simplex.application.model

import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.TestCase

class BranchTest : FunSpec({

    context("支店コードは、文字数3文字以上しか受け付けないこと") {

        test("支店コードが3文字未満の時、インスタンス生成に失敗すること") {
            shouldThrow<ModelCreationFailedException> {
                Branch.Code.of("")
            }
            shouldThrow<ModelCreationFailedException> {
                Branch.Code.of("1")
            }
            shouldThrow<ModelCreationFailedException> {
                Branch.Code.of("12")
            }
            shouldNotThrow<ModelCreationFailedException> {
                Branch.Code.of("123")
            }
        }
    }

    context("支店コードを取得できること") {

        listOf(
            TestCase(input = "123", expected = "123"),
            TestCase(input = "1234", expected = "123"),
            TestCase(input = "12345", expected = "123"),
            TestCase(input = "123456", expected = "123"),
        ).forEach {
            test("支店コードが${it.input}の時、prefix()で取得できる値は${it.expected}であること") {
                Branch.Code.of(it.input).getPrefix().shouldBe(it.expected)
            }
        }
    }
})
