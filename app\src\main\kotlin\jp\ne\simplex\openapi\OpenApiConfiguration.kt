package jp.ne.simplex.openapi

import io.swagger.v3.oas.models.headers.Header
import io.swagger.v3.oas.models.media.Content
import io.swagger.v3.oas.models.media.MediaType
import io.swagger.v3.oas.models.responses.ApiResponse
import io.swagger.v3.oas.models.security.SecurityRequirement
import io.swagger.v3.oas.models.security.SecurityScheme
import jp.ne.simplex.configuration.ApplicationConstants
import org.springdoc.core.customizers.OpenApiCustomizer
import org.springdoc.core.models.GroupedOpenApi
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping

@Configuration
class OpenApiConfiguration(
    private val requestMappingHandlerMapping: RequestMappingHandlerMapping
) {

    @Bean
    fun internalApiGroup(): GroupedOpenApi {
        return GroupedOpenApi.builder()
            .group("INTERNAL") // 内部用APIグループ名
            .pathsToExclude(ApplicationConstants.EXTERNAL_SERVLET_PATH + "/**")
            .build()
    }

    @Bean
    fun externalApiGroup(): GroupedOpenApi {
        return GroupedOpenApi.builder()
            .group("EXTERNAL")
            .pathsToMatch(ApplicationConstants.EXTERNAL_SERVLET_PATH + "/**")
            .addOpenApiCustomizer { openApi ->
                val securitySchemeName = "API Key"
                openApi.components.addSecuritySchemes(
                    securitySchemeName,
                    SecurityScheme()
                        .type(SecurityScheme.Type.APIKEY)
                        .`in`(SecurityScheme.In.HEADER)
                        .name("x-api-key")
                )
                openApi.addSecurityItem(SecurityRequirement().addList(securitySchemeName))
            }
            .build()
    }

    @Bean
    fun customOpenApiCustomizer(): OpenApiCustomizer {
        return OpenApiCustomizer { openApi ->
            requestMappingHandlerMapping.handlerMethods.forEach { (requestMappingInfo, handlerMethod) ->
                // Controllerクラスの @GetMapping, @PostMapping アノテーションを付与しているメソッド名
                val method = handlerMethod.method
                val returnType = method.returnType.simpleName

                // 当該メソッドから @ApiDefinition アノテーションの情報を抽出する
                val targetFunc = method.getAnnotation(ApiDefinition::class.java)

                if (targetFunc != null) {
                    val path =
                        requestMappingInfo.pathPatternsCondition?.patterns?.first()?.patternString

                    // 当該メソッドのエンドポイントから対応するOperation情報を取得する
                    val operation = openApi.paths[path]?.readOperations()?.find {
                        it.operationId == handlerMethod.method.name
                    }

                    operation?.apply {
                        summary = targetFunc.summary
                        description = targetFunc.description
                        responses.addApiResponse(
                            "200",
                            ApiResponse().apply {
                                if (targetFunc.responseHeaderName.isNotEmpty())
                                    headers = mapOf(
                                        targetFunc.responseHeaderName to Header().apply {
                                            description =
                                                targetFunc.responseHeaderDescription
                                            example = targetFunc.responseHeaderExample
                                            schema = io.swagger.v3.oas.models.media.Schema<Any>()
                                                .type(targetFunc.responseHeaderClass.simpleName)
                                                .example(targetFunc.responseHeaderExample)
                                        }
                                    )
                                if (returnType != "void")
                                    content = Content().addMediaType(
                                        org.springframework.http.MediaType.APPLICATION_JSON_VALUE,
                                        MediaType().schema(
                                            io.swagger.v3.oas.models.media.Schema<Any>()
                                                .`$ref`("#/components/schemas/${returnType}")
                                        )
                                    )
                            }
                        )
                    }
                }
            }
        }
    }
}
