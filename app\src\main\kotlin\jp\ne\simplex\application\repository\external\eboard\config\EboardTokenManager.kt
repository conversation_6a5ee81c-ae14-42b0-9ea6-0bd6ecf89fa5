package jp.ne.simplex.application.repository.external.eboard.config

class EboardTokenManager private constructor() {
    companion object {
        private var token: String? = null

        @Synchronized
        fun getToken(): String? {
            return token
        }

        @Synchronized
        fun updateToken(newToken: String) {
            token = newToken
        }

        @Synchronized
        fun clear() {
            token = null
        }
    }
}