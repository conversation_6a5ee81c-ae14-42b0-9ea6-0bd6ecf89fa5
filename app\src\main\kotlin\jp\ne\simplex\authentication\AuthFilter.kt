package jp.ne.simplex.authentication

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jp.ne.simplex.configuration.ApplicationConstants
import org.springframework.web.filter.OncePerRequestFilter

class AuthFilter(
    private val apiKeyVerifier: ApiKeyVerifier,
    private val jwtVerifier: JwtVerifier
) : OncePerRequestFilter() {

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        if (request.servletPath.startsWith(ApplicationConstants.EXTERNAL_SERVLET_PATH)) {
            apiKeyVerifier.verify(request)
        } else {
            jwtVerifier.verify(request, response)
        }
        filterChain.doFilter(request, response)
    }
}
