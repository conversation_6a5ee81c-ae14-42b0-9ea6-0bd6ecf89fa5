package jp.ne.simplex.application.repository.sftp

import com.jcraft.jsch.ChannelSftp
import com.jcraft.jsch.JSch
import com.jcraft.jsch.ProxyHTTP
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.application.repository.proxy.Proxy
import jp.ne.simplex.shared.StringExtension.Companion.toMap
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.nio.file.Path

@Profile("batch")
@Component
class SftpTemplate(
    private val secretManager: SecretManagerRepository,
    private val sftpConfig: SftpConfig,
    private val jsch: JSch = JSch(),
    private val proxy: Proxy?
) {
    private val log = LoggerFactory.getLogger(SftpTemplate::class.java)

    private var session: com.jcraft.jsch.Session? = null
    private var channel: ChannelSftp? = null

    fun connect() {
        if (isSessionIsAlreadyConnected() || isChannelIsAlreadyConnected()) {
            throw IllegalStateException("Session or Channel is already connected")
        }
        session = createSession()
        session!!.connect()
        channel = session!!.openChannel("sftp") as ChannelSftp
        channel!!.connect()
    }

    fun sendFile(localFilePath: Path, remoteFileName: String) {
        if (!isChannelIsAlreadyConnected()) {
            throw IllegalStateException("Channel is not connected")
        }
        channel!!.cd(sftpConfig.sendDirectory)
        channel!!.put(localFilePath.toString(), remoteFileName)
        log.info("File successfully uploaded to SFTP: $remoteFileName")
    }

    fun disconnect() {
        channel?.disconnect()
        channel = null
        session?.disconnect()
        session = null
    }

    private fun createSession(): com.jcraft.jsch.Session {
        val secrets = secretManager.getValue(sftpConfig.secretId).toMap()
        val username = secrets["username"] as String
        val hostKey = secrets["hostKey"] as String
        val clientPrivateKey = secrets["clientPrivateKey"] as String

        //クライアント認証に使う秘密鍵を設定
        jsch.addIdentity("privateKey", clientPrivateKey.toByteArray(Charsets.UTF_8), null, null)

        //サーバ認証に使うサーバ側の公開鍵を設定
        jsch.setKnownHosts(ByteArrayInputStream(hostKey.toByteArray(Charsets.UTF_8)))
        val session = jsch.getSession(username, sftpConfig.host, 22)
        if (proxy != null) {
            session.setProxy(ProxyHTTP(proxy.host, proxy.port))
        }
        session.setConfig("StrictHostKeyChecking", "yes")
        return session
    }

    private fun isSessionIsAlreadyConnected(): Boolean {
        return session != null && session!!.isConnected
    }

    private fun isChannelIsAlreadyConnected(): Boolean {
        return channel != null && channel!!.isConnected
    }
}
