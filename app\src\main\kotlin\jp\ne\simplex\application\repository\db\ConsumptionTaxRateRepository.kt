package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.ConsumptionTaxRate
import jp.ne.simplex.application.repository.db.extension.ConsumptionTaxRateEx.Companion.toConsumptionTaxRate
import jp.ne.simplex.db.jooq.gen.tables.pojos.ConsumptionTaxRateMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.references.CONSUMPTION_TAX_RATE_MASTER
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.time.LocalDate

@Repository
class ConsumptionTaxRateRepository(private val context: DSLContext) :
    ConsumptionTaxRateRepositoryInterface {
    override fun findActive(): ConsumptionTaxRate? {
        val now = LocalDate.now().yyyyMMdd().toInt()
        return context.selectFrom(CONSUMPTION_TAX_RATE_MASTER)
            .where(CONSUMPTION_TAX_RATE_MASTER.EFFECTIVE_START_DATE.le(now))
            .and(CONSUMPTION_TAX_RATE_MASTER.EFFECTIVE_END_DATE.ge(now))
            .and(CONSUMPTION_TAX_RATE_MASTER.CONSUMPTION_TAX_MANAGEMENT_CODE.eq("01")) // 01:消費税率
            .fetchOneInto(ConsumptionTaxRateMasterPojo::class.java)?.toConsumptionTaxRate()
    }
}

interface ConsumptionTaxRateRepositoryInterface {
    fun findActive(): ConsumptionTaxRate?
}
