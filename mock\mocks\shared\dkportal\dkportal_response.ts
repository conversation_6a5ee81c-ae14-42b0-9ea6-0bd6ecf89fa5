export class DKPortalValidationError {
  message: string;
  locations: DKPortalErrorLocation[];
  path: string[];
  extensions: { validation: { [key: string]: string[] } };

  constructor(operation: string, property_name: string, message: string) {
    this.message = `Validation failed for the field [${operation}}].`;
    this.locations = [new DKPortalErrorLocation()];
    this.path = [operation];
    this.extensions = {
      validation: {
        [property_name]: [message],
      },
    };
  }
}

class DKPortalErrorLocation {
  line: number;
  column: number;

  constructor(line: number = 5, column: number = 3) {
    this.line = line;
    this.column = column;
  }
}
