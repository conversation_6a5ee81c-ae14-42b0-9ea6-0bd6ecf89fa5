package jp.ne.simplex.shared

import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull

class AssertionHelper {

    companion object {

        /**
         * 第一引数に指定されたクラスの全プロパティの中で、
         * 第二引数で指定されたプロパティ全ての値が NULL で、それ以外は NOT-NULL であることをチェックする
         */
        fun assertNullProperties(instance: Any, nullValuePropertyNames: List<String>) {
            for (field in instance::class.java.getDeclaredFields()) {
                field.setAccessible(true)
                val value: Any? = field.get(instance)

                if (nullValuePropertyNames.contains(field.name)) {
                    assertNull(
                        value,
                        "${field.name}=\"$value\", but \"$value\" is expected null"
                    )
                } else {
                    assertNotNull(
                        value,
                        "${field.name}=\"$value\", but \"$value\" is expected not null"
                    )
                }
            }
        }

        /**
         * 第一引数に指定されたクラスの全プロパティの中で、
         * 第二引数で指定されたプロパティ全ての値が NOT-NULL で、それ以外は NULL であることをチェックする
         */
        fun assertNotNullProperties(instance: Any, notNullValuePropertyNames: List<String>) {
            for (field in instance::class.java.getDeclaredFields()) {
                field.setAccessible(true)
                val value: Any? = field.get(instance)

                if (notNullValuePropertyNames.contains(field.name)) {
                    assertNotNull(
                        value,
                        "${field.name}=\"$value\", but \"$value\" is expected not null"
                    )
                } else {
                    assertNull(
                        value,
                        "${field.name}=\"$value\", but \"$value\" is expected null"
                    )
                }
            }
        }
    }
}