package jp.ne.simplex.application.controller.external.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.external.parking.dto.ExternalReservationType.Companion.toReservationType
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ParkingReservation.RequestSource.Companion.toRequestSource
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.isAllCharsFullWidthConvertible
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeParseException

data class ExternalUpdateParkingReservationRequest(

    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード", example = "000000001")
    val buildingCode: String,

    @JsonProperty("parkingCode")
    @field:Schema(description = "駐車場コード", example = "001")
    val parkingCode: String,

    @JsonProperty("reservationStatus")
    @field:Schema(description = "予約状態", example = "1")
    val reservationStatus: Int,

    @JsonProperty("reservationType")
    @field:Schema(description = "予約種別", example = "0")
    val reservationType: Int,

    @JsonProperty("reserveStartDate")
    @field:Schema(description = "予約開始日", example = "20250101")
    val reserveStartDate: String?,

    @JsonProperty("reserveEndDate")
    @field:Schema(description = "予約終了日", example = "20250102")
    val reserveEndDate: String?,

    @JsonProperty("receptionStaff")
    @field:Schema(description = "受付担当者", example = "シンプレクス受付")
    val receptionStaff: String?,

    @JsonProperty("reserverName")
    @field:Schema(description = "利用者氏名", example = "シンプレクス予約")
    val reserverName: String?,

    @JsonProperty("reserverTel")
    @field:Schema(description = "利用者電話番号", example = "090-1234-5678")
    val reserverTel: String?,

    @JsonProperty("linkedBuildingCode")
    @field:Schema(description = "同時に仮押さえした物件の建物コード", example = "000000001")
    val linkedBuildingCode: String?,

    @JsonProperty("linkedRoomCode")
    @field:Schema(description = "同時に仮押さえした物件の部屋コード", example = "00001")
    val linkedRoomCode: String?,

    @JsonProperty("remarks")
    @field:Schema(description = "備考", example = "備考")
    val remarks: String?,

    ) {

    companion object {
        private const val NAME_MAX_LENGTH = 42
    }

    fun toServiceInterface(apiKey: AuthInfo.ApiKey): ParkingReservationAction {

        val parkingReservationStatus =
            ParkingReservation.Status.fromValue(reservationStatus.toString())

        // 申込以外は受け付けない
        if (reservationType != ExternalReservationType.APPLICATION.value) {
            throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format())
        }

        // 氏名の長さチェック
        if ((receptionStaff != null && receptionStaff.length > NAME_MAX_LENGTH)
            || (reserverName != null && reserverName.length > NAME_MAX_LENGTH)
        ) {
            throw ClientValidationException(
                ErrorMessage.STRING_MAX_LENGTH.format("氏名", NAME_MAX_LENGTH)
            )
        }

        // 氏名の文字列チェック(全角or全角に変換できるもののみ受け付ける)
        if ((receptionStaff != null && !receptionStaff.isAllCharsFullWidthConvertible())
            || (reserverName != null && !reserverName.isAllCharsFullWidthConvertible())
        ) {
            throw ClientValidationException(ErrorMessage.INVALID_CHAR.format("氏名"))
        }

        // 予約メモの文字列チェック(全角or全角に変換できるもののみ受け付ける)
        if (remarks != null && !remarks.isAllCharsFullWidthConvertible()) {
            throw ClientValidationException(ErrorMessage.INVALID_CHAR.format("予約メモ"))
        }

        try {
            return when (parkingReservationStatus) {
                ParkingReservation.Status.RESERVATION -> toRegisterReservation(apiKey)
                ParkingReservation.Status.CANCEL -> toCancelReservation()
                else -> throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format())
            }
        } catch (e: ClientValidationException) {
            throw e
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }

    private fun toRegisterReservation(apiKey: AuthInfo.ApiKey): RegisterParkingReservation {
        if (reserveStartDate == null) {
            throw ClientValidationException(
                ErrorMessage.PARKING_RESERVATION_START_DATE_REQUIRED.format("申込")
            )
        }
        if (reserveEndDate != null) {
            throw ClientValidationException(
                ErrorMessage.PARKING_RESERVATION_END_DATE_NOT_ALLOWED.format("申込")
            )
        }
        val startDate: LocalDate
        try {
            startDate = reserveStartDate.yyyyMMdd()
        } catch (_: DateTimeParseException) {
            throw ClientValidationException(ErrorMessage.INVALID_DATE_FORMAT.format("yyyyMMdd"))
        }
        return RegisterParkingReservation.of(
            parkingLotId = ParkingLot.Id(
                Building.Code.of(buildingCode),
                ParkingLot.Code.of(parkingCode)
            ),
            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
            reservationType = ExternalReservationType.fromValue(reservationType)
                ?.toReservationType()!!,
            reserveStartDatetime = startDate.atTime(LocalTime.MIN),
            reserveEndDatetime = null,
            receptionStaff = receptionStaff,
            reserverName = reserverName,
            reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
            requestSource = apiKey.externalSystem.toRequestSource(),
            linkedBuildingCode = linkedBuildingCode?.let { Building.Code.of(linkedBuildingCode) },
            linkedRoomCode = linkedRoomCode?.let { Room.Code.of(linkedRoomCode) },
            remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
        )
    }

    private fun toCancelReservation(): CancelApplicationParkingReservation {
        return CancelApplicationParkingReservation(
            parkingLotId = ParkingLot.Id(
                Building.Code.of(buildingCode),
                ParkingLot.Code.of(parkingCode)
            ),
            status = ParkingReservation.Status.CANCEL,
            reservationType = ExternalReservationType.fromValue(reservationType)
                ?.toReservationType()!!,
            remarks = remarks?.let { ParkingReservation.Remarks.of(it) },
        )
    }
}
