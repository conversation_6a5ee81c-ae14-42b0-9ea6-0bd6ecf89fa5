package jp.ne.simplex.application.model

/** 「駐車場車種情報更新」を操作する際の Class */
class ParkingVehicleInfo(
    // 対象のテナント契約番号
    val tenantContractNumber: TenantContract.Number,
    // 部屋コード
    val roomCd: Room.Code?,
    // 駐車場区分
    val parkingLotCategory: ParkingLot.Category?,
    // 車種ナンバー
    val vehicleNumber: Number,
    // 車種
    val vehicleCategory: Category,
    // 車庫証明発給サイン
    val parkingCertIssueSign: ParkingCertIssueSign?,
    // 車庫証明コメント
    val parkingCertComment: String?,

    ) {
    data class Number(
        // 陸事名
        val landTransportName: String?,
        // 種別
        val type: String?,
        // 業態
        val businessCategory: String?,
        // 左ナンバー
        val leftNumber: String?,
        // 右ナンバー
        val rightNumber: String?,
    )

    data class Category(
        // メーカー区分
        val manufacturerDivision: ManufacturerDivision?,
        // 車種名
        val carModelName: String?,
    ) {
        enum class ManufacturerDivision(val byte: Byte) {
            NONE(0), // 未設定
            TOYOTA(1), // トヨタ
            NISSAN(2), // 日産
            HONDA(3), // ホンダ
            MAZDA(4), // マツダ
            MITSUBISHI(5), // 三菱
            SUBARU(6), // スバル
            DAIHATSU(7), // ダイハツ
            SUZUKI(8), // スズキ
            ISUZU(9), // いすず
            HINO(10), // 日野
            MITSUOKA(11), // 光岡
            IMPORT(12), // 輸入車
            OTHER(13); // その他
        }
    }

    enum class ParkingCertIssueSign(val byte: Byte) {
        NONE(0), // 未設定
        ISSUED(1), // 発給
    }
}
