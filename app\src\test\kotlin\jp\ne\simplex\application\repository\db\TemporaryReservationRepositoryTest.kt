package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.TemporaryReservationInfo
import jp.ne.simplex.db.jooq.gen.tables.pojos.TemporaryReservationFilePojo
import jp.ne.simplex.db.jooq.gen.tables.references.TEMPORARY_RESERVATION_FILE
import jp.ne.simplex.exception.DBValidationException
import jp.ne.simplex.mock.MockBranchRepository
import jp.ne.simplex.mock.MockEmployeeRepository
import jp.ne.simplex.mock.MockLocalDateTime
import jp.ne.simplex.shared.AssertionHelper.Companion.assertNotNullProperties
import jp.ne.simplex.shared.AssertionHelper.Companion.assertNullProperties
import jp.ne.simplex.shared.CoroutineHelper.Companion.runAsyncTasks
import jp.ne.simplex.shared.DSLContextEx.Companion.countTemporaryReservationsBy
import jp.ne.simplex.shared.DSLContextEx.Companion.selectTemporaryReservationBy
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.*
import org.jooq.Configuration
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import java.time.LocalDate
import java.time.LocalDateTime

class TemporaryReservationRepositoryTest : AbstractTestContainerTest() {

    private val branchRepository = MockBranchRepository()

    private val employeeRepository = MockEmployeeRepository()

    private lateinit var repository: TemporaryReservationRepository

    private val currentDateTime = LocalDateTime.of(2024, 11, 12, 13, 45, 30)

    override fun beforeEach() {
        MockLocalDateTime.setNow(currentDateTime)

        repository =
            TemporaryReservationRepository(
                dslContext,
                MockBranchRepository(),
                MockEmployeeRepository()
            )
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(TEMPORARY_RESERVATION_FILE)
    }

    @AfterEach
    fun tearDown() {
        MockLocalDateTime.close()
    }

    @Nested
    @DisplayName("仮押さえ登録（自社）")
    inner class Scenario1 {

        @Nested
        @DisplayName("対象の物件レコードが存在しない（更新日/更新時刻がNULL）場合")
        inner class Scenario1x1 {

            @Test
            @DisplayName("他社情報関連のカラム及び、未使用のカラム以外は、リクエスト値が設定されたレコードが作成されること")
            fun case1() {
                // setup
                val updateData = stubOwnCompanyRegisterTemporaryReservation(
                    buildingCode = "*********",
                    roomCode = "01010",
                    scheduledMoveInDate = LocalDate.of(2024, 12, 19),
                    assignedEmployeeCode = "000011",
                    assignedBranchCode = "965000",
                    comment = "仮押さえコメントはこちら",
                    updateDate = null,
                    updateTime = null,
                )

                assert(dslContext.countTemporaryReservationsBy(updateData.getId()) == 0)

                // execute
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                assertEquals(updateData.getId().buildingCode.value, record.buildingCd)
                assertEquals(updateData.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals("20241219", record.applicationScheduledDate)
                assertEquals("000011", record.applicationScheduledPersonCd)
                assertEquals(updateData.assignedBranch.code.getPrefix(), record.customerRepBranchCd)
                assertEquals(updateData.comment.value, record.comment)
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45, 30) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.registrationDate)
                assertEquals("134530", record.linkCdRegistrationTime)

                // 未使用カラムについては、更新しないので、NULLのまま
                // また、他社情報は、別APIでのみ更新される想定のため、ここでは更新されないこと（NULLのまま）を確認する
                assertNullProperties(
                    record,
                    listOf(
                        "state", // 未使用カラム
                        "customerRepCd", // 未使用カラム
                        "customerRepShozokuCd", // 未使用カラム
                        "listComment", // 未使用カラム（いい物件では使用してるが、DKリンクで使用しない）
                        "contractFormECode", // 他社情報関連カラム
                        "otherCompanyFlag", // 他社情報関連カラム
                        "otherCompanyMemberId", // 他社情報関連カラム
                        "otherCompanyName", // 他社情報関連カラム
                        "otherCompanyStoreName", // 他社情報関連カラム
                        "otherCompanyRepName", // 他社情報関連カラム
                    )
                )
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の処理が失敗すること")
            fun case2() {
                // 同時リクエスト実現させるため、lockRecordの結果を常に NULL を返すようにする
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordNoWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            return null
                        }
                    }

                val targetBuildingCode = "*********"
                val targetRoomCode = "01010"

                val firstRequest = stubOwnCompanyRegisterTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    assignedEmployeeCode = "000011",
                )
                val secondRequest = stubOwnCompanyRegisterTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    assignedEmployeeCode = "000009",
                )

                // task1の実行後1秒後に、task2を実行する
                // task1は成功するが、task2は重複チェックで失敗する
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.register(config, firstRequest)
                            }
                        }
                    },
                    task2 = {
                        assertThrows<DBValidationException> {
                            dslContext.transaction { config ->
                                repository.register(config, secondRequest)
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )

                // verify
                val sqlResult =
                    dslContext.selectTemporaryReservationBy(targetBuildingCode, targetRoomCode)

                assertEquals(1, sqlResult.size)

                // リクエスト①の内容でレコードが作成されていることを確認
                assertEquals("000011", sqlResult.first().applicationScheduledPersonCd)
            }
        }

        @Nested
        @DisplayName("対象の物件レコードが存在する場合")
        inner class Scenario1x2 {

            val existsData = stubTemporaryReservationFilePojo(
                buildingCode = "*********",
                roomCode = "02020",
                applicationScheduledDate = "20241219",
                applicationScheduledPersonCd = "000011",
                customerRepBranchCd = "731",
                comment = "ここに仮押さえコメントが入ります",
                contractFormECode = "E541",
                registrationDate = "20250206",
                linkCdRegistrationTime = "124912",
                otherCompanyFlag = "1",
                otherCompanyMemberId = "03253",
                otherCompanyName = "〇〇株式会社",
                otherCompanyStoreName = "東京銀座店",
            )

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.saveTemporaryReservationFilePojo(existsData)
            }

            @Test
            @DisplayName("他社情報関連のカラム及び、未使用のカラム以外は、リクエスト値でレコードが更新されること")
            fun case1() {
                // setup
                val updateData = stubOwnCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = LocalDate.of(2025, 1, 20),
                    assignedEmployeeCode = "000009",
                    assignedBranchCode = "854000",
                    comment = "仮押さえコメントはこちら",
                    updateDate = existsData.registrationDate,
                    updateTime = existsData.linkCdRegistrationTime,
                )

                // execute
                // 画面表示しているデータが最新の状態（リクエストに含まれる更新日時の情報が更新対象のレコードと一致している）の場合は、更新に成功する
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                assertEquals(updateData.getId().buildingCode.value, record.buildingCd)
                assertEquals(updateData.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals("20250120", record.applicationScheduledDate)
                assertEquals("000009", record.applicationScheduledPersonCd)
                assertEquals(updateData.assignedBranch.code.getPrefix(), record.customerRepBranchCd)
                assertEquals(updateData.comment.value, record.comment)
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.registrationDate)
                assertEquals("134530", record.linkCdRegistrationTime)

                // 他社情報や未使用カラムの情報は更新されていないこと
                assertEquals(existsData.state, record.state)
                assertEquals(existsData.customerRepCd, record.customerRepCd)
                assertEquals(existsData.customerRepShozokuCd, record.customerRepShozokuCd)
                assertEquals(existsData.listComment, record.listComment)
                assertEquals(existsData.contractFormECode, record.contractFormECode)
                assertEquals(existsData.otherCompanyFlag, record.otherCompanyFlag)
                assertEquals(existsData.otherCompanyMemberId, record.otherCompanyMemberId)
                assertEquals(existsData.otherCompanyName, record.otherCompanyName)
                assertEquals(existsData.otherCompanyStoreName, record.otherCompanyStoreName)
                assertEquals(existsData.otherCompanyRepName, record.otherCompanyRepName)
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の処理が失敗すること")
            fun case2() {
                // レコードロックの時間を調整するために、プロダクションのRepositoryを継承し、
                // lockRecord の処理を従来の処理 + 遅延処理で overrideしている
                // また、バージョンチェックで弾かれないように、以下のtask1とtask2の遅延時間（1000ms）以上待たせる
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordNoWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            val result = super.lockRecordNoWait(config, id)
                            Thread.sleep(3000)
                            return result
                        }
                    }

                val targetBuildingCode = existsData.buildingCd!!
                val targetRoomCode = existsData.roomCd!!

                // task1の実行後1秒後に、task2を実行する
                // task1は成功するが、task2はレコードロックで失敗する
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.register(
                                    config,
                                    stubOwnCompanyRegisterTemporaryReservation(
                                        buildingCode = targetBuildingCode,
                                        roomCode = targetRoomCode,
                                        assignedEmployeeCode = "000009",
                                        updateDate = existsData.registrationDate,
                                        updateTime = existsData.linkCdRegistrationTime,
                                    )
                                )
                            }
                        }
                    },
                    task2 = {
                        assertThrows<DBValidationException> {
                            dslContext.transaction { config ->
                                repository.register(
                                    config,
                                    stubOwnCompanyRegisterTemporaryReservation(
                                        buildingCode = targetBuildingCode,
                                        roomCode = targetRoomCode,
                                        assignedEmployeeCode = "000011",
                                        updateDate = existsData.registrationDate,
                                        updateTime = existsData.linkCdRegistrationTime,
                                    )
                                )
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )

                // verify
                val sqlResult =
                    dslContext.selectTemporaryReservationBy(targetBuildingCode, targetRoomCode)

                assertEquals(1, sqlResult.size)

                // リクエスト①の内容でレコードが作成されていることを確認
                assertEquals("000009", sqlResult.first().applicationScheduledPersonCd)
            }

            @Test
            @DisplayName("バージョン情報が異なる場合、更新に失敗すること")
            fun case3() {
                // setup
                val updateData = stubOwnCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    updateDate = existsData.registrationDate + "1",
                    updateTime = existsData.linkCdRegistrationTime + "1",
                )

                // execute & verify
                // 既にデータは更新され、画面表示しているデータが古い状態で作成されたリクエストによる更新は失敗する
                assertThrows<DBValidationException> {
                    dslContext.transaction { config -> repository.register(config, updateData) }
                }
            }
        }
    }

    @Nested
    @DisplayName("仮押さえ登録（他社）")
    inner class Scenario2 {

        @Nested
        @DisplayName("対象の物件レコードが存在しない（業務上起こり得ない）場合")
        inner class Scenario2x1 {

            @Test
            @DisplayName("業務上あり得ないので、DBへの永続化処理は行われないこと")
            fun case1() {
                // setup
                val updateData = stubOtherCompanyRegisterTemporaryReservation()

                // execute
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                assertEquals(0, dslContext.countTemporaryReservationsBy(updateData.getId()))
            }
        }

        @Nested
        @DisplayName("対象の物件レコードが存在する場合")
        inner class Scenario2x2 {

            val existsData = stubTemporaryReservationFilePojo(
                buildingCode = "*********",
                roomCode = "02020",
                applicationScheduledDate = "20241219",
                applicationScheduledPersonCd = "000011",
                customerRepBranchCd = "731",
                comment = "ここに仮押さえコメントが入ります",
                contractFormECode = "E541",
                registrationDate = "20250206",
                linkCdRegistrationTime = "124912",
                otherCompanyFlag = "1",
                otherCompanyName = "〇〇株式会社",
                otherCompanyStoreName = "東京銀座店",
                otherCompanyRepName = "田中太郎"
            )

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.saveTemporaryReservationFilePojo(existsData)
            }

            @Test
            @DisplayName("コメントと更新日時のみが更新されること")
            fun case1() {
                // setup
                val updateData = stubOtherCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = existsData.applicationScheduledDate!!.yyyyMMdd(),
                    otherCompanyCode = existsData.contractFormECode!!,
                    otherCompanyName = existsData.otherCompanyName!!,
                    otherCompanyStoreName = existsData.otherCompanyStoreName!!,
                    otherCompanyStaffName = existsData.otherCompanyRepName!!,
                    comment = "仮押さえコメントを更新しました。",
                    updateDate = existsData.registrationDate!!,
                    updateTime = existsData.linkCdRegistrationTime!!,
                )

                // execute
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                assertNotEquals(existsData.comment, record.comment)
                assertEquals(updateData.comment.value, record.comment)
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.registrationDate)
                assertEquals("134530", record.linkCdRegistrationTime)

                // コメントと更新日時以外は更新されない
                assertEquals(existsData.state, record.state)
                assertEquals(existsData.status!!, record.status)
                assertEquals(existsData.applicationScheduledDate!!, record.applicationScheduledDate)
                assertEquals(
                    existsData.applicationScheduledPersonCd!!,
                    record.applicationScheduledPersonCd
                )
                assertEquals(existsData.customerRepBranchCd!!, record.customerRepBranchCd)
                assertEquals(existsData.customerRepCd, record.customerRepCd)
                assertEquals(existsData.customerRepShozokuCd, record.customerRepShozokuCd)
                assertEquals(existsData.listComment, record.listComment)
                assertEquals(existsData.contractFormECode, record.contractFormECode)
                assertEquals(existsData.otherCompanyFlag, record.otherCompanyFlag)
                assertEquals(existsData.otherCompanyMemberId, record.otherCompanyMemberId)
                assertEquals(existsData.otherCompanyName, record.otherCompanyName)
                assertEquals(existsData.otherCompanyStoreName, record.otherCompanyStoreName)
                assertEquals(existsData.otherCompanyRepName, record.otherCompanyRepName)
            }

            @Test
            @DisplayName("リクエストの入居予定日が一致していない場合は、更新されないこと")
            fun case2() {
                // setup
                val updateData = stubOtherCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = existsData.applicationScheduledDate!!.yyyyMMdd()
                        .plusDays(1),
                    otherCompanyCode = existsData.contractFormECode!!,
                    otherCompanyName = existsData.otherCompanyName!!,
                    otherCompanyStoreName = existsData.otherCompanyStoreName!!,
                    otherCompanyStaffName = existsData.otherCompanyRepName!!,
                    comment = "仮押さえコメントを更新しました。",
                    updateDate = existsData.registrationDate!!,
                    updateTime = existsData.linkCdRegistrationTime!!,
                )

                // execute
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()

                assertEquals(existsData.comment, record.comment)
                assertEquals(existsData.registrationDate, record.registrationDate)
                assertEquals(existsData.linkCdRegistrationTime, record.linkCdRegistrationTime)
            }

            @Test
            @DisplayName("リクエストの他社会社名が一致していない場合は、更新されないこと")
            fun case3() {
                // setup
                val updateData = stubOtherCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = existsData.applicationScheduledDate!!.yyyyMMdd(),
                    otherCompanyCode = existsData.contractFormECode!!,
                    otherCompanyName = existsData.otherCompanyName!! + "ここが違います",
                    otherCompanyStoreName = existsData.otherCompanyStoreName!!,
                    otherCompanyStaffName = existsData.otherCompanyRepName!!,
                    comment = "仮押さえコメントを更新しました。",
                    updateDate = existsData.registrationDate!!,
                    updateTime = existsData.linkCdRegistrationTime!!,
                )

                // execute
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()

                assertEquals(existsData.comment, record.comment)
                assertEquals(existsData.registrationDate, record.registrationDate)
                assertEquals(existsData.linkCdRegistrationTime, record.linkCdRegistrationTime)
            }

            @Test
            @DisplayName("リクエストの他社店舗名が一致していない場合は、更新されないこと")
            fun case4() {
                // setup
                val updateData = stubOtherCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = existsData.applicationScheduledDate!!.yyyyMMdd(),
                    otherCompanyCode = existsData.contractFormECode!!,
                    otherCompanyName = existsData.otherCompanyName!!,
                    otherCompanyStoreName = existsData.otherCompanyStoreName!! + "ここが違います",
                    otherCompanyStaffName = existsData.otherCompanyRepName!!,
                    comment = "仮押さえコメントを更新しました。",
                    updateDate = existsData.registrationDate!!,
                    updateTime = existsData.linkCdRegistrationTime!!,
                )

                // execute
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()

                assertEquals(existsData.comment, record.comment)
                assertEquals(existsData.registrationDate, record.registrationDate)
                assertEquals(existsData.linkCdRegistrationTime, record.linkCdRegistrationTime)
            }

            @Test
            @DisplayName("リクエストの他社担当者名が一致していない場合は、更新されないこと")
            fun case5() {
                // setup
                val updateData = stubOtherCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = existsData.applicationScheduledDate!!.yyyyMMdd(),
                    otherCompanyCode = existsData.contractFormECode!!,
                    otherCompanyName = existsData.otherCompanyName!!,
                    otherCompanyStoreName = existsData.otherCompanyStoreName!!,
                    otherCompanyStaffName = existsData.otherCompanyRepName!! + "ここが違います",
                    comment = "仮押さえコメントを更新しました。",
                    updateDate = existsData.registrationDate!!,
                    updateTime = existsData.linkCdRegistrationTime!!,
                )

                // execute
                dslContext.transaction { config -> repository.register(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()

                assertEquals(existsData.comment, record.comment)
                assertEquals(existsData.registrationDate, record.registrationDate)
                assertEquals(existsData.linkCdRegistrationTime, record.linkCdRegistrationTime)
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の処理が失敗すること")
            fun case6() {
                // レコードロックの時間を調整するために、プロダクションのRepositoryを継承し、
                // lockRecord の処理を従来の処理 + 遅延処理で overrideしている
                // また、バージョンチェックで弾かれないように、以下のtask1とtask2の遅延時間（1000ms）以上待たせる
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordNoWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            val result = super.lockRecordNoWait(config, id)
                            Thread.sleep(3000)
                            return result
                        }
                    }

                val targetBuildingCode = existsData.buildingCd!!
                val targetRoomCode = existsData.roomCd!!

                // task1の実行後1秒後に、task2を実行する
                // task1は成功するが、task2はレコードロックで失敗する
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.register(
                                    config,
                                    stubOtherCompanyRegisterTemporaryReservation(
                                        buildingCode = targetBuildingCode,
                                        roomCode = targetRoomCode,
                                        comment = "コメント一個目",
                                        otherCompanyCode = existsData.contractFormECode!!,
                                        otherCompanyName = existsData.otherCompanyName!!,
                                        otherCompanyStoreName = existsData.otherCompanyStoreName!!,
                                        otherCompanyStaffName = existsData.otherCompanyRepName!!,
                                        updateDate = existsData.registrationDate!!,
                                        updateTime = existsData.linkCdRegistrationTime!!,
                                    )
                                )
                            }
                        }
                    },
                    task2 = {
                        assertThrows<DBValidationException> {
                            dslContext.transaction { config ->
                                repository.register(
                                    config,
                                    stubOtherCompanyRegisterTemporaryReservation(
                                        buildingCode = targetBuildingCode,
                                        roomCode = targetRoomCode,
                                        comment = "コメント二個目",
                                        otherCompanyCode = existsData.contractFormECode!!,
                                        otherCompanyName = existsData.otherCompanyName!!,
                                        otherCompanyStoreName = existsData.otherCompanyStoreName!!,
                                        otherCompanyStaffName = existsData.otherCompanyRepName!!,
                                        updateDate = existsData.registrationDate!!,
                                        updateTime = existsData.linkCdRegistrationTime!!,
                                    )
                                )
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )

                // verify
                val sqlResult =
                    dslContext.selectTemporaryReservationBy(targetBuildingCode, targetRoomCode)

                assertEquals(1, sqlResult.size)

                // リクエスト①の内容でレコードが作成されていることを確認
                assertEquals("コメント一個目", sqlResult.first().comment)
            }

            @Test
            @DisplayName("バージョン情報が異なる場合、更新に失敗すること")
            fun case7() {
                // setup
                val updateData = stubOtherCompanyRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    comment = "コメント一個目",
                    otherCompanyName = existsData.otherCompanyName!!,
                    otherCompanyStoreName = existsData.otherCompanyStoreName!!,
                    otherCompanyStaffName = existsData.otherCompanyRepName!!,
                    updateDate = existsData.registrationDate!! + 1,
                    updateTime = existsData.linkCdRegistrationTime!! + 1,
                )

                // execute & verify
                // 既にデータは更新され、画面表示しているデータが古い状態で作成されたリクエストによる更新は失敗する
                assertThrows<DBValidationException> {
                    dslContext.transaction { config -> repository.register(config, updateData) }
                }
            }
        }
    }

    @Nested
    @DisplayName("仮押さえ解除")
    inner class Scenario3 {

        @Nested
        @DisplayName("対象の物件レコードが存在しない（更新日/更新時刻がNULL）場合")
        inner class Scenario2x1 {

            @Test
            @DisplayName("建物CD,部屋CD,ステータス,コメントのみが設定されたレコードが作成されること")
            fun case1() {
                // setup
                val updateData = stubCancelTemporaryReservation(
                    comment = "仮押さえ解除コメントはこちら",
                    updateTime = null,
                    updateDate = null,
                )

                assert(dslContext.countTemporaryReservationsBy(updateData.getId()) == 0)

                // execute
                var result: TemporaryReservationInfo? = null
                dslContext.transaction { config -> result = repository.cancel(config, updateData) }

                // verify
                assertNull(result)
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                assertEquals(updateData.getId().buildingCode.value, record.buildingCd)
                assertEquals(updateData.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals(updateData.comment.value, record.comment)
                assertNotNullProperties(
                    record,
                    listOf("buildingCd", "roomCd", "status", "comment")
                )
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の処理が失敗すること")
            fun case2() {
                // 同時リクエスト実現させるため、lockRecordの結果を常に NULL を返すようにする
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordNoWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            return null
                        }
                    }

                val targetBuildingCode = "*********"
                val targetRoomCode = "01010"

                val firstRequest = stubCancelTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    comment = "仮押さえ解除コメントはこちら①",
                    updateTime = null,
                    updateDate = null,
                )
                val secondRequest = stubCancelTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    comment = "仮押さえ解除コメントはこちら②",
                    updateTime = null,
                    updateDate = null,
                )

                var result: TemporaryReservationInfo? = null
                // task1の実行後1秒後に、task2を実行する
                // task1は成功するが、task2は重複チェックで失敗する
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                result = repository.cancel(config, firstRequest)
                            }
                        }
                    },
                    task2 = {
                        assertThrows<DBValidationException> {
                            dslContext.transaction { config ->
                                repository.cancel(config, secondRequest)
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )
                // verify
                assertNull(result)
                val sqlResult =
                    dslContext.selectTemporaryReservationBy(targetBuildingCode, targetRoomCode)

                assertEquals(1, sqlResult.size)

                // リクエスト①の内容でレコードが作成されていることを確認
                assertEquals(firstRequest.comment.value, sqlResult.first().comment)
            }
        }

        @Nested
        @DisplayName("対象の物件レコードが存在する場合")
        inner class Scenario2x2 {

            @Test
            @DisplayName("建物CD,部屋CD,ステータス,コメント,未使用のカラム以外は「NULL」で更新され、更新前のレコードを取得できること")
            fun case1() {
                // setup
                val dbData = stubTemporaryReservationFilePojo(
                    applicationScheduledDate = "20241219",
                    comment = "ここに仮押さえコメントが入ります",
                    registrationDate = "20250206",
                    linkCdRegistrationTime = "124912",
                    otherCompanyFlag = "1",
                    contractFormECode = "E12345678",
                    otherCompanyName = "〇〇株式会社",
                    otherCompanyStoreName = "東京銀座店",
                    otherCompanyRepName = "テスト太郎"
                )
                dslContext.saveTemporaryReservationFilePojo(dbData)

                val cancelReq = stubCancelTemporaryReservation(
                    buildingCode = dbData.buildingCd!!,
                    roomCode = dbData.roomCd!!,
                    comment = "〇〇のため、この仮おさえを解除します",
                    updateDate = dbData.registrationDate!!,
                    updateTime = dbData.linkCdRegistrationTime!!,
                )

                // execute
                var result: TemporaryReservationInfo? = null
                // 画面表示しているデータが最新の状態（リクエストに含まれる更新日時の情報が更新対象のレコードと一致している）の場合は、更新に成功する
                dslContext.transaction { config -> result = repository.cancel(config, cancelReq) }

                // verify function output
                val data =
                    result as TemporaryReservationInfo.OtherCompanyTemporaryReservationInfo
                assertEquals(dbData.applicationScheduledDate, data.scheduledMoveInDate.yyyyMMdd())
                assertEquals(dbData.contractFormECode, data.otherCompanyInfo.companyCode)
                assertEquals(dbData.otherCompanyName, data.otherCompanyInfo.companyName)
                assertEquals(dbData.otherCompanyStoreName, data.otherCompanyInfo.storeName)
                assertEquals(dbData.otherCompanyRepName, data.otherCompanyInfo.staffName)
                assertEquals(dbData.comment, data.comment.value)

                // verify database record
                val sqlResult = dslContext.selectTemporaryReservationBy(cancelReq.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                assertEquals(cancelReq.getId().buildingCode.value, record.buildingCd)
                assertEquals(cancelReq.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals(cancelReq.comment.value, record.comment)
                assertNotNullProperties(
                    record,
                    listOf(
                        "buildingCd",
                        "roomCd",
                        "status",
                        "comment",
                        "listComment",
                        "customerRepCd", // ← 未使用カラム
                        "customerRepShozokuCd", // ← 未使用カラム
                    )
                )
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の処理が失敗すること")
            fun case2() {
                // レコードロックの時間を調整するために、プロダクションのRepositoryを継承し、
                // lockRecord の処理を従来の処理 + 遅延処理で overrideしている
                // また、バージョンチェックで弾かれないように、以下のtask1とtask2の遅延時間（1000ms）以上待たせる
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordNoWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            val result = super.lockRecordNoWait(config, id)
                            Thread.sleep(3000)
                            return result
                        }
                    }

                // setup
                val dbData = stubTemporaryReservationFilePojo(
                    applicationScheduledDate = "20241219",
                    applicationScheduledPersonCd = "000011",
                    customerRepBranchCd = "731",
                    registrationDate = "20250206",
                    linkCdRegistrationTime = "124912",
                    otherCompanyFlag = null,
                )
                dslContext.saveTemporaryReservationFilePojo(dbData)

                val cancelReq = stubCancelTemporaryReservation(
                    buildingCode = dbData.buildingCd!!,
                    roomCode = dbData.roomCd!!,
                    comment = "〇〇のため、この仮おさえを解除します",
                    updateDate = dbData.registrationDate!!,
                    updateTime = dbData.linkCdRegistrationTime!!,
                )

                var result: TemporaryReservationInfo? = null
                // task1の実行後1秒後に、task2を実行する
                // task1は成功するが、task2はレコードロックで失敗する
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                result = repository.cancel(config, cancelReq)
                            }
                        }
                    },
                    task2 = {
                        assertThrows<DBValidationException> {
                            dslContext.transaction { config ->
                                repository.cancel(config, cancelReq)
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )

                // verify
                val data =
                    result as TemporaryReservationInfo.OwnCompanyTemporaryReservationInfo
                assertEquals(dbData.applicationScheduledDate, data.scheduledMoveInDate.yyyyMMdd())
                assertEquals(dbData.customerRepBranchCd, data.assignedBranch.code.getPrefix())
                assertEquals(dbData.applicationScheduledPersonCd, data.assignedEmployee.code.value)

            }

            @Test
            @DisplayName("バージョン情報が異なる場合、更新に失敗すること")
            fun case3() {
                // setup
                val dbData = stubTemporaryReservationFilePojo(
                    registrationDate = "20250206",
                    linkCdRegistrationTime = "124912",
                )
                dslContext.saveTemporaryReservationFilePojo(dbData)

                val cancelReq = stubCancelTemporaryReservation(
                    buildingCode = dbData.buildingCd!!,
                    roomCode = dbData.roomCd!!,
                    updateDate = dbData.registrationDate!! + "1",
                    updateTime = dbData.linkCdRegistrationTime!! + "1",
                )

                // execute & verify
                // 既にデータは更新され、画面表示しているデータが古い状態で作成されたリクエストによる更新は失敗する
                assertThrows<DBValidationException> {
                    dslContext.transaction { config -> repository.cancel(config, cancelReq) }
                }
            }
        }
    }

    @Nested
    @DisplayName("仮押さえ強制解除")
    inner class Scenario4 {
        @Nested
        @DisplayName("対象の物件レコードが存在しない（更新日/更新時刻がNULL）場合")
        inner class Scenario3x1 {

            @Test
            @DisplayName("建物CD,部屋CD,ステータス,コメントのみが設定されたレコードが作成されること")
            fun case1() {
                // setup
                val req = stubForceCancelTemporaryReservation(
                    comment = "仮押さえ解除コメントはこちら",
                )

                assert(dslContext.countTemporaryReservationsBy(req.getId()) == 0)

                // execute
                dslContext.transaction { config -> repository.forceCancel(config, req) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(req.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                assertEquals(req.getId().buildingCode.value, record.buildingCd)
                assertEquals(req.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals(req.comment.value, record.comment)
                assertNotNullProperties(
                    record,
                    listOf("buildingCd", "roomCd", "status", "comment")
                )
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の処理が失敗すること")
            fun case2() {
                // 同時リクエスト実現させるため、lockRecordの結果を常に NULL を返すようにする
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            return null
                        }
                    }

                val targetBuildingCode = "*********"
                val targetRoomCode = "01010"

                val firstReq = stubForceCancelTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    comment = "仮押さえ解除コメントはこちら①",
                )
                val secondReq = stubForceCancelTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    comment = "仮押さえ解除コメントはこちら②",
                )

                // task1の実行後1秒後に、task2を実行する
                // task1は成功するが、task2は重複チェックで失敗する
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.forceCancel(config, firstReq)
                            }
                        }
                    },
                    task2 = {
                        assertThrows<DBValidationException> {
                            dslContext.transaction { config ->
                                repository.forceCancel(config, secondReq)
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )
                // verify
                val sqlResult =
                    dslContext.selectTemporaryReservationBy(targetBuildingCode, targetRoomCode)

                assertEquals(1, sqlResult.size)

                // リクエスト①の内容でレコードが作成されていることを確認
                assertEquals(firstReq.comment.value, sqlResult.first().comment)
            }
        }

        @Nested
        @DisplayName("対象の物件レコードが存在する場合")
        inner class Scenario3x2 {

            @Test
            @DisplayName("建物CD,部屋CD,ステータス,コメント,未使用のカラム以外は「NULL」で更新され、更新前のレコードを取得できること")
            fun case1() {
                // setup
                val dbData = stubTemporaryReservationFilePojo(
                    applicationScheduledDate = "20241219",
                    comment = "ここに仮押さえコメントが入ります",
                    registrationDate = "20250206",
                    linkCdRegistrationTime = "124912",
                    otherCompanyFlag = "1",
                    otherCompanyMemberId = "03253",
                    otherCompanyName = "〇〇株式会社",
                    otherCompanyStoreName = "東京銀座店",
                    otherCompanyRepName = "テスト太郎"
                )
                dslContext.saveTemporaryReservationFilePojo(dbData)

                val cancelReq = stubForceCancelTemporaryReservation(
                    buildingCode = dbData.buildingCd!!,
                    roomCode = dbData.roomCd!!,
                    comment = "〇〇のため、この仮おさえを解除します",
                )

                // execute
                // 画面表示しているデータが最新の状態（リクエストに含まれる更新日時の情報が更新対象のレコードと一致している）の場合は、更新に成功する
                dslContext.transaction { config -> repository.forceCancel(config, cancelReq) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(cancelReq.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                assertEquals(cancelReq.getId().buildingCode.value, record.buildingCd)
                assertEquals(cancelReq.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals(cancelReq.comment.value, record.comment)
                assertNotNullProperties(
                    record,
                    listOf(
                        "buildingCd",
                        "roomCd",
                        "status",
                        "comment",
                        "listComment",
                        "customerRepCd", // ← 未使用カラム
                        "customerRepShozokuCd", // ← 未使用カラム
                    )
                )
            }

            @Test
            @DisplayName("同時リクエストが行われた場合でも、最新のリクエストで更新されること")
            fun case2() {
                // レコードロックの時間を調整するために、プロダクションのRepositoryを継承し、
                // lockRecord の処理を従来の処理 + 遅延処理で overrideしている
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            val result = super.lockRecordWait(config, id)
                            Thread.sleep(3000)
                            return result
                        }
                    }

                // setup
                val dbData = stubTemporaryReservationFilePojo(
                    applicationScheduledDate = "20241219",
                    applicationScheduledPersonCd = "000011",
                    customerRepBranchCd = "731",
                    registrationDate = "20250206",
                    linkCdRegistrationTime = "124912",
                    otherCompanyFlag = null,
                )
                dslContext.saveTemporaryReservationFilePojo(dbData)

                val firstReq = stubForceCancelTemporaryReservation(
                    buildingCode = dbData.buildingCd!!,
                    roomCode = dbData.roomCd!!,
                    comment = "〇〇のため、この仮おさえを解除します①",
                )

                val secondReq = stubForceCancelTemporaryReservation(
                    buildingCode = dbData.buildingCd!!,
                    roomCode = dbData.roomCd!!,
                    comment = "〇〇のため、この仮おさえを解除します②",
                )

                // task1の実行後1秒後に、task2を実行する
                // task2は、task1のロック解除を待って更新処理が行われる
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.forceCancel(config, firstReq)
                            }
                        }
                    },
                    task2 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.forceCancel(config, secondReq)
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(secondReq.getId())

                assertEquals(1, sqlResult.size)

                // 最終的には、リクエスト②の値で更新されること
                val record = sqlResult.first()
                assertEquals(secondReq.getId().buildingCode.value, record.buildingCd)
                assertEquals(secondReq.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals(secondReq.comment.value, record.comment)
                assertNotNullProperties(
                    record,
                    listOf(
                        "buildingCd",
                        "roomCd",
                        "status",
                        "comment",
                        "listComment",
                        "customerRepCd", // ← 未使用カラム
                        "customerRepShozokuCd", // ← 未使用カラム
                    )
                )
            }
        }
    }

    @Nested
    @DisplayName("仮押さえ強制登録")
    inner class Scenario5 {

        @Nested
        @DisplayName("対象の物件レコードが存在しない（更新日/更新時刻がNULL）場合")
        inner class Scenario4x1 {

            @Test
            @DisplayName("自社による登録リクエストの場合、他社情報カラムや未使用のカラム以外は、リクエスト値が設定されたレコードが作成されること")
            fun case1() {
                // setup
                val updateData = stubOwnCompanyForceRegisterTemporaryReservation(
                    scheduledMoveInDate = LocalDate.of(2024, 12, 19),
                    comment = "仮押さえコメントはこちら",
                )

                assert(dslContext.countTemporaryReservationsBy(updateData.getId()) == 0)

                // execute
                dslContext.transaction { config -> repository.forceRegister(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                // @formatter:off
                assertEquals(updateData.getId().buildingCode.value, record.buildingCd)
                assertEquals(updateData.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals("20241219", record.applicationScheduledDate)
                assertEquals(updateData.assignedEmployeeCode!!.value, record.applicationScheduledPersonCd)
                assertEquals(updateData.assignedBranchCode!!.getPrefix(), record.customerRepBranchCd)
                assertEquals(updateData.comment.value, record.comment)
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45, 30) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.registrationDate)
                assertEquals("134530", record.linkCdRegistrationTime)
                // @formatter:on

                // 未使用カラムについては、更新しないので、NULLのまま
                // また、他社情報は、別APIでのみ更新される想定のため、ここでは更新されないこと（NULLのまま）を確認する
                assertNullProperties(
                    record,
                    listOf(
                        "state", // 未使用カラム
                        "customerRepCd", // 未使用カラム
                        "customerRepShozokuCd", // 未使用カラム
                        "listComment", // 未使用カラム（いい物件では使用してるが、DKリンクで使用しない）
                        "contractFormECode", // 他社情報関連カラム
                        "otherCompanyFlag", // 他社情報関連カラム
                        "otherCompanyMemberId", // 他社情報関連カラム
                        "otherCompanyName", // 他社情報関連カラム
                        "otherCompanyStoreName", // 他社情報関連カラム
                        "otherCompanyRepName", // 他社情報関連カラム
                    )
                )
            }

            @Test
            @DisplayName("他社による登録リクエストの場合、自社情報カラムや未使用のカラム以外は、リクエスト値が設定されたレコードが作成されること")
            fun case2() {
                // setup
                val updateData = stubOtherCompanyForceRegisterTemporaryReservation(
                    scheduledMoveInDate = LocalDate.of(2024, 12, 19),
                    comment = "仮押さえコメントはこちら",
                )

                assert(dslContext.countTemporaryReservationsBy(updateData.getId()) == 0)

                // execute
                dslContext.transaction { config -> repository.forceRegister(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()
                // @formatter:off
                assertEquals(updateData.getId().buildingCode.value, record.buildingCd)
                assertEquals(updateData.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals("20241219", record.applicationScheduledDate)
                assertEquals("1", record.otherCompanyFlag)
                assertEquals(updateData.otherCompanyInfo!!.companyCode, record.contractFormECode)
                assertEquals(updateData.otherCompanyInfo!!.companyName, record.otherCompanyName)
                assertEquals(updateData.otherCompanyInfo!!.storeName, record.otherCompanyStoreName)
                assertEquals(updateData.otherCompanyInfo!!.staffName, record.otherCompanyRepName)
                assertEquals(updateData.comment.value, record.comment)
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45, 30) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.registrationDate)
                assertEquals("134530", record.linkCdRegistrationTime)
                // @formatter:on

                // 未使用カラムについては、更新しないので、NULLのまま
                // また、他社情報は、別APIでのみ更新される想定のため、ここでは更新されないこと（NULLのまま）を確認する
                assertNullProperties(
                    record,
                    listOf(
                        "state", // 未使用カラム
                        "customerRepCd", // 未使用カラム
                        "customerRepShozokuCd", // 未使用カラム
                        "listComment", // 未使用カラム（いい物件では使用してるが、DKリンクで使用しない）
                        "applicationScheduledPersonCd", // 自社情報関連カラム
                        "customerRepBranchCd", // 自社情報関連カラム
                        "otherCompanyMemberId", // 他社関連だが、データソースがないため更新されない
                    )
                )
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、後からリクエストされた側の処理が失敗すること")
            fun case3() {
                // 同時リクエスト実現させるため、lockRecordの結果を常に NULL を返すようにする
                val repository =
                    object : TemporaryReservationRepository(
                        dslContext,
                        branchRepository,
                        employeeRepository
                    ) {
                        override fun lockRecordWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            return null
                        }
                    }

                val targetBuildingCode = "*********"
                val targetRoomCode = "01010"

                val firstRequest = stubOwnCompanyForceRegisterTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    assignedEmployeeCode = "000011",
                )
                val secondRequest = stubOwnCompanyForceRegisterTemporaryReservation(
                    buildingCode = targetBuildingCode,
                    roomCode = targetRoomCode,
                    assignedEmployeeCode = "000009",
                )

                // task1の実行後1秒後に、task2を実行する
                // task1は成功するが、task2は重複チェックで失敗する
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.forceRegister(config, firstRequest)
                            }
                        }
                    },
                    task2 = {
                        assertThrows<DBValidationException> {
                            dslContext.transaction { config ->
                                repository.forceRegister(config, secondRequest)
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )

                // verify
                val sqlResult =
                    dslContext.selectTemporaryReservationBy(targetBuildingCode, targetRoomCode)

                assertEquals(1, sqlResult.size)

                // リクエスト①の内容でレコードが作成されていることを確認
                assertEquals(
                    firstRequest.assignedEmployeeCode!!.value,
                    sqlResult.first().applicationScheduledPersonCd
                )
            }
        }

        @Nested
        @DisplayName("対象の物件レコードが存在する場合")
        inner class Scenario4x2 {

            val existsData = stubTemporaryReservationFilePojo(
                buildingCode = "*********",
                roomCode = "02020",
                applicationScheduledDate = "20241219",
                applicationScheduledPersonCd = "000011",
                customerRepBranchCd = "731",
                comment = "ここに仮押さえコメントが入ります",
                contractFormECode = "E541",
                registrationDate = "20250206",
                linkCdRegistrationTime = "124912",
                otherCompanyFlag = "1",
                otherCompanyMemberId = "03253",
                otherCompanyName = "〇〇株式会社",
                otherCompanyStoreName = "東京銀座店",
            )

            @BeforeEach
            fun setup() {
                // DBにデータをInsertしておく
                dslContext.saveTemporaryReservationFilePojo(existsData)
            }

            @Test
            @DisplayName("自社による登録リクエストの場合、未使用のカラム以外は、リクエスト値でレコードが更新されること")
            fun case1() {
                // setup
                val updateData = stubOwnCompanyForceRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = LocalDate.of(2024, 12, 19),
                    comment = "仮押さえコメントはこちら",
                )

                // execute
                dslContext.transaction { config -> repository.forceRegister(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()

                // @formatter:off
                assertEquals(updateData.getId().buildingCode.value, record.buildingCd)
                assertEquals(updateData.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals("20241219", record.applicationScheduledDate)
                assertEquals(updateData.assignedEmployeeCode!!.value, record.applicationScheduledPersonCd)
                assertEquals(updateData.assignedBranchCode!!.getPrefix(), record.customerRepBranchCd)
                assertEquals(null, record.contractFormECode)
                assertEquals(null, record.otherCompanyFlag)
                assertEquals(null, record.otherCompanyName)
                assertEquals(null, record.otherCompanyStoreName)
                assertEquals(null, record.otherCompanyRepName)
                assertEquals(updateData.comment.value, record.comment)
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.registrationDate)
                assertEquals("134530", record.linkCdRegistrationTime)
                // @formatter:off

                // 未使用カラムの情報は更新されていないこと
                assertEquals(existsData.state, record.state)
                assertEquals(existsData.customerRepCd, record.customerRepCd)
                assertEquals(existsData.customerRepShozokuCd, record.customerRepShozokuCd)
                assertEquals(existsData.listComment, record.listComment)
                assertEquals(existsData.otherCompanyMemberId, record.otherCompanyMemberId)
            }

            @Test
            @DisplayName("他社による登録リクエストの場合、未使用のカラム以外は、リクエスト値でレコードが更新されること")
            fun case2() {
                // setup
                val updateData = stubOtherCompanyForceRegisterTemporaryReservation(
                    buildingCode = existsData.buildingCd!!,
                    roomCode = existsData.roomCd!!,
                    scheduledMoveInDate = LocalDate.of(2024, 12, 19),
                    comment = "仮押さえコメントはこちら",
                )

                // execute
                dslContext.transaction { config -> repository.forceRegister(config, updateData) }

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(updateData.getId())

                assertEquals(1, sqlResult.size)

                val record = sqlResult.first()

                // @formatter:off
                assertEquals(updateData.getId().buildingCode.value, record.buildingCd)
                assertEquals(updateData.getId().roomCode.value, record.roomCd)
                assertEquals("1", record.status)
                assertEquals("20241219", record.applicationScheduledDate)
                assertEquals(null, record.applicationScheduledPersonCd)
                assertEquals(null, record.customerRepBranchCd)
                assertEquals(updateData.otherCompanyInfo!!.companyCode, record.contractFormECode)
                assertEquals("1", record.otherCompanyFlag)
                assertEquals(updateData.otherCompanyInfo!!.companyName, record.otherCompanyName)
                assertEquals(updateData.otherCompanyInfo!!.storeName, record.otherCompanyStoreName)
                assertEquals(updateData.otherCompanyInfo!!.staffName, record.otherCompanyRepName)
                assertEquals(updateData.comment.value, record.comment)
                // 現在時刻を、LocalDateTime.of(2024, 11, 12, 13, 45) で固定しているため、以下の値が設定される
                assertEquals("20241112", record.registrationDate)
                assertEquals("134530", record.linkCdRegistrationTime)
                // @formatter:off

                // 未使用カラムの情報は更新されていないこと
                assertEquals(existsData.state, record.state)
                assertEquals(existsData.customerRepCd, record.customerRepCd)
                assertEquals(existsData.customerRepShozokuCd, record.customerRepShozokuCd)
                assertEquals(existsData.listComment, record.listComment)
            }

            @Test
            @DisplayName("同時リクエストが行われた場合、最新のリクエストで更新されること")
            fun case3() {
                // レコードロックの時間を調整するために、プロダクションのRepositoryを継承し、
                // lockRecord の処理を従来の処理 + 遅延処理で overrideしている
                val repository =
                    object : TemporaryReservationRepository(dslContext,branchRepository, employeeRepository) {
                        override fun lockRecordWait(
                            config: Configuration, id: Property.Id
                        ): TemporaryReservationFilePojo? {
                            val result = super.lockRecordWait(config, id)
                            Thread.sleep(3000)
                            return result
                        }
                    }

                val targetBuildingCode = existsData.buildingCd!!
                val targetRoomCode = existsData.roomCd!!

                // task1の実行後1秒後に、task2を実行する
                // task2は、task1のロック解除を待って更新処理が行われる
                runAsyncTasks(
                    task1 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.forceRegister(
                                    config,
                                    stubOwnCompanyForceRegisterTemporaryReservation(
                                        buildingCode = targetBuildingCode,
                                        roomCode = targetRoomCode,
                                        assignedEmployeeCode = "000009",
                                    )
                                )
                            }
                        }
                    },
                    task2 = {
                        assertDoesNotThrow {
                            dslContext.transaction { config ->
                                repository.forceRegister(
                                    config,
                                    stubOwnCompanyForceRegisterTemporaryReservation(
                                        buildingCode = targetBuildingCode,
                                        roomCode = targetRoomCode,
                                        assignedEmployeeCode = "000011",
                                    )
                                )
                            }
                        }
                    },
                    delayBetweenTasks = 1000,
                )

                // verify
                val sqlResult = dslContext.selectTemporaryReservationBy(targetBuildingCode, targetRoomCode)

                assertEquals(1, sqlResult.size)

                // リクエスト②の内容でレコードが作成されていることを確認
                assertEquals("000011", sqlResult.first().applicationScheduledPersonCd)
            }
        }
    }

    @Nested
    @DisplayName("仮押さえ取得")
    inner class FindScenario {

        @Test
        @DisplayName("解除された仮押さえ情報を取得できること")
        fun case1() {
            // setup
            val pojo = stubTemporaryReservationFilePojo(
                applicationScheduledDate = null,
                applicationScheduledPersonCd = null,
                customerRepBranchCd = null,
                comment = "ここに仮押さえコメントが入ります",
                registrationDate = null,
                linkCdRegistrationTime = null,
                otherCompanyFlag = null,
                otherCompanyMemberId = null,
                otherCompanyName = null,
                otherCompanyStoreName = null,
                otherCompanyRepName = null
            )
            dslContext.saveTemporaryReservationFilePojo(pojo)

            // execute
            val result = repository.findBy(stubPropertyId(
                buildingCode = pojo.buildingCd!!,
                roomCode = pojo.roomCd!!
            )) as TemporaryReservationInfo.CancelledTemporaryReservationInfo

            assertEquals(pojo.buildingCd, result.getId().buildingCode.value)
            assertEquals(pojo.roomCd, result.getId().roomCode.value)
            assertEquals(pojo.comment, result.comment.value)
        }

    }
}

