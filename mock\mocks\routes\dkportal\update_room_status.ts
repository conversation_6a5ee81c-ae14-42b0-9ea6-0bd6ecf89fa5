import { DKPortalValidationError } from '../../shared/dkportal/dkportal_response';
import { deserializeGraphqlBody, getOperationName } from '../../shared/shared_function';

/**
 * DKポータル/物件の掲載ステータス更新API
 *
 * サンプルリクエスト
 *  curl -i -X POST -H "Content-Type: application/json" http://localhost:8083/dkportal -d '{"query": "mutation { updateRoomStatus(property_type: 1, kentaku_building_code: \"019100894\", kentaku_room_code:\"01010\", is_publish: 1, up_state: 2) { status } }"}'
 *
 */

const targetOperation = 'updateRoomStatus';

export default [
  {
    id: 'dkportal_update_room_status',
    url: '/dkportal',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: async (req, res, next) => {
            const query = req.body['query'];
            // 対象の関数ではない場合は処理を終了
            const operation = getOperationName(query);
            if (targetOperation !== operation) {
              next();
              return;
            }

            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const result = await checkParameter(query);

            if (result) {
              res.status(400);
              res.send({
                errors: [result],
                data: { [targetOperation]: null },
              });
              return;
            }

            res.status(200);
            res.send({
              data: { [targetOperation]: { status: true } },
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            res.status(500);
            res.send({
              error_message: '予期せぬエラーが発生しました。',
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する。適切な場合はtrueを返す。
 * @param {*} req
 * @returns {Boolean}
 */
async function checkParameter(query): Promise<DKPortalValidationError> {
  const body = deserializeGraphqlBody(query);

  const {
    property_type,
    kentaku_building_code,
    kentaku_room_code,
    is_publish,
    up_state,
  } = body;

  // 必須チェック
  if (!property_type) {
    return new DKPortalValidationError(
      targetOperation,
      'property_type',
      '物件種別は必須です。',
    );
  }
  if (!kentaku_building_code) {
    return new DKPortalValidationError(
      targetOperation,
      'kentaku_building_code',
      '建物コードは必須です。',
    );
  }
  if (!kentaku_room_code) {
    return new DKPortalValidationError(
      targetOperation,
      'kentaku_room_code',
      '部屋コードは必須です。',
    );
  }
  if (!is_publish) {
    return new DKPortalValidationError(
      targetOperation,
      'is_publish',
      '掲載ON/OFFは必須です。',
    );
  }
  if (!up_state) {
    return new DKPortalValidationError(
      targetOperation,
      'up_state',
      '掲載状態は必須です。',
    );
  }

  // 1: 住居用 2: 事業用
  if (property_type.value != 1 && property_type.value != 2) {
    return new DKPortalValidationError(
      targetOperation,
      'property_type',
      '物件種別は0か1で指定してください。',
    );
  }

  // ex) 012345678	9桁
  if (kentaku_building_code.value.length !== 9) {
    return new DKPortalValidationError(
      targetOperation,
      'kentaku_building_code',
      '建物コードは9桁で指定してください。',
    );
  }

  // ex) 01010	5桁
  if (kentaku_room_code.value.length !== 5) {
    return new DKPortalValidationError(
      targetOperation,
      'kentaku_room_code',
      '部屋コードは5桁で指定してください。',
    );
  }

  // 0: OFF 1: ON
  if (is_publish.value != 0 && is_publish.value != 1) {
    return new DKPortalValidationError(
      targetOperation,
      'is_publish',
      '掲載ON/OFFは0か1で指定してください。',
    );
  }

  // 1.準備中 2.募集中  3.申込中  4.入居済
  if (
    up_state.value != 1 &&
    up_state.value != 2 &&
    up_state.value != 3 &&
    up_state.value != 4
  ) {
    return new DKPortalValidationError(
      targetOperation,
      'up_state',
      '掲載状態は1,2,3,4のいずれかで指定してください。',
    );
  }

  return undefined;
}
