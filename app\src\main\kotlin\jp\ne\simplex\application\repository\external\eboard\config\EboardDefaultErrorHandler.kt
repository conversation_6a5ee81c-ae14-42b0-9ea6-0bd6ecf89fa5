package jp.ne.simplex.application.repository.external.eboard.config

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import jp.ne.simplex.application.repository.external.ExternalApiObjectMapper
import jp.ne.simplex.exception.*
import org.springframework.http.HttpMethod
import org.springframework.http.client.ClientHttpResponse
import org.springframework.web.client.ResponseErrorHandler
import java.net.URI

class EboardDefaultErrorHandler : ResponseErrorHandler {
    private val objectMapper: ObjectMapper = ExternalApiObjectMapper.getInstance()

    override fun hasError(response: ClientHttpResponse): <PERSON><PERSON><PERSON> {
        val body = objectMapper.readValue(response.body, EboardDefaultResponse::class.java)

        return response.statusCode.is4xxClientError ||
                response.statusCode.is5xxServerError || body.hasError()
    }

    override fun handleError(url: URI, method: HttpMethod, response: ClientHttpResponse) {
        val body = objectMapper.readValue(response.body, EboardDefaultResponse::class.java)
        val ex = RuntimeException(body.toString())

        if (response.statusCode.is5xxServerError) {
            throw ExternalApiConnectionException(ErrorType.EBOARD_API_ERROR, ex)
        }
        when (body.getResultCode()) {
            EboardResultCode.PARAMETER_INVALID,
            EboardResultCode.PARAMETER_MISSING,
            EboardResultCode.NO_PROPERTIES_FOUND,
            EboardResultCode.NO_STORES_FOUND,
            EboardResultCode.MAX_COUNT_EXCEEDED,
            EboardResultCode.DATA_ALREADY_EXISTS,
            EboardResultCode.ROOM_NOT_FOUND,
            -> throw ExternalApiServerException(
                ErrorType.EBOARD_API_ERROR,
                ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format(body.toString()),
            )

            EboardResultCode.AUTH_FAILED,
            EboardResultCode.TOKEN_EXPIRED
            -> throw ExternalApiUnauthorizedException(
                ErrorType.EBOARD_API_ERROR,
                ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format(body.toString()),
            )

            EboardResultCode.DATABASE_ERROR,
            EboardResultCode.MAIL_SEND_ERROR,
            EboardResultCode.OTHER_ERROR,
            EboardResultCode.MESSAGE_NONE,
            -> throw ExternalApiServerException(
                ErrorType.EBOARD_API_ERROR,
                ErrorMessage.EBOARD_RECEIVED_ERROR_RESPONSE.format(body.toString()),
            )

            EboardResultCode.SUCCESS -> {}
        }
    }
}

/** いい部件ボード認証API以外のAPIレスポンスに対応する型 */
class EboardDefaultResponse(
    @JsonProperty("result")
    val result: Int?,

    @JsonProperty("message")
    val message: String?
) {

    fun hasError(): Boolean {
        return getResultCode().isError()
    }

    fun getResultCode(): EboardResultCode {
        return EboardResultCode.entries.find { it.code == result }
            ?: EboardResultCode.SUCCESS
    }

    override fun toString(): String {
        return "{ result: ${result}, message: $message }"
    }
}

/** いい物件ボードAPIの応答コード一覧 */
enum class EboardResultCode(val code: Int, val message: String) {
    //パラメータ値が規定外であった場合(数値のパラメータに数字以外の文字列が入っていた等)
    PARAMETER_INVALID(201, "【エラー】パラメータが規定外です。"),

    // 必須となるパラメータ(auth_code/entry_date/function/(各機能で必須とするもの))が無い場合
    PARAMETER_MISSING(202, "【エラー】パラメータ不正です。"),

    // 認証に失敗した場合
    AUTH_FAILED(203, "【エラー】認証に失敗しました。"),

    // アクセストークンの有効期限が切れていた場合
    TOKEN_EXPIRED(204, "【エラー】アクセストークンの有効期限が切れています。"),

    // DAOがExceptionまたは、エラー値を返した場合
    DATABASE_ERROR(101, "データベースにアクセスできませんでした。"),

    // お問合わせ登録で、メール送信の失敗があった場合
    MAIL_SEND_ERROR(102, "メール送信エラーが発生しました。"),

    // その他の障害が発生した場合
    OTHER_ERROR(103, "その他の障害が発生しました"),

    // 前回更新日時がDB更新日時(バッチによる作成時刻等)以降の場合
    MESSAGE_NONE(11, "メッセージ無し"),

    // 検索結果が0件であった場合
    NO_PROPERTIES_FOUND(10, "該当する物件が見つかりませんでした。条件を変えて検索してみてください。"),

    // 検索結果が0件であった場合
    NO_STORES_FOUND(20, "該当する店舗が見つかりませんでした。条件を変えて検索してみてください。"),

    // 要求された数が受け付け可能な最大件数をオーバーする場合
    MAX_COUNT_EXCEEDED(30, "最大件数をオーバーする為、受け付けできませんでした。"),

    // 要求された物件または検索条件がすでに存在する場合
    DATA_ALREADY_EXISTS(40, "そのデータはすでに存在します"),

    // 要求された物件が無い場合
    ROOM_NOT_FOUND(1, "room not found"),

    // 正常に処理が完了した場合
    SUCCESS(0, ""),
    ;

    fun isError(): Boolean {
        return this != SUCCESS
    }
}
