name: Deploy App DockerImage

on:
  # GitHub上から手動実行するとき
  workflow_dispatch:
    inputs:
      environment:
        description: "デプロイ先環境"
        required: true
        default: "it"
        type: environment
      tag_version:
        description: "Dockerイメージのタグのバージョン部分"
        required: true

jobs:
  DeployAppImage:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 30
    permissions:
      id-token: write
      contents: write
    steps:
      # イメージのタグの生成
      - name: Create Image Tag
        run: |
          echo "IMAGE_TAG=app-${{ inputs.tag_version }}" >> $GITHUB_ENV
      # AWS認証
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: arn:aws:iam::${{ vars.ACCOUNT_ID }}:role/GitHubActions-${{ vars.DEPLOY_ENV }}-ECRDeployRole
      # ECRログイン
      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin ${{ vars.APP_ECR_URI }}
        shell: bash
      # イメージ存在確認
      - name: Check if Image Exists
        run: |
          repo_name=$(echo ${{ vars.APP_ECR_URI }} | awk -F'/' '{print $2}')
          if aws ecr describe-images --repository-name $repo_name --image-ids imageTag=${{ env.IMAGE_TAG }} >/dev/null 2>&1; then
            echo "Image with tag '${{ env.IMAGE_TAG }}' already exists."
            exit 1
          fi
        shell: bash
      # GitHubAppのトークンを作成
      - name: Generate GitHub Apps token
        id: generate
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.REPOSITORY_READ_TOKEN_APP_ID }}
          private-key: ${{ secrets.REPOSITORY_READ_TOKEN_PRIVATE_KEY }}
          repositories: |
            propetech-handover
            propetech-server-handover
      # 自リポジトリからソースをダウンロード
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate.outputs.token }}
          repository: ${{ github.repository }}
      # propetech リポジトリからソースをダウンロード
      - name: Clone dklink-project/propetech-handover repository
        env:
          GITHUB_TOKEN: ${{ steps.generate.outputs.token }}
        run: |
          cd ../
          base_branch=${{ github.ref_name }}
          echo "base_branch: $base_branch"
          target_branch="main"
          echo "target_branch: $target_branch"
          git clone --branch $target_branch https://x-access-token:${GITHUB_TOKEN}@github.com/dklink-project/propetech-handover.git
        shell: bash
      # Setup（Java/Gradleの設定及び、Jooq自動生成）
      - name: Setup
        uses: ./.github/workflows/composite/setup
      # Jarファイル生成
      - name: Generate .jar file
        run: ./gradlew build -x test -x :db:build -x :db-strategy:build --stacktrace
      # QEMUのセットアップ
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/arm64
      - name: Copy buildkitd.toml
        run: |
          sudo mkdir -p /etc/buildkit
          sudo cp "$GITHUB_WORKSPACE/.github/etc/buildkit/buildkitd.toml" /etc/buildkit/buildkitd.toml
        shell: bash
      # Buildxのセットアップ
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-config: /etc/buildkit/buildkitd.toml
          driver-opts: |
            image=moby/buildkit:buildx-stable-1
      # Docker Build
      - name: Docker Build
        run: |
          docker buildx build \
          --platform linux/arm64 \
          -t ${{ vars.APP_ECR_URI }}:${{ env.IMAGE_TAG }} \
          -f app/Dockerfile app \
          --network=host \
          --load
        shell: bash
      # Docker push
      - name: Push Docker Image
        run: |
          docker push ${{ vars.APP_ECR_URI }}:${{ env.IMAGE_TAG }}
        shell: bash
      # AWS認証(Setup内でCache利用で別のRoleが付与されるため再度ECRDeployRoleを付与
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: arn:aws:iam::${{ vars.ACCOUNT_ID }}:role/GitHubActions-${{ vars.DEPLOY_ENV }}-ECRDeployRole
      # タグ管理用SSMパラメータストアの値を更新
      - name: Update PRD SSM Parameter
        run: |
          aws ssm put-parameter --name ${{ vars.APP_IMAGE_TAG_SSM_PARAMETER_NAME }} --value ${{ env.IMAGE_TAG }} --type "String" --overwrite
        shell: bash
