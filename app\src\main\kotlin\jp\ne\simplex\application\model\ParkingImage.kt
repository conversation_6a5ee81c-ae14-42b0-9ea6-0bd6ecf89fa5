package jp.ne.simplex.application.model

import org.springframework.web.multipart.MultipartFile

class RegisterParkingImage private constructor(
    // 建物コード
    val buildingCode: Building.Code,
    // 駐車場配置図画像
    val imageFile: ImageFile,
) {
    companion object {
        private const val IMAGE_MAX_BYTES: Long = 500 * 1024 // 500KB

        fun of(buildingCode: Building.Code, image: MultipartFile): RegisterParkingImage {
            return RegisterParkingImage(
                buildingCode = buildingCode,
                imageFile = ImageFile.of(image, IMAGE_MAX_BYTES)
            )
        }
    }
}
