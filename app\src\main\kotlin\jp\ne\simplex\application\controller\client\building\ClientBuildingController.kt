package jp.ne.simplex.application.controller.client.building

import jp.ne.simplex.application.controller.client.building.dto.ClientBuildingGetRequest
import jp.ne.simplex.application.controller.client.building.dto.ClientBuildingGetResponse
import jp.ne.simplex.application.service.BuildingService
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/building")
class ClientBuildingController(
    private val service: BuildingService,
) {

    @GetMapping()
    @ApiDefinition(summary = "建物情報取得API")
    fun getBuilding(
        @ModelAttribute request: ClientBuildingGetRequest,
    ): ClientBuildingGetResponse {
        val result = service.getActive(request.toServiceInterface())

        return ClientBuildingGetResponse.of(result)
    }
}
