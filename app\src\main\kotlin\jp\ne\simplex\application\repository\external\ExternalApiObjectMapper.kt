package jp.ne.simplex.application.repository.external

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.json.JsonMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule

class ExternalApiObjectMapper private constructor() {

    companion object {

        private val objectMapper: ObjectMapper = JsonMapper.builder()
            // 余計なプロパティが返却されても無視してデシリアライズを行う
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .build()
            .registerKotlinModule()

        fun getInstance(): ObjectMapper {
            return objectMapper
        }
    }
}
