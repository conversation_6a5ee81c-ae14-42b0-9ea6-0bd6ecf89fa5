package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.client.shared.ClientBranchDto
import jp.ne.simplex.application.controller.client.shared.ClientEmployeeDto
import jp.ne.simplex.application.controller.client.shared.ClientPropertyIdDto
import jp.ne.simplex.application.model.TemporaryReservationInfo
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd

data class ClientTemporaryReservationGetResponse private constructor(
    val data: Data?,
) {

    data class Data private constructor(
        @JsonProperty("propertyId")
        @field:Schema(description = "物件ID")
        val propertyId: ClientPropertyIdDto,

        @JsonProperty("scheduledMoveInDate")
        @field:Schema(description = "入居予定日", example = "20241219")
        val scheduledMoveInDate: String?,

        @JsonProperty("ownCompanyInfo")
        @field:Schema(description = "自社の仮押さえ担当情報")
        val ownCompanyInfo: OwnCompanyInfo?,

        @JsonProperty("otherCompanyInfo")
        @field:Schema(description = "他社の仮押さえ担当情報")
        val otherCompanyInfo: OtherCompanyInfo?,

        @JsonProperty("comment")
        @field:Schema(description = "コメント")
        val comment: String?,

        @JsonProperty("updateDate")
        @field:Schema(description = "更新日", example = "20250310")
        val updateDate: String?,

        @JsonProperty("updateTime")
        @field:Schema(description = "更新時刻", example = "184530")
        val updateTime: String?,
    ) {
        companion object {

            fun of(source: TemporaryReservationInfo): Data {
                return when (source) {
                    is TemporaryReservationInfo.OwnCompanyTemporaryReservationInfo -> {
                        Data(
                            propertyId = ClientPropertyIdDto.of(source.getId()),
                            scheduledMoveInDate = source.scheduledMoveInDate.yyyyMMdd(),
                            ownCompanyInfo = OwnCompanyInfo(
                                assignedBranch = ClientBranchDto.of(source.assignedBranch),
                                assignedEmployee = ClientEmployeeDto.of(source.assignedEmployee),
                            ),
                            otherCompanyInfo = null,
                            comment = source.comment.value,
                            updateDate = source.version.updateDate,
                            updateTime = source.version.updateTime,
                        )
                    }

                    is TemporaryReservationInfo.OtherCompanyTemporaryReservationInfo -> {
                        Data(
                            propertyId = ClientPropertyIdDto.of(source.getId()),
                            scheduledMoveInDate = source.scheduledMoveInDate.yyyyMMdd(),
                            ownCompanyInfo = null,
                            otherCompanyInfo = OtherCompanyInfo(
                                companyCode = source.otherCompanyInfo.companyCode,
                                companyName = source.otherCompanyInfo.companyName,
                                storeName = source.otherCompanyInfo.storeName,
                                staffName = source.otherCompanyInfo.staffName,
                            ),
                            comment = source.comment.value,
                            updateDate = source.version.updateDate,
                            updateTime = source.version.updateTime,
                        )
                    }

                    is TemporaryReservationInfo.CancelledTemporaryReservationInfo -> {
                        Data(
                            propertyId = ClientPropertyIdDto.of(source.getId()),
                            scheduledMoveInDate = null,
                            ownCompanyInfo = null,
                            otherCompanyInfo = null,
                            comment = source.comment.value,
                            updateDate = null,
                            updateTime = null,
                        )
                    }
                }
            }
        }

        data class OwnCompanyInfo(
            @JsonProperty("assignedBranch")
            @field:Schema(description = "担当支店")
            val assignedBranch: ClientBranchDto,

            @JsonProperty("assignedEmployee")
            @field:Schema(description = "担当者")
            val assignedEmployee: ClientEmployeeDto,
        )

        data class OtherCompanyInfo(
            @JsonProperty("companyCode")
            @field:Schema(description = "会員ID", example = "DKP943")
            val companyCode: String,

            @JsonProperty("companyName")
            @field:Schema(description = "会社名", example = "〇〇株式会社")
            val companyName: String,

            @JsonProperty("storeName")
            @field:Schema(description = "店舗名", example = "〇〇支店")
            val storeName: String,

            @JsonProperty("staffName")
            @field:Schema(description = "担当者名", example = "田中太郎")
            val staffName: String
        )
    }

    companion object {
        fun of(source: TemporaryReservationInfo?): ClientTemporaryReservationGetResponse {
            return ClientTemporaryReservationGetResponse(
                data = source?.let { Data.of(it) }
            )
        }
    }
}
