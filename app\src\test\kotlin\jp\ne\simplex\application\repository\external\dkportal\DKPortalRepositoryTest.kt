package jp.ne.simplex.application.repository.external.dkportal

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.PropertyMaintenance
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRestConfig
import jp.ne.simplex.application.repository.external.dkportal.dto.DKPortalExclusiveCompanyType
import jp.ne.simplex.exception.ExternalApiServerException
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.stub.*
import org.hamcrest.Matchers.containsString
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.test.web.client.MockRestServiceServer
import org.springframework.test.web.client.match.MockRestRequestMatchers.*
import org.springframework.test.web.client.response.MockRestResponseCreators.*
import org.springframework.web.client.RestClient
import kotlin.test.assertEquals

class DKPortalRepositoryTest {
    companion object {
        private const val ENDPOINT = "http://example.com"
    }

    private val builder = RestClient.builder()

    private val mockServer = MockRestServiceServer.bindTo(builder).ignoreExpectOrder(true).build()

    private val repository = DKPortalRepository(
        DKPortalRestConfig(ENDPOINT).graphqlClient(builder),
    )

    @Nested
    @DisplayName("駐車場検索用情報連携APIの検証")
    inner class Scenario1 {

        @Test
        @DisplayName("リクエストパラメータが適切に生成されること")
        fun case1() {

            val status = stubParkingContractPossibility()

            val requestBuilder = StringBuilder()
            requestBuilder.append("order_code: \\\"${status.orderCode.value}\\\",")
            requestBuilder.append("available_parking_flag: 0,")
            requestBuilder.append("second_parking_contract_possible_flag: 0")

            val query = """
                mutation {
                    updateParkingLot(
                        $requestBuilder
                    ) {
                        status
                    }
                }
            """.trimIndent()

            // expected
            val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

            // setup && verify
            mockServer.expect(requestTo(ENDPOINT)) // このURLに
                .andExpect(content().json(requestBody)) // ボディを設定して
                .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                .andRespond(  // 以下のレスポンスが返却される
                    withSuccess(
                        """
                        {
                            "data": {
                                "updateParkingLot": {
                                  "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )
            repository.updateParkingLot(status)
            mockServer.verify()
        }
    }

    @Nested
    @DisplayName("AD・FF更新APIの検証")
    inner class Scenario2 {

        @Nested
        @DisplayName("リクエストパラメータが適切に生成されること")
        inner class Scenario2x1 {
            @Test
            @DisplayName("AD・FFの値を更新する場合、リクエストパラメータが適切に作成できること")
            fun case1() {
                val request = stubUpdatePropertyMaintenance(
                    advertisementFee = 21200,
                    frontFreerentPeriod = 3F,
                ).getUpdateAdFf(
                    stubProperty(buildingType = Building.Type.APARTMENT)
                )

                val requestBuilder = StringBuilder()
                requestBuilder.append("property_type: 1,")
                requestBuilder.append("kentaku_building_code: \\\"${request.id.buildingCode.value}\\\",")
                requestBuilder.append("kentaku_room_code: \\\"${request.id.roomCode.value}\\\",")
                requestBuilder.append("ad_price: ${request.adFf.advertisementFee!!},")
                requestBuilder.append("ad_unit_type: 3,")
                requestBuilder.append("ff_month: 3.0")

                val query = """
                mutation {
                    updateAdFfAndDepositKeyMoney(
                        $requestBuilder
                    ) {
                        status
                    }
                }
            """.trimIndent()

                // expected
                val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "data": {
                                "updateAdFfAndDepositKeyMoney": {
                                  "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )
                repository.updateAdff(listOf(request))

                mockServer.verify()
            }

            @Test
            @DisplayName("AD・FFの値を削除する場合、リクエストパラメータが適切に作成できること")
            fun case2() {
                val request = stubUpdatePropertyMaintenance(
                    advertisementFee = null,
                    frontFreerentPeriod = null,
                ).getUpdateAdFf(
                    stubProperty(buildingType = Building.Type.COMMERCIAL_APARTMENT)
                )

                val requestBuilder = StringBuilder()
                requestBuilder.append("property_type: 2,")
                requestBuilder.append("kentaku_building_code: \\\"${request.id.buildingCode.value}\\\",")
                requestBuilder.append("kentaku_room_code: \\\"${request.id.roomCode.value}\\\",")
                requestBuilder.append("ad_price: 0,")  // 設定削除の場合は、0を設定する
                requestBuilder.append("ad_unit_type: 3,")
                requestBuilder.append("ff_month: 0.0") // 設定削除の場合は、0を設定する

                val query = """
                mutation {
                    updateAdFfAndDepositKeyMoney(
                        $requestBuilder
                    ) {
                        status
                    }
                }
            """.trimIndent()

                // expected
                val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "data": {
                                "updateAdFfAndDepositKeyMoney": {
                                  "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )
                repository.updateAdff(listOf(request))

                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("APIレスポンスに応じた関数の戻り値（成功した物件ID/失敗した物件ID）を生成できること")
        inner class Scenario2x2 {
            @Test
            @DisplayName("4,5xx系エラーが帰ってきた場合、成功した物件ID=0, 失敗した物件ID=ALL の戻り値が生成されること")
            fun case1() {
                // setup && verify
                val request1 =
                    stubUpdatePropertyMaintenance(buildingCode = "634623600").getUpdateAdFf(
                        stubProperty()
                    )

                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request1.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(withBadRequest())

                val request2 =
                    stubUpdatePropertyMaintenance(buildingCode = "143251300").getUpdateAdFf(
                        stubProperty()
                    )
                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request2.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(withServerError())

                // execute
                val result = repository.updateAdff(listOf(request1, request2))

                // verify
                assertEquals(0, result.success.size)
                assertEquals(2, result.failed.size)
            }

            @Test
            @DisplayName("HTTPステータスが200の場合、レスポンスの中身に応じて戻り値が生成されること")
            fun case2() {
                // setup && verify
                val request1 =
                    stubUpdatePropertyMaintenance(buildingCode = "634623600").getUpdateAdFf(
                        stubProperty()
                    )

                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request1.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "data": {
                                    "updateAdFfAndDepositKeyMoney": {
                                      "status": true
                                    }
                                }
                            }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                val request2 =
                    stubUpdatePropertyMaintenance(buildingCode = "143251300").getUpdateAdFf(
                        stubProperty()
                    )
                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request2.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "data": {
                                    "updateAdFfAndDepositKeyMoney": {
                                      "status": false
                                    }
                                }
                            }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                val result = repository.updateAdff(listOf(request1, request2))

                // verify
                assertEquals(1, result.success.size)
                assertEquals(request1.id, result.success.first())

                assertEquals(1, result.failed.size)
                assertEquals(request2.id, result.failed.first())
            }
        }
    }

    @Nested
    @DisplayName("部屋情報（掲載状態）更新APIの検証")
    inner class Scenario3 {

        @Nested
        @DisplayName("リクエストパラメータが適切に生成されること")
        inner class Scenario3x1 {
            @Test
            @DisplayName("リクエストパラメータが適切に作成できること")
            fun case1() {
                val propertyUpState = Property.UpState.RECRUITING

                val request = stubUpdatePropertyMaintenance(
                    publishStatus = PropertyMaintenance.PublishStatus.PUBLIC
                ).getUpdatePublishStatusWithUpState(
                    stubProperty(buildingType = Building.Type.APARTMENT),
                    propertyUpState,
                )

                val requestBuilder = StringBuilder()
                requestBuilder.append("property_type: 1,")
                requestBuilder.append("kentaku_building_code: \\\"${request.id.buildingCode.value}\\\",")
                requestBuilder.append("kentaku_room_code: \\\"${request.id.roomCode.value}\\\",")
                requestBuilder.append("is_publish: 1,")
                requestBuilder.append("up_state: 2")

                val query = """
                mutation {
                    updateRoomStatus(
                        $requestBuilder
                    ) {
                        status
                    }
                }
            """.trimIndent()

                // expected
                val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "data": {
                                "updateRoomStatus": {
                                  "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                repository.updateRoomStatus(
                    listOf(request)
                )

                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("APIレスポンスに応じた関数の戻り値（成功した物件ID/失敗した物件ID）を生成できること")
        inner class Scenario3x2 {
            @Test
            @DisplayName("4,5xx系エラーが帰ってきた場合、成功した物件ID=0, 失敗した物件ID=ALL の戻り値が生成されること")
            fun case1() {
                // setup && verify
                val request1 =
                    stubUpdatePropertyMaintenance(buildingCode = "634623600")
                        .getUpdatePublishStatusWithUpState(
                            stubProperty(),
                            Property.UpState.PREPARING,
                        )

                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request1.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(withBadRequest())

                val request2 =
                    stubUpdatePropertyMaintenance(buildingCode = "143251300")
                        .getUpdatePublishStatusWithUpState(
                            stubProperty(),
                            Property.UpState.TEMPORARY_RESERVED
                        )
                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request2.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(withServerError())

                // execute
                val result = repository.updateRoomStatus(
                    listOf(request1, request2)
                )

                // verify
                assertEquals(0, result.success.size)
                assertEquals(2, result.failed.size)
            }

            @Test
            @DisplayName("HTTPステータスが200の場合、レスポンスの中身に応じて戻り値が生成されること")
            fun case2() {
                // setup && verify
                val request1 =
                    stubUpdatePropertyMaintenance(buildingCode = "634623600")
                        .getUpdatePublishStatusWithUpState(
                            stubProperty(),
                            Property.UpState.TEMPORARY_RESERVED,
                        )

                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request1.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "data": {
                                    "updateRoomStatus": {
                                      "status": true
                                    }
                                }
                            }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                val request2 =
                    stubUpdatePropertyMaintenance(buildingCode = "143251300")
                        .getUpdatePublishStatusWithUpState(
                            stubProperty(),
                            Property.UpState.RECRUITING,
                        )
                mockServer.expect(requestTo(ENDPOINT))
                    .andExpect(
                        content().string(
                            containsString(
                                "kentaku_building_code: \\\"${request2.id.buildingCode.value}\\\""
                            )
                        )
                    )
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                            {
                                "data": {
                                    "updateRoomStatus": {
                                      "status": false
                                    }
                                }
                            }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                // execute
                val result = repository.updateRoomStatus(
                    listOf(request1, request2)
                )

                // verify
                assertEquals(1, result.success.size)
                assertEquals(request1.id, result.success.first())

                assertEquals(1, result.failed.size)
                assertEquals(request2.id, result.failed.first())
            }
        }
    }

    @Nested
    @DisplayName("先行公開登録APIの検証")
    inner class Scenario4 {

        @Test
        @DisplayName("居住用物件の先行公開登録APIのリクエストパラメータが適切に生成されること")
        fun case1() {
            val requestAuthInfo = stubJwtAuthInfo()

            val request = stubRegisterExclusiveProperty(
                exclusiveTargetWithIds = listOf(
                    stubExclusivePropertyTargetWithId(
                        companyType = ExclusiveProperty.CompanyType.RealEstate,
                        eCode = "E12345678",
                    )
                )
            ).getRecords().first()

            // 居住用物件
            val property = stubProperty(
                buildingCode = request.propertyId.buildingCode.value,
                roomCode = request.propertyId.roomCode.value,
                buildingType = Building.Type.APARTMENT,
            )

            val dkPortalCompanyType =
                DKPortalExclusiveCompanyType.of(request.exclusiveTargetWithId.target.companyType)

            val requestBuilder = StringBuilder()
            requestBuilder.append("dk_link_id: \\\"${request.exclusiveTargetWithId.id.value}\\\",")
            requestBuilder.append("sales_office_id: ${property.marketingBranchOfficeCode!!.value},")
            requestBuilder.append("kentaku_building_code: \\\"${request.propertyId.buildingCode.value}\\\",")
            requestBuilder.append("kentaku_room_code: \\\"${request.propertyId.roomCode.value}\\\",")
            requestBuilder.append("exclusive_from: \\\"${request.exclusiveRange.from.yyyyMMdd()}\\\",")
            requestBuilder.append("exclusive_to: \\\"${request.exclusiveRange.to.yyyyMMdd()}\\\",")
            requestBuilder.append("e_code: \\\"${request.exclusiveTargetWithId.target.eCode?.value}\\\",")
            requestBuilder.append("company_type: ${dkPortalCompanyType.value}")

            val query = """
                mutation {
                    createExclusiveHousing(
                        $requestBuilder
                    ) {
                        id,kentaku_building_code,kentaku_room_code,exclusive_from,exclusive_to,company_type,e_code
                    }
                }
            """.trimIndent()

            // expected
            val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

            // setup && verify
            mockServer.expect(requestTo(ENDPOINT)) // このURLに
                .andExpect(content().json(requestBody)) // ボディを設定して
                .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                .andRespond(  // 以下のレスポンスが返却される
                    withSuccess(
                        """
                        {
                            "data": {
                                "createExclusiveHousing": [
                                    {
                                        "id": "${request.exclusiveTargetWithId.id.value}",
                                        "kentaku_building_code": "${request.propertyId.buildingCode.value}",
                                        "kentaku_room_code": "${request.propertyId.roomCode.value}",
                                        "exclusive_from": "${request.exclusiveRange.from.yyyyMMdd()}",
                                        "exclusive_to": "${request.exclusiveRange.to.yyyyMMdd()}",
                                        "company_type": ${dkPortalCompanyType.value},
                                        "e_code": "${request.exclusiveTargetWithId.target.eCode?.value}",
                                        "auto_delete_flg": true
                                    }
                                ]
                            }
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )

            repository.createExclusive(property, request)

            mockServer.verify()
        }

        @Test
        @DisplayName("事業用物件の先行公開登録APIのリクエストパラメータが適切に生成されること")
        fun case2() {
            val requestAuthInfo = stubJwtAuthInfo()

            val request = stubRegisterExclusiveProperty(
                exclusiveTargetWithIds = listOf(
                    stubExclusivePropertyTargetWithId(
                        companyType = ExclusiveProperty.CompanyType.HouseCom,
                    )
                )
            ).getRecords().first()

            // 居住用物件
            val property = stubProperty(
                buildingCode = request.propertyId.buildingCode.value,
                roomCode = request.propertyId.roomCode.value,
                buildingType = Building.Type.FACTORY,
            )

            val dkPortalCompanyType =
                DKPortalExclusiveCompanyType.of(request.exclusiveTargetWithId.target.companyType)

            val requestBuilder = StringBuilder()
            requestBuilder.append("dk_link_id: \\\"${request.exclusiveTargetWithId.id.value}\\\",")
            requestBuilder.append("sales_office_id: ${property.marketingBranchOfficeCode!!.value},")
            requestBuilder.append("kentaku_building_code: \\\"${request.propertyId.buildingCode.value}\\\",")
            requestBuilder.append("kentaku_room_code: \\\"${request.propertyId.roomCode.value}\\\",")
            requestBuilder.append("exclusive_from: \\\"${request.exclusiveRange.from.yyyyMMdd()}\\\",")
            requestBuilder.append("exclusive_to: \\\"${request.exclusiveRange.to.yyyyMMdd()}\\\",")
            requestBuilder.append("company_type: ${dkPortalCompanyType.value}")

            val query = """
                mutation {
                    createExclusiveBusiness(
                        $requestBuilder
                    ) {
                        id,kentaku_building_code,kentaku_room_code,exclusive_from,exclusive_to,company_type,e_code
                    }
                }
            """.trimIndent()

            // expected
            val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

            // setup && verify
            mockServer.expect(requestTo(ENDPOINT)) // このURLに
                .andExpect(content().json(requestBody)) // ボディを設定して
                .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                .andRespond(  // 以下のレスポンスが返却される
                    withSuccess(
                        """
                        {
                            "data": {
                                "createExclusiveBusiness": [
                                    {
                                        "id": "${request.exclusiveTargetWithId.id.value}",
                                        "kentaku_building_code": "${request.propertyId.buildingCode.value}",
                                        "kentaku_room_code": "${request.propertyId.roomCode.value}",
                                        "exclusive_from": "${request.exclusiveRange.from.yyyyMMdd()}",
                                        "exclusive_to": "${request.exclusiveRange.to.yyyyMMdd()}",
                                        "company_type": ${dkPortalCompanyType.value},
                                        "auto_delete_flg": true
                                    }
                                ]
                            }
                        }
                        """.trimIndent(),
                        MediaType.APPLICATION_JSON
                    )
                )

            repository.createExclusive(property, request)

            mockServer.verify()
        }
    }

    @Nested
    @DisplayName("先行公開削除APIの検証")
    inner class Scenario5 {

        @Nested
        @DisplayName("リクエストパラメータ生成処理の検証")
        inner class GenerateParameterLogicCheck {

            @Test
            @DisplayName("居住用物件の先行公開削除APIのリクエストパラメータが適切に生成されること")
            fun case1() {
                val exclusiveId = ExclusiveProperty.Id.of(123456789012345678)

                // 居住用物件
                val property = stubProperty(
                    buildingCode = "000000001",
                    roomCode = "01010",
                    buildingType = Building.Type.APARTMENT,
                )

                val query = """
                mutation {
                    deleteExclusiveHousing(
                        dk_link_id: \"${exclusiveId.value}\"
                    ) {
                        status
                    }
                }
            """.trimIndent()

                // expected
                val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "data": {
                                "deleteExclusiveHousing": {
                                    "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                repository.deleteExclusive(property, exclusiveId)

                mockServer.verify()
            }

            @Test
            @DisplayName("事業用物件の先行公開削除APIのリクエストパラメータが適切に生成されること")
            fun case2() {
                val exclusiveId = ExclusiveProperty.Id.of(123456789012345678)

                // 居住用物件
                val property = stubProperty(
                    buildingCode = "000000001",
                    roomCode = "01010",
                    buildingType = Building.Type.DEDICATED_OFFICE,
                )

                val query = """
                mutation {
                    deleteExclusiveBusiness(
                        dk_link_id: \"${exclusiveId.value}\"
                    ) {
                        status
                    }
                }
            """.trimIndent()

                // expected
                val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "data": {
                                "deleteExclusiveBusiness": {
                                    "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                repository.deleteExclusive(property, exclusiveId)

                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("APIレスポンスを適切にハンドリングできること")
        inner class HandleResponse {

            @Test
            @DisplayName("4,5xx系エラーが帰ってきた場合、Exceptionがスローされること")
            fun case1() {
                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(withServerError()) // 以下のレスポンスが返却される

                assertThrows<ExternalApiServerException> {
                    repository.deleteExclusive(
                        stubProperty(),
                        ExclusiveProperty.Id.of(123456789012345678)
                    )
                }

                mockServer.verify()
            }

            @Test
            @DisplayName("HTTPステータスが200の場合、status=falseの場合、Exceptionがスローされること")
            fun case2() {
                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(
                        withSuccess(
                            """
                        {
                            "data": {
                                "deleteExclusiveHousing": {
                                    "status": false
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    ) // 以下のレスポンスが返却される

                assertThrows<ExternalApiServerException> {
                    repository.deleteExclusive(
                        stubProperty(),
                        ExclusiveProperty.Id.of(123456789012345678)
                    )
                }

                mockServer.verify()
            }
        }
    }

    @Nested
    @DisplayName("先行公開更新APIの検証")
    inner class Scenario6 {
        @Nested
        @DisplayName("リクエストパラメータ生成処理の検証")
        inner class GenerateParameterLogicCheck {

            @Test
            @DisplayName("居住用物件の先行公開更新APIのリクエストパラメータが適切に生成されること")
            fun case1() {
                // 居住用物件
                val property = stubProperty(
                    buildingType = Building.Type.APARTMENT,
                )

                val request = stubUpdateExclusiveProperty(
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 100,
                            companyType = ExclusiveProperty.CompanyType.RealEstate,
                            eCode = "E12345678",
                        )
                    )
                )

                val record = request.getRecords(property).first()

                val dkPortalCompanyType =
                    DKPortalExclusiveCompanyType.of(record.exclusiveTargetWithId.target.companyType)

                val requestBuilder = StringBuilder()
                requestBuilder.append("dk_link_id: \\\"${request.id.value}\\\",")
                requestBuilder.append("exclusive_from: \\\"${record.exclusiveRange.from.yyyyMMdd()}\\\",")
                requestBuilder.append("exclusive_to: \\\"${record.exclusiveRange.to.yyyyMMdd()}\\\",")
                requestBuilder.append("e_code: \\\"${record.exclusiveTargetWithId.target.eCode?.value}\\\",")
                requestBuilder.append("company_type: ${dkPortalCompanyType.value}")

                val query = """
                mutation {
                    updateExclusiveHousing(
                        $requestBuilder
                    ) {
                        status
                    }
                }
            """.trimIndent()

                // expected
                val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "data": {
                                "updateExclusiveHousing": {
                                    "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                repository.updateExclusive(property, request.id, record)

                mockServer.verify()
            }

            @Test
            @DisplayName("事業用物件の先行公開更新APIのリクエストパラメータが適切に生成されること")
            fun case2() {
                // 居住用物件
                val property = stubProperty(
                    buildingType = Building.Type.DEDICATED_OFFICE,
                )

                val request = stubUpdateExclusiveProperty(
                    exclusiveTargetWithIds = listOf(
                        stubExclusivePropertyTargetWithId(
                            id = 100,
                            companyType = ExclusiveProperty.CompanyType.RealEstate,
                            eCode = "E12345678",
                        )
                    )
                )

                val record = request.getRecords(property).first()

                val dkPortalCompanyType =
                    DKPortalExclusiveCompanyType.of(record.exclusiveTargetWithId.target.companyType)

                val requestBuilder = StringBuilder()
                requestBuilder.append("dk_link_id: \\\"${request.id.value}\\\",")
                requestBuilder.append("exclusive_from: \\\"${record.exclusiveRange.from.yyyyMMdd()}\\\",")
                requestBuilder.append("exclusive_to: \\\"${record.exclusiveRange.to.yyyyMMdd()}\\\",")
                requestBuilder.append("e_code: \\\"${record.exclusiveTargetWithId.target.eCode?.value}\\\",")
                requestBuilder.append("company_type: ${dkPortalCompanyType.value}")

                val query = """
                mutation {
                    updateExclusiveBusiness(
                        $requestBuilder
                    ) {
                        status
                    }
                }
            """.trimIndent()

                // expected
                val requestBody = """
                    {
                        "query": "$query"
                    }
                """.trimIndent()

                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(content().json(requestBody)) // ボディを設定して
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(  // 以下のレスポンスが返却される
                        withSuccess(
                            """
                        {
                            "data": {
                                "updateExclusiveBusiness": {
                                    "status": true
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    )

                repository.updateExclusive(property, request.id, record)

                mockServer.verify()
            }
        }

        @Nested
        @DisplayName("APIレスポンスを適切にハンドリングできること")
        inner class HandleResponse {

            @Test
            @DisplayName("4,5xx系エラーが帰ってきた場合、Exceptionがスローされること")
            fun case1() {
                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(withServerError()) // 以下のレスポンスが返却される

                assertThrows<ExternalApiServerException> {
                    repository.updateExclusive(
                        stubProperty(),
                        ExclusiveProperty.Id.of(123456789012345678),
                        stubUpdateExclusiveProperty(
                            exclusiveTargetWithIds = listOf(
                                stubExclusivePropertyTargetWithId()
                            )
                        ).getRecords(stubProperty()).first()
                    )
                }

                mockServer.verify()
            }

            @Test
            @DisplayName("HTTPステータスが200の場合、status=falseの場合、Exceptionがスローされること")
            fun case2() {
                // setup && verify
                mockServer.expect(requestTo(ENDPOINT)) // このURLに
                    .andExpect(method(HttpMethod.POST))  // POSTリクエストを送信すると
                    .andRespond(
                        withSuccess(
                            """
                        {
                            "data": {
                                "updateExclusiveHousing": {
                                    "status": false
                                }
                            }
                        }
                        """.trimIndent(),
                            MediaType.APPLICATION_JSON
                        )
                    ) // 以下のレスポンスが返却される

                assertThrows<ExternalApiServerException> {
                    repository.updateExclusive(
                        stubProperty(),
                        ExclusiveProperty.Id.of(123456789012345678),
                        stubUpdateExclusiveProperty(
                            exclusiveTargetWithIds = listOf(
                                stubExclusivePropertyTargetWithId()
                            )
                        ).getRecords(
                            stubProperty(buildingType = Building.Type.APARTMENT)
                        ).first()
                    )
                }
                mockServer.verify()
            }
        }
    }
}
