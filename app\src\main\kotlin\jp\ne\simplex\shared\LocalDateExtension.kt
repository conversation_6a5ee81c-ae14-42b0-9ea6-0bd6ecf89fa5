package jp.ne.simplex.shared

import java.time.LocalDate
import java.time.format.DateTimeFormatter

class LocalDateExtension {

    companion object {

        fun LocalDate.yyyyMMdd(): String {
            return this.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        }

        fun LocalDate.yyyyMM(): String {
            return this.format(DateTimeFormatter.ofPattern("yyyyMM"))
        }
    }
}
