package jp.ne.simplex.application.controller.external.parking.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.external.parking.dto.ExternalReservationType.Companion.toReservationType
import jp.ne.simplex.application.controller.external.shared.ReserveDateCreatorForWelcomePark
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ParkingReservation.RequestSource.Companion.toRequestSource
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import java.time.LocalTime

data class ExternalUpdateParkingReservationForWelcomeParkRequest(

    @JsonProperty("buildingCode")
    @field:Schema(description = "建物コード", example = "000000001")
    val buildingCode: String,

    @JsonProperty("parkingCode")
    @field:Schema(description = "駐車場コード", example = "001")
    val parkingCode: String,

    @JsonProperty("reservationStatus")
    @field:Schema(description = "予約状態", example = "1")
    val reservationStatus: Int,

    @JsonProperty("reservationType")
    @field:Schema(description = "予約種別", example = "3")
    val reservationType: Int,

    @JsonProperty("reserverName")
    @field:Schema(description = "利用者氏名", example = "シンプレクス予約")
    val reserverName: String?,

    @JsonProperty("reserverTel")
    @field:Schema(description = "利用者電話番号", example = "090-1234-5678")
    val reserverTel: String?,

    ) {

    companion object {
        private const val NAME_MAX_LENGTH = 42
    }

    fun toServiceInterface(apiKey: AuthInfo.ApiKey): ParkingReservationAction {

        val parkingReservationStatus =
            ParkingReservation.Status.fromValue(reservationStatus.toString())

        // 1日利用以外は受け付けない
        if (reservationType != ExternalReservationType.ONE_DAY.value) {
            throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_TYPE.format())
        }

        // 氏名の長さチェック
        if (reserverName != null && reserverName.length > NAME_MAX_LENGTH) {
            throw ClientValidationException(
                ErrorMessage.STRING_MAX_LENGTH.format("氏名", NAME_MAX_LENGTH)
            )
        }

        try {
            return when (parkingReservationStatus) {
                ParkingReservation.Status.RESERVATION -> toRegisterReservation(apiKey)
                else -> throw ClientValidationException(ErrorMessage.PARKING_RESERVATION_INVALID_STATUS.format())
            }
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        }
    }

    private fun toRegisterReservation(apiKey: AuthInfo.ApiKey): RegisterParkingReservation {
        val reserveStartDate = ReserveDateCreatorForWelcomePark.reserveStartDate()
        return RegisterParkingReservation.of(
            parkingLotId = ParkingLot.Id(
                Building.Code.of(buildingCode),
                ParkingLot.Code.of(parkingCode)
            ),
            parkingReservationStatus = ParkingReservation.Status.RESERVATION,
            reservationType = ExternalReservationType.fromValue(reservationType)
                ?.toReservationType()!!,
            reserveStartDatetime = reserveStartDate.atTime(LocalTime.of(8, 0, 0)),
            reserveEndDatetime = reserveStartDate.plusDays(1).atTime(LocalTime.of(7, 59, 59)),
            reserverName = reserverName,
            reserverTel = reserverTel?.let { TelephoneNumber.of(it) },
            requestSource = apiKey.externalSystem.toRequestSource()
        )
    }
}
