package jp.ne.simplex.authentication.saml

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties("app.auth.single-sign-on")
class SingleSignOnConfig(
    val enabled: Boolean,
    val saml: SamlConfig,
    val accessKey: AccessKeyConfig,
) {
    class SamlConfig(
        val entityId: String,
        val dkLinkUrl: String,
        val secretId: String,
    )

    class AccessKeyConfig(
        val secretId: String,
    )
}

