package jp.ne.simplex.application.controller.external.parking

import jp.ne.simplex.application.controller.external.ExternalRootController
import jp.ne.simplex.application.controller.external.parking.dto.*
import jp.ne.simplex.application.service.ParkingReservationService
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
class ExternalParkingController(
    private val parkingReservationService: ParkingReservationService,
) : ExternalRootController() {

    @PostMapping("/update-parking-reservation")
    @ApiDefinition(summary = "キマルームSignが駐車場予約(新規作成・取消)をするAPI")
    fun updateParkingReservation(
        @RequestBody request: ExternalUpdateParkingReservationRequest,
        @AuthenticationPrincipal apiKey: AuthInfo.ApiKey,
    ) {
        parkingReservationService.updateReservation(
            apiKey, request.toServiceInterface(apiKey)
        )
    }

    @GetMapping("/check-parking-reservable-for-welcomepark")
    @ApiDefinition(
        summary = "WelcomePark向け駐車場予約可能確認",
        description = "WelcomeParkが予約可能か確認するAPI",
    )
    fun checkParkingReservableForWelcomePark(
        @ModelAttribute request: ExternalCheckParkingReservableForWelcomeParkRequest,
    ): ExternalCheckParkingReservableForWelcomeParkResponse {
        val param = request.toServiceInterface()
        try {
            parkingReservationService.validateRegister(param)
            return ExternalCheckParkingReservableForWelcomeParkResponse.toSuccessResponse()
        } catch (e: ServerValidationException) {
            return ExternalCheckParkingReservableForWelcomeParkResponse.toFailureResponse(e)
        }
    }

    @PostMapping("/update-parking-reservation-for-welcomepark")
    @ApiDefinition(summary = "WelcomeParkが駐車場予約(新規作成)をするAPI")
    fun updateParkingReservationForWelcomePark(
        @RequestBody request: ExternalUpdateParkingReservationForWelcomeParkRequest,
        @AuthenticationPrincipal apiKey: AuthInfo.ApiKey,
    ): ExternalUpdateParkingReservationForWelcomeParkResponse {
        val param = request.toServiceInterface(apiKey)
        try {
            parkingReservationService.updateReservation(apiKey, param)
            return ExternalUpdateParkingReservationForWelcomeParkResponse.toSuccessResponse()
        } catch (e: ServerValidationException) {
            return ExternalUpdateParkingReservationForWelcomeParkResponse.toFailureResponse(e)
        }
    }
}
