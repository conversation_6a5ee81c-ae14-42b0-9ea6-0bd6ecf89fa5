name: Deploy Mock DockerImage

on:
  # GitHub上から手動実行するとき
  workflow_dispatch:
    inputs:
      environment:
        description: 'デプロイ先環境'
        required: true
        default: 'it'
        type: environment

jobs:
  DeployMockImage:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 30
    permissions:
      id-token: write
      contents: write
    steps:
      # イメージのタグの生成
      - name: Create Image Tag
        run: |
          tag_version=$(date "+%Y%m%d%H%M%S")
          echo "IMAGE_TAG=mock-${tag_version}" >> $GITHUB_ENV
      # AWS認証
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: arn:aws:iam::${{ vars.ACCOUNT_ID }}:role/GitHubActions-${{ vars.DEPLOY_ENV }}-ECRDeployRole
      # ECRログイン
      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin ${{ vars.APP_ECR_URI }}
        shell: bash
      # イメージ存在確認
      - name: Check if Image Exists
        run: |
          repo_name=$(echo ${{ vars.APP_ECR_URI }} | awk -F'/' '{print $2}')
          if aws ecr describe-images --repository-name $repo_name --image-ids imageTag=${{ env.IMAGE_TAG }} >/dev/null 2>&1; then
            echo "Image with tag '${{ env.IMAGE_TAG }}' already exists."
            exit 1
          fi
        shell: bash
      # リポジトリからソースをダウンロード
      - name: Clone repository
        uses: actions/checkout@v4
      # QEMUのセットアップ
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/arm64
      - name: Copy buildkitd.toml
        run: |
          sudo mkdir -p /etc/buildkit
          sudo cp "$GITHUB_WORKSPACE/.github/etc/buildkit/buildkitd.toml" /etc/buildkit/buildkitd.toml
        shell: bash
      # Buildxのセットアップ
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-config: /etc/buildkit/buildkitd.toml
          driver-opts: |
            image=moby/buildkit:buildx-stable-1
      # Docker Build
      - name: Docker Build
        run: |
          docker buildx build \
          --platform linux/arm64 \
          -t ${{ vars.APP_ECR_URI }}:${{ env.IMAGE_TAG }} \
          -f mock/Dockerfile mock \
          --network=host \
          --load
        shell: bash
      # Docker push
      - name: Push Docker Image
        run: |
          docker push ${{ vars.APP_ECR_URI }}:${{ env.IMAGE_TAG }}
        shell: bash
      # タグ管理用SSMパラメータストアの値を更新
      - name: Update PRD SSM Parameter
        run: |
          aws ssm put-parameter --name ${{ vars.MOCK_IMAGE_TAG_SSM_PARAMETER_NAME }} --value ${{ env.IMAGE_TAG }} --type "String" --overwrite
        shell: bash