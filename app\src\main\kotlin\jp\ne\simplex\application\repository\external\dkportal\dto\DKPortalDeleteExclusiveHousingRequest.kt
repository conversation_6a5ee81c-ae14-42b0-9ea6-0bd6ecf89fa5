package jp.ne.simplex.application.repository.external.dkportal.dto

import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalOperationName

class DKPortalDeleteExclusiveHousingRequest(
    id: ExclusiveProperty.Id,
) : DKPortalDeleteExclusiveRequest(
    dkLinkId = id.value.toString(),
) {
    override fun getDKPortalOperationName(): DKPortalOperationName {
        return DKPortalOperationName.DELETE_EXCLUSIVE_HOUSING
    }
}
