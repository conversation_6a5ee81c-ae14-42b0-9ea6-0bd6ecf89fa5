import {
  eboardResponseMapping,
  EboardResponseType,
} from '../../shared/eboard/eboard_response_type';
import EboardTokenManager from '../../shared/eboard/eboard_token_manager';
import { keysToLowerCase } from '../../shared/shared_function';

export default [
  {
    id: 'eboard_token_verification_interceptor',
    url: '*/eboard/*',
    method: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: (req, res, next) => {
            const headers = keysToLowerCase(req.headers);
            const authorization = headers['authorization'];
            if (!authorization) {
              res.status(400);
              res.send({
                result: EboardResponseType.AUTH_FAILED,
                message: eboardResponseMapping[EboardResponseType.AUTH_FAILED],
              });
              return;
            }
            const token = authorization.replace('Bearer', '').trim();

            if (EboardTokenManager.instance.verify(token)) {
              next();
              return;
            }

            res.status(400);
            res.send({
              result: EboardResponseType.TOKEN_EXPIRED,
              message: eboardResponseMapping[EboardResponseType.TOKEN_EXPIRED],
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (req, res, next) => {
            next();
          },
        },
      },
    ],
  },
];
