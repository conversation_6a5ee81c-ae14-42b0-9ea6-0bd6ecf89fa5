package jp.ne.simplex.application.controller.client.exclusive

import jp.ne.simplex.application.controller.client.exclusive.dto.*
import jp.ne.simplex.application.service.ExclusivePropertyService
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/exclusive")
class ClientExclusiveController(
    private val exclusivePropertyService: ExclusivePropertyService,
) {
    @GetMapping("/properties")
    @ApiDefinition(summary = "先行公開検索API")
    fun getExclusiveProperties(
        @ModelAttribute request: ClientExclusivePropertiesSearchRequest,
    ): ClientExclusivePropertiesSearchResponse {
        val (response, totalCount) = exclusivePropertyService.search(request.toServiceInterface())
        return ClientExclusivePropertiesSearchResponse.of(response, totalCount)
    }

    @PostMapping("/properties")
    @ApiDefinition(summary = "先行公開新規登録API")
    fun registerExclusiveProperties(
        @RequestBody request: ClientExclusivePropertiesRegisterRequest,
        @AuthenticationPrincipal authInfo: AuthInfo.Jwt,
    ): ClientExclusivePropertiesRegisterResponse {
        val result = exclusivePropertyService.register(authInfo, request.toServiceInterface())

        return ClientExclusivePropertiesRegisterResponse.of(result)
    }

    @PutMapping("/properties")
    @ApiDefinition(summary = "先行公開更新API")
    fun updateExclusiveProperties(
        @RequestBody request: ClientExclusivePropertiesUpdateRequest,
        @AuthenticationPrincipal authInfo: AuthInfo.Jwt,
    ): ClientExclusivePropertiesUpdateResponse {
        val result = exclusivePropertyService.update(authInfo, request.toServiceInterface())

        return ClientExclusivePropertiesUpdateResponse.of(result)
    }

    @DeleteMapping("/properties")
    @ApiDefinition(summary = "先行公開削除API")
    fun deleteExclusiveProperties(
        @RequestBody request: ClientExclusivePropertiesDeleteRequest,
        @AuthenticationPrincipal authInfo: AuthInfo.Jwt,
    ): ClientExclusivePropertiesDeleteResponse {
        val result = exclusivePropertyService.cancel(authInfo, request.toServiceInterface())

        return ClientExclusivePropertiesDeleteResponse.of(result)
    }
}
