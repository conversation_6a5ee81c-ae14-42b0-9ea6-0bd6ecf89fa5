import EboardTokenManager from '../../shared/eboard/eboard_token_manager';
import { getSecretKey } from '../../shared/eboard/secret_manager';
import { v4 as uuidv4 } from 'uuid';

/**
 * いい物件ボード/認証API
 *
 * サンプルリクエスト
 * curl -i -X POST -H "Content-Type: application/json" http://localhost:8083/eboard/token_dp_issue -d '{"userid": "dummy","password":"dummy","passphrase":"dummy"}'
 */
export default [
  {
    id: 'eboard_token_dp_issue',
    url: '/eboard/token_dp_issue',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: async (req, res) => {
            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const responseType = await checkParameter(req);

            if (responseType != EboardAuthResponseType.SUCCESS) {
              res.status(400);
              res.send({
                error_code: responseType,
                error_id: uuidv4(),
                error_msg: eboardAuthResponseMapping[responseType],
              });

              return;
            }

            const token = uuidv4();
            const expiredAt = new Date();
            expiredAt.setMinutes(expiredAt.getMinutes() + 3);

            EboardTokenManager.instance.add(token, expiredAt);

            // status: 200 の場合
            res.status(200);
            res.send({
              expired_dt: expiredAt,
              issue_id: uuidv4(),
              accesstoken: token,
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            const responseType = EboardAuthResponseType.INTERNAL_ERROR;

            res.status(500);
            res.send({
              error_code: responseType,
              error_id: uuidv4(),
              error_msg: eboardAuthResponseMapping[responseType],
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する
 * responseCodeMapping（api_response_specification.js）定義のエラーコードを返却する
 * @param {*} req
 * @returns {EboardAuthResponseType}
 */
async function checkParameter(req): Promise<EboardAuthResponseType> {
  const body = req.body;
  if (!body) {
    return EboardAuthResponseType.PARAMETER_MISSING;
  }

  const reqUserId = body['userid'];
  const reqPassword = body['password'];
  const reqPassphrase = body['passphrase'];

  if (!reqUserId && !reqPassword && !reqPassphrase) {
    return EboardAuthResponseType.PARAMETER_MISSING;
  }

  const { userid, password, passphrase } = await getSecretKey('eboard_api_secret');

  // ユーザーID/パスワード/パスフレーズが不正な場合
  if (
    !(reqUserId === userid && reqPassword === password && reqPassphrase === passphrase)
  ) {
    return EboardAuthResponseType.PARAMETER_INVALID;
  }

  return EboardAuthResponseType.SUCCESS;
}

/**
 * 認証API用の応答コード一覧
 */
const enum EboardAuthResponseType {
  SUCCESS = '0',
  PARAMETER_MISSING = '01',
  INTERNAL_ERROR = '02',
  UNKNOWN_USER = '03',
  PARAMETER_INVALID = '04',
  FORBIDDEN_CLIENT = '06',
}

const eboardAuthResponseMapping: Record<EboardAuthResponseType, string> = {
  [EboardAuthResponseType.SUCCESS]: '正常終了',

  [EboardAuthResponseType.PARAMETER_MISSING]:
    'パラメータ不正（ユーザID、パスワードがNULL)',

  [EboardAuthResponseType.INTERNAL_ERROR]:
    '内部エラー（通常ありえない、ﾊｯｼｭ値の取得失敗）',

  [EboardAuthResponseType.UNKNOWN_USER]:
    '認証用マスタが未整備もしくはユーザIDの登録がない',

  [EboardAuthResponseType.PARAMETER_INVALID]: 'パスワード、パスフレーズの何れかが不正',

  [EboardAuthResponseType.FORBIDDEN_CLIENT]:
    'アクセスのあったライアントIPはサービス提供対象外',
};
