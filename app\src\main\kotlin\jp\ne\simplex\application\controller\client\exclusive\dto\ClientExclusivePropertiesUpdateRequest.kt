package jp.ne.simplex.application.controller.client.exclusive.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.DateRange
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.application.model.UpdateExclusiveProperty
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd

@JsonIgnoreProperties(value = ["ecodeList"])
class ClientExclusivePropertiesUpdateRequest(
    @JsonProperty("id")
    @field:Schema(description = "先行公開ID", example = "174539560948543005")
    val id: String,

    @JsonProperty("exclusiveFrom")
    @field:Schema(description = "先行期間From", example = "20190116")
    val exclusiveFrom: String,

    @JsonProperty("exclusiveTo")
    @field:Schema(description = "先行期間To", example = "20191216")
    val exclusiveTo: String,

    @JsonProperty("companyTypeList")
    @field:Schema(description = "先行先種別リスト", type = "Array", example = "[0,1]")
    val companyTypeList: List<String>,

    @JsonProperty("eCodeList")
    @field:Schema(description = "Eコード", type = "Array", example = "[E12345678, E12345679]")
    val eCodeList: List<String>?,
) {

    companion object {
        private const val MAX_ECODE_COUNT = 50
    }

    // Service層の Interface に変換する
    fun toServiceInterface(): UpdateExclusiveProperty {
        if (companyTypeList.isEmpty()) {
            throw ClientValidationException(ErrorMessage.MISSING_REQUIRED_FIELDS.format("先行先"))
        }

        // 先行先に、不動産を指定している場合、Eコードが必須
        if (companyTypeList.contains(ExclusiveProperty.CompanyType.RealEstate.value.toString())) {
            if (eCodeList.isNullOrEmpty()) {
                throw ClientValidationException(ErrorMessage.MISSING_REQUIRED_FIELDS.format("Eコード"))
            }
        }

        if ((eCodeList?.size ?: 0) > MAX_ECODE_COUNT) {
            throw ClientValidationException(
                ErrorMessage.PROPERTY_MAX_COUNT.format("Eコード", MAX_ECODE_COUNT)
            )
        }

        try {
            return UpdateExclusiveProperty(
                id = ExclusiveProperty.Id.of(id.toLong()),
                exclusiveRange = DateRange.of(
                    from = exclusiveFrom.yyyyMMdd(),
                    to = exclusiveTo.yyyyMMdd(),
                    allowSameDate = true,
                ),
                exclusiveTargetWithIds = ExclusiveProperty.ExclusiveTargetWithId.of(
                    companyTypes = companyTypeList,
                    eCodes = eCodeList,
                )
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        } catch (_: Exception) {
            throw ClientValidationException(ErrorMessage.INVALID_REQUEST_FORMAT.format())
        }

    }
}
