package jp.ne.simplex.application.controller.client.exclusive.dto

import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import jp.ne.simplex.application.model.Agent
import jp.ne.simplex.application.model.ExclusiveProperty
import jp.ne.simplex.exception.ClientValidationException

class ClientExclusivePropertiesRegisterRequestTest : FunSpec({

    context("リクエストのバリデーションチェック") {
        test("先行先種別リストが未設定の場合、不正なリクエストと判断されること") {
            shouldThrow<ClientValidationException> {
                ClientExclusivePropertiesRegisterRequest(
                    buildingCode = "*********",
                    roomCd = "01010",
                    exclusiveFrom = "20190116",
                    exclusiveTo = "20191216",
                    companyTypeList = emptyList(),
                    eCodeList = listOf("E12345678", "E12345679", "E12345680"),
                ).toServiceInterface()
            }
        }

        context("Eコードが未設定の場合") {

            test("先行先種別リストに、1か2のみが設定されている場合は、正常なリクエストとして判断されること") {
                shouldNotThrow<ClientValidationException> {
                    ClientExclusivePropertiesRegisterRequest(
                        buildingCode = "*********",
                        roomCd = "01010",
                        exclusiveFrom = "20190116",
                        exclusiveTo = "20191216",
                        companyTypeList = listOf("1", "2"),
                        eCodeList = null,
                    ).toServiceInterface()
                }
            }

            test("先行先種別リストに、0が設定されている場合は、不正なリクエストとして判断されること") {
                shouldThrow<ClientValidationException> {
                    ClientExclusivePropertiesRegisterRequest(
                        buildingCode = "*********",
                        roomCd = "01010",
                        exclusiveFrom = "20190116",
                        exclusiveTo = "20191216",
                        companyTypeList = listOf("0", "1", "2"),
                        eCodeList = null,
                    ).toServiceInterface()
                }
            }
        }

        context("Eコードは最大で50件までしか設定できないこと") {
            test("Eコードを50件指定した場合、正常なリクエストとして判断されること") {
                shouldNotThrow<ClientValidationException> {
                    ClientExclusivePropertiesRegisterRequest(
                        buildingCode = "*********",
                        roomCd = "01010",
                        exclusiveFrom = "20190116",
                        exclusiveTo = "20191216",
                        companyTypeList = listOf("0", "1", "2"),
                        eCodeList = List(50) { index ->
                            "E123456${String.format("%02d", index)}"
                        },
                    ).toServiceInterface()
                }
            }

            test("Eコードを51件指定した場合、不正なリクエストとして判断されること") {
                shouldThrow<ClientValidationException> {
                    ClientExclusivePropertiesRegisterRequest(
                        buildingCode = "*********",
                        roomCd = "01010",
                        exclusiveFrom = "20190116",
                        exclusiveTo = "20191216",
                        companyTypeList = listOf("0", "1", "2"),
                        eCodeList = List(51) { index ->
                            "E123456${String.format("%02d", index)}"
                        },

                        ).toServiceInterface()
                }
            }

        }
    }

    context("先行公開ID生成処理の検証") {

        listOf(
            1,
            9,
            10,
            50
        ).forEach { input ->
            test("先行公開登録リクエストにEコードが${input}件指定されている場合、18桁の先行公開IDが生成されること") {
                ClientExclusivePropertiesRegisterRequest(
                    buildingCode = "*********",
                    roomCd = "01010",
                    exclusiveFrom = "20190116",
                    exclusiveTo = "20191216",
                    companyTypeList = listOf("0", "1", "2"),
                    eCodeList = List(input) { "E12345678" },
                ).toServiceInterface()
                    .exclusiveTargetWithIds.forEach {
                        it.id.value.toString().length.shouldBe(18)
                    }
            }
        }
    }

    test("リクエストを適切にデシリザライズできること") {
        val result = ClientExclusivePropertiesRegisterRequest(
            buildingCode = "*********",
            roomCd = "01010",
            exclusiveFrom = "20190116",
            exclusiveTo = "20191216",
            companyTypeList = listOf("0", "1", "2"),
            eCodeList = listOf("E12345678", "E12345679", "E12345680"),
        ).toServiceInterface()
        result.exclusiveTargetWithIds.size.shouldBe(5)

        val exclusiveTargetList = result.exclusiveTargetWithIds.map { it.target }

        exclusiveTargetList.contains(
            ExclusiveProperty.ExclusiveTarget(
                companyType = ExclusiveProperty.CompanyType.Leasing,
                eCode = null
            )
        ).shouldBe(true)

        exclusiveTargetList.contains(
            ExclusiveProperty.ExclusiveTarget(
                companyType = ExclusiveProperty.CompanyType.HouseCom,
                eCode = null
            )
        ).shouldBe(true)

        exclusiveTargetList.contains(
            ExclusiveProperty.ExclusiveTarget(
                companyType = ExclusiveProperty.CompanyType.RealEstate,
                eCode = Agent.ECode.of("E12345678")
            )
        ).shouldBe(true)

        exclusiveTargetList.contains(
            ExclusiveProperty.ExclusiveTarget(
                companyType = ExclusiveProperty.CompanyType.RealEstate,
                eCode = Agent.ECode.of("E12345679")
            )
        ).shouldBe(true)

        exclusiveTargetList.contains(
            ExclusiveProperty.ExclusiveTarget(
                companyType = ExclusiveProperty.CompanyType.RealEstate,
                eCode = Agent.ECode.of("E12345680")
            )
        ).shouldBe(true)

    }
})
