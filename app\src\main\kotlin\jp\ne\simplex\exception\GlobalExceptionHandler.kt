package jp.ne.simplex.exception

import jakarta.servlet.http.HttpServletRequest
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice

@RestControllerAdvice
class GlobalExceptionHandler {

    companion object {
        private val log = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)
    }

    @ExceptionHandler(ClientValidationException::class)
    fun handleClientValidationException(
        e: ClientValidationException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.warn(e.message, e)

        return ResponseEntity.status(HttpStatus.BAD_REQUEST.value())
            .body(ErrorResponse.of(e, request))
    }

    @ExceptionHandler(ServerValidationException::class)
    fun handleServerValidationException(
        e: ServerValidationException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.warn(e.message, e)

        return ResponseEntity.status(HttpStatus.BAD_REQUEST.value())
            .body(ErrorResponse.of(e, request))
    }

    @ExceptionHandler(ModelCreationFailedException::class)
    fun handleModelCreationFailedException(
        e: ModelCreationFailedException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.warn(e.message, e)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .body(ErrorResponse.of(e, request))
    }

    @ExceptionHandler(DBValidationException::class)
    fun handleDBValidationException(
        e: DBValidationException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.warn(e.message, e)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .body(ErrorResponse.of(e, request))
    }

    @ExceptionHandler(ExternalApiServerException::class)
    fun handleExternalApiServerException(
        e: ExternalApiServerException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.warn(e.message, e)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .body(ErrorResponse.of(e, request))
    }

    @ExceptionHandler(ExternalApiUnauthorizedException::class)
    fun handleExternalApiUnauthorizedException(
        e: ExternalApiUnauthorizedException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.warn(e.message, e)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .body(ErrorResponse.of(e, request))
    }

    @ExceptionHandler(ExternalApiConnectionException::class)
    fun handleExternalApiConnectionException(
        e: ExternalApiConnectionException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.error(e.message, e)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .body(ErrorResponse.of(e, request))
    }

    @ExceptionHandler(HttpMessageNotReadableException::class)
    fun handleHttpMessageNotReadableException(
        e: HttpMessageNotReadableException,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.warn(e.message, e)

        return ResponseEntity.status(HttpStatus.BAD_REQUEST.value())
            .body(
                ErrorResponse.of(
                    ClientValidationException(ErrorMessage.INVALID_REQUEST_FORMAT.format()),
                    request
                )
            )
    }

    @ExceptionHandler(Exception::class)
    fun handleException(
        e: java.lang.Exception,
        request: HttpServletRequest
    ): ResponseEntity<ErrorResponse> {
        log.error(e.message, e)

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .body(ErrorResponse.of(ErrorType.UNEXPECTED_ERROR, request))
    }
}
