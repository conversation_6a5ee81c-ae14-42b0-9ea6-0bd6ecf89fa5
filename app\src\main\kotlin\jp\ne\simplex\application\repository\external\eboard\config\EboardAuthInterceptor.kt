package jp.ne.simplex.application.repository.external.eboard.config

import jp.ne.simplex.application.repository.external.BufferingClientHttpResponseWrapper
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiUnauthorizedException
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse

class EboardAuthInterceptor : ClientHttpRequestInterceptor {
    override fun intercept(
        request: HttpRequest,
        body: ByteArray,
        execution: ClientHttpRequestExecution
    ): ClientHttpResponse {
        // 認証APIの場合は、何もしない
        if (request.uri.path.contains(EboardApiPath.TOKEN_DP_ISSUE.value)) {
            return BufferingClientHttpResponseWrapper(execution.execute(request, body))
        }
        // 認証トークンが存在しない場合のみ、認証エラーをスローする
        if (EboardTokenManager.getToken() == null) {
            throw ExternalApiUnauthorizedException(
                ErrorType.EBOARD_API_ERROR,
                ErrorMessage.EXTERNAL_API_AUTHENTICATION_FAILURE.format()
            )
        }
        request.headers.add(HttpHeaders.AUTHORIZATION, "Bearer ${EboardTokenManager.getToken()}")
        return BufferingClientHttpResponseWrapper(execution.execute(request, body))
    }
}
