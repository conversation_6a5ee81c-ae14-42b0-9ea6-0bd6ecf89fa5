package jp.ne.simplex.application.controller.client.auth.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema

data class ClientLoginParkingResponse(
    @JsonProperty("path")
    @field:Schema(description = "遷移先パス")
    val path: String?,
) {
    companion object {
        fun of(path: String?): ClientLoginParkingResponse {
            return ClientLoginParkingResponse(path)
        }
    }
}


