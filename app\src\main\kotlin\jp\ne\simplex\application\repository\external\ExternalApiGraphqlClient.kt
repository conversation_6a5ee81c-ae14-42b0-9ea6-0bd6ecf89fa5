package jp.ne.simplex.application.repository.external

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.MapLikeType
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.shared.ClassExtension.Companion.getJsonPropertyNames
import org.apache.hc.client5.http.classic.HttpClient
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.web.client.ResponseErrorHandler
import org.springframework.web.client.RestClient
import java.lang.reflect.ParameterizedType

class ExternalApiGraphqlClient<Req : ExternalApiRequest, Res> private constructor() {

    class Builder<Req : ExternalApiRequest, Res>(
        private val builder: RestClient.Builder
    ) {

        fun endpoint(endpoint: String): Builder<Req, Res> {
            builder.baseUrl(endpoint)
            return this
        }

        fun requestFactory(httpClient: HttpClient?): Builder<Req, Res> {
            if (httpClient != null) {
                builder.requestFactory(HttpComponentsClientHttpRequestFactory(httpClient))
            }
            return this
        }

        fun requestInterceptor(vararg interceptor: ClientHttpRequestInterceptor): Builder<Req, Res> {
            interceptor.forEach { builder.requestInterceptor(it) }
            return this
        }

        fun errorHandler(vararg errorHandler: ResponseErrorHandler): Builder<Req, Res> {
            builder.defaultStatusHandler(ExternalApiCompositeErrorHandler(errorHandler.toList()))
            return this
        }

        fun build(): ExternalApiGraphqlClient<Req, Res> {
            val client = ExternalApiGraphqlClient<Req, Res>()
            client.restClient =
                ExternalApiRestClient.Builder<ExternalApiRequest, ExternalApiGraphqlResponse>(
                    builder
                ).build()
            return client
        }
    }

    companion object {
        fun <Req : ExternalApiRequest, Res> builder(builder: RestClient.Builder): Builder<Req, Res> {
            return Builder(builder)
        }
    }

    private lateinit var restClient: ExternalApiRestClient<ExternalApiRequest, ExternalApiGraphqlResponse>

    /** クエリパラメータへの変換などを担当するマッパー */
    private val objectMapper: ObjectMapper = ExternalApiObjectMapper.getInstance()

    /** ObjectMapperで、オブジェクトを変換する先のMapの型定義 */
    private val kataOfQueryParam: MapLikeType = objectMapper.typeFactory.constructMapLikeType(
        Map::class.java,
        String::class.java,
        Any::class.java
    )

    fun <T : Res> call(
        queryType: QueryType,
        operation: String,
        request: Req,
        responseClass: Class<T>,
    ): T {
        val newRequest = buildGraphqlParameter(queryType, operation, request, responseClass)

        val graphqlResponse = restClient.call(
            method = Method.POST,
            path = "",
            request = newRequest,
            responseClass = ExternalApiGraphqlResponse::class.java,
            authenticationFailureCallback = {}
        )

        return when (val response = graphqlResponse.data[operation]) {
            // リスト形式のレスポンスをデシリアライズする場合は、入れ子の型を取得して要素ひとつずつデシリアライズする
            is List<*> -> {
                // レスポンスがList<*>の場合、対応するレスポンスクラスのフィールドは1つのみであるはず
                val listField = responseClass.declaredFields.firstOrNull {
                    List::class.java.isAssignableFrom(it.type)
                }
                val listFieldType =
                    (listField?.genericType as ParameterizedType).actualTypeArguments.first() as Class<*>

                val listObject = response.map {
                    objectMapper.convertValue(it, listFieldType)
                }
                return responseClass.getConstructor(List::class.java).newInstance(listObject)
            }

            else -> objectMapper.convertValue(response, responseClass)
        }
    }

    private fun <Req : ExternalApiRequest, T> buildGraphqlParameter(
        queryType: QueryType,
        operation: String,
        request: Req,
        responseClass: Class<T>,
    ): ExternalApiGraphqlRequest {
        val requestMap = objectMapper.convertValue<Map<String, Any?>>(request, kataOfQueryParam)
        val responseProperties = responseClass.getJsonPropertyNames()
        val query = if (responseProperties.isEmpty()) {
            """
                ${queryType.value} {
                    $operation(
                        ${requestMap.format()}
                    )
                }
            """
        } else {
            """
                ${queryType.value} {
                    $operation(
                        ${requestMap.format()}
                    ) {
                        ${responseProperties.joinToString(",")}
                    }
                }
            """
        }

        return ExternalApiGraphqlRequest(
            query = query.trimIndent(),
            apiErrorType = request.getErrorType(),
        )
    }

    private fun Map<String, Any?>.format(): String {
        return this.entries.filter { it.value != null }.joinToString(",") { entry ->
            when (entry.value) {
                is String -> "${entry.key}: \"${entry.value}\""
                else -> "${entry.key}: ${entry.value}"
            }
        }
    }
}

class ExternalApiGraphqlRequest(
    val query: String,

    @JsonIgnore
    val apiErrorType: ErrorType
) : ExternalApiRequest {
    override fun getErrorType(): ErrorType {
        return apiErrorType
    }
}

class ExternalApiGraphqlResponse(
    @JsonProperty("data")
    val data: Map<String, Any?>,

    @JsonProperty("errors")
    val errors: List<GraphQLErrorResponse>?,
)

class GraphQLErrorResponse(
    @JsonProperty("message")
    val message: String,
    @JsonProperty("locations")
    val locations: List<GraphQLErrorLocation>?,
    @JsonProperty("path")
    val path: List<String>?,
    @JsonProperty("extensions")
    val extensions: Map<String, Any>?,
) {
    override fun toString(): String {
        return "{ " +
                "message: $message, " +
                "locations: ${locations?.toString()}, " +
                "path: ${path?.toString()}, " +
                "extensions: ${extensions?.toString()} " +
                "}"
    }
}

data class GraphQLErrorLocation(
    val line: Int,
    val column: Int
)

enum class QueryType(val value: String) {
    QUERY("query"), // データ取得
    MUTATION("mutation"), // データ追加, データ更新, データ削除
    SUBSCRIPTION("subscription"), // イベント監視
}
