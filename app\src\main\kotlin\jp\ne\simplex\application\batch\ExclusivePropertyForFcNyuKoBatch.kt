package jp.ne.simplex.application.batch

import jp.ne.simplex.application.model.ExclusivePropertyInfo
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.repository.aws.S3RepositoryInterface
import jp.ne.simplex.application.repository.db.BatchExecuteHistoryRepositoryInterface
import jp.ne.simplex.application.repository.file.ExclusivePropertyFileForFcNyuKoRepository
import jp.ne.simplex.application.service.ExclusivePropertyService
import jp.ne.simplex.application.service.TemporaryReservationService
import jp.ne.simplex.exception.ServerValidationException
import jp.ne.simplex.shared.MultiThreadRunBlocking.Companion.runAsyncTasks
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.nio.file.Path
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/** 新入稿システム向け先行公開バッチ */
@Profile("batch")
@Component("ExclusivePropertyForFcNyuKoBatch")
class ExclusivePropertyForFcNyuKoBatch (
    private val s3Repository: S3RepositoryInterface,
    private val exclusivePropertyFileRepository: ExclusivePropertyFileForFcNyuKoRepository,
    private val exclusivePropertyService: ExclusivePropertyService,
    @Value("\${batch.s3.exclusive-property-for-fc-nyuko-batch-backup-bucket}")
    private val bucketName: String,
    @Value("\${batch.s3.exclusive-property-for-fc-nyuko-batch-bucket}")
    private val forFcNyukobucketName: String,
    override val batchExecuteHistoryRepository: BatchExecuteHistoryRepositoryInterface,
): BatchInterface {
    override val batchType: BatchType = BatchType.EXCLUSIVE_PROPERTY_FOR_FC_NYUKO

    @Value("\${app.property.exclusive-property-data.parallel-num}")
    val exclusivePropertyDataParallelNum = 1

    companion object {
        private val log = LoggerFactory.getLogger(ExclusivePropertyForFcNyuKoBatch::class.java)
    }

    override fun executeInternal(executeOption: ExecuteOptionType?) {
        val results = getExclusivePropertyList()
        val localFilePath = exclusivePropertyFileRepository.saveExclusivePropertyForFcNyuKo(results)

        s3Repository.uploadFile(
            localFilePath,
            localFilePath.fileName.toString(),
            bucketName
        )
        // 新入稿システム向け
        val dateString = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        val outputPath: String = Path.of("v1/dk_link/daito/${dateString}/", localFilePath.fileName.toString()).toString()
        s3Repository.uploadFile(
            localFilePath,
            outputPath,
            forFcNyukobucketName
        )
    }

    override fun isAcceptableOption(executeOption: ExecuteOptionType): Boolean {
        return executeOption == ExecuteOptionType.RERUN_ONLY_SEND_FILE || executeOption == ExecuteOptionType.RERUN
    }

    private fun getExclusivePropertyList(): List<ExclusivePropertyInfo> {
        val targets = exclusivePropertyService.getExclusivePropertyInfoForFcNyuKoBatch()
        return targets
    }
}
