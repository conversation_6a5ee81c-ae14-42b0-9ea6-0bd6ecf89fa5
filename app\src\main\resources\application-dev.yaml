server:
  port: 8082

spring:
  datasource:
    url: ****************************************
    username: app
    password: simplex
    hikari:
      maximum-pool-size: 50

swagger:
  enabled: true

app:
  auth:
    jwt:
      access-token:
        validity-period: 86400
      refresh-token:
        validity-period: 86400
      cookie:
        http-only: false
        secure: false
        same-site: NONE
        max-age: 86400
    single-sign-on:
      enabled: false # ローカルでのSSO検証時はtrueに設定する
      saml:
        entity-id: http://localhost:18080/realms/propetech-dev
        dk-link-url: http://localhost:4200
        secret-id: saml_verification_cert
  log:
    # devモード起動時はTEXT形式でログ出力する（フォーマットは、logback-spring.xml参照）
    appender: console-text

mail:
  parking:
    reservation:
      send: true

external:
  use-proxy: false
  eboard:
    endpoint: http://localhost:8083/eboard
  dkportal:
    endpoint: http://localhost:8083/dkportal

aws:
  endpoint: http://localhost:4566
