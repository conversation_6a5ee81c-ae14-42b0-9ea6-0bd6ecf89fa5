package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingContractPossibility
import jp.ne.simplex.application.repository.db.ParkingContractPossibilityRepositoryInterface
import jp.ne.simplex.authentication.AuthInfo
import org.jooq.Configuration

class MockParkingContractPossibilityRepository(
    val findByIdFunc: (
        orderCode: Building.OrderCode
    ) -> ParkingContractPossibility? = { _ -> null },
    val registerFunc: (
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility,
    ) -> Unit = { _, _ -> },
    val updateFunc: (
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility,
    ) -> Unit = { _, _ -> },
) : ParkingContractPossibilityRepositoryInterface {
    override fun findBy(orderCode: Building.OrderCode): ParkingContractPossibility? {
        return findByIdFunc(orderCode)
    }

    override fun findByIdForUpdate(
        config: Configuration,
        orderCode: Building.OrderCode,
        waitSeconds: Int
    ): ParkingContractPossibility? {
        return findByIdFunc(orderCode)
    }

    override fun register(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility
    ) {
        registerFunc(requestUser, param)
    }

    override fun update(
        config: Configuration,
        requestUser: AuthInfo.RequestUser,
        param: ParkingContractPossibility
    ) {
        updateFunc(requestUser, param)
    }
}
