package jp.ne.simplex.application.controller.client.auth

import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jp.ne.simplex.application.controller.client.auth.dto.ClientLoginParkingRequest
import jp.ne.simplex.application.controller.client.auth.dto.ClientLoginParkingResponse
import jp.ne.simplex.application.controller.client.auth.dto.ClientLoginRequest
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.application.service.AuthService
import jp.ne.simplex.application.service.LoginParkingService
import jp.ne.simplex.authentication.AuthConfig
import jp.ne.simplex.authentication.JwtAuthToken
import jp.ne.simplex.openapi.ApiDefinition
import org.springframework.http.HttpHeaders
import org.springframework.http.ResponseCookie
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/auth")
class ClientAuthController(
    private val config: AuthConfig,
    private val service: AuthService,
    private val lpService: LoginParkingService,
) {

    @PostMapping("/login")
    @ApiDefinition(summary = "ログインAPI")
    fun login(
        @RequestBody request: ClientLoginRequest,
        response: HttpServletResponse
    ) {
        val token = service.login(request.toServiceInterface())

        response.withToken(config.jwt.cookie, token)

        return
    }

    @PostMapping("/login/parking-details")
    @ApiDefinition(summary = "駐車場詳細遷移API")
    fun loginParking(
        @RequestBody body: ClientLoginParkingRequest,
        request: HttpServletRequest,
        response: HttpServletResponse
    ): ClientLoginParkingResponse {
        // 認証
        val token = service.loginWithAccessKey(body.toServiceInterface())
        response.withToken(config.jwt.cookie, token)

        // 遷移先パスを取得
        val path = lpService.getPath(body.toParkingServiceInterface())
        return ClientLoginParkingResponse.of(path)
    }

    fun ssoLogin(
        employeeCode: Employee.Code, response: HttpServletResponse
    ) {
        val token = service.ssoLogin(employeeCode)
        response.withToken(config.jwt.cookie, token)
        return
    }

    private fun HttpServletResponse.withToken(
        config: AuthConfig.Jwt.CookieConfig,
        token: JwtAuthToken
    ) {
        // アクセストークンをCookieに設定
        addHeader(
            HttpHeaders.SET_COOKIE,
            ResponseCookie
                .from(AuthConfig.Jwt.COOKIE_ACCESS_TOKEN_KEY, token.accessTokenValue)
                .secure(config.secure)
                .path("/")
                .sameSite(config.getSameSiteAttribute())
                .maxAge(config.maxAge)
                .build().toString()
        )

        // リフレッシュトークンをCookieに設定
        addHeader(
            HttpHeaders.SET_COOKIE,
            ResponseCookie
                .from(AuthConfig.Jwt.COOKIE_REFRESH_TOKEN_KEY, token.refreshTokenValue)
                .httpOnly(config.httpOnly)
                .secure(config.secure)
                // TODO 適切なパスを設定する必要あり
                .path("/api")
                .sameSite(config.getSameSiteAttribute())
                .maxAge(config.maxAge)
                .build().toString()
        )
    }
}
