package jp.ne.simplex.application.repository.external.dkportal.config

import com.fasterxml.jackson.databind.ObjectMapper
import jp.ne.simplex.application.repository.external.ExternalApiGraphqlResponse
import jp.ne.simplex.application.repository.external.ExternalApiObjectMapper
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.exception.ExternalApiServerException
import org.springframework.http.HttpMethod
import org.springframework.http.client.ClientHttpResponse
import org.springframework.web.client.ResponseErrorHandler
import java.net.URI

class DKPortalDefaultErrorHandler : ResponseErrorHandler {
    private val objectMapper: ObjectMapper = ExternalApiObjectMapper.getInstance()

    override fun hasError(response: ClientHttpResponse): Boolean {
        if (response.statusCode.is4xxClientError || response.statusCode.is5xxServerError) {
            return true
        }
        val body = objectMapper.readValue(response.body, ExternalApiGraphqlResponse::class.java)
        return body.errors?.isNotEmpty() == true
    }

    override fun handleError(url: URI, method: HttpMethod, response: ClientHttpResponse) {
        val body =
            try {
                objectMapper.readValue(response.body, ExternalApiGraphqlResponse::class.java)
            } catch (_: Exception) {
                throw ExternalApiServerException(
                    ErrorType.DK_PORTAL_API_ERROR,
                    ErrorMessage.DK_PORTAL_RECEIVED_UNEXPECTED_RESPONSE.format()
                )
            }

        throw ExternalApiServerException(
            ErrorType.DK_PORTAL_API_ERROR,
            ErrorMessage.DK_PORTAL_RECEIVED_ERROR_RESPONSE.format(body.errors.toString())
        )
    }
}
