services:
  db:
    build:
      context: db
      dockerfile: Dockerfile
    container_name: propetech-server-db
    command: postgres -c log_destination=stderr -c log_statement=all -c log_connections=on -c log_disconnections=on
    logging:
      options:
        max-size: '10k'
        max-file: '5'
    restart: always
    ports:
      - '5433:5432'
    networks:
      - local

  app:
    build:
      context: app
      dockerfile: Dockerfile
    container_name: propetech-server-app
    tty: true
    ports:
      - '8082:8082'
    networks:
      - local
    environment:
      - SPRING_DATASOURCE_URL=*********************************************

  mock:
    build:
      context: mock
      dockerfile: Dockerfile
    container_name: propetech-server-mock
    tty: true
    ports:
      - '8083:8083'
    networks:
      - local

networks:
  local:
    driver: bridge
