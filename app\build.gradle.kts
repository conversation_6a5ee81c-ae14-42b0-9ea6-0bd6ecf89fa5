import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent
import org.springframework.boot.gradle.tasks.run.BootRun

val jooqVersion: String by project
val flywayVersion: String by project

plugins {
    kotlin("plugin.spring") version "1.9.25"
    id("org.springframework.boot") version "3.4.4"
    id("io.spring.dependency-management") version "1.1.7"
    id("org.springdoc.openapi-gradle-plugin") version "1.9.0"
}

springBoot {
    mainClass.set("jp.ne.simplex.AppApplicationKt")
}

repositories {
    mavenCentral()
    maven {
        url = uri("https://repository.gael-systems.com/repository/public/")
    }
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-reflect")

    // Spring WEB
    implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("org.springframework.security:spring-security-saml2-service-provider")
    implementation("org.hibernate.validator:hibernate-validator:7.0.5.Final")

    // HTTP Client
    implementation("org.apache.httpcomponents.client5:httpclient5:5.4.2")

    // HealthCheck
    implementation("org.springframework.boot:spring-boot-starter-actuator")

    // Database
    implementation(project(":db"))
    implementation("org.jooq:jooq:$jooqVersion")
    implementation("org.flywaydb:flyway-core:$flywayVersion")
    implementation("org.flywaydb:flyway-database-postgresql:$flywayVersion")

    // AWS SDK
    implementation("software.amazon.awssdk:apache-client:2.29.47")
    implementation("software.amazon.awssdk:secretsmanager:2.29.47")
    implementation("software.amazon.awssdk:s3:2.31.17")
    implementation("software.amazon.awssdk:ses:2.31.60")

    //SFTP
    implementation("com.github.mwiede:jsch:0.2.25")

    //FTP
    implementation("commons-net:commons-net:3.11.1")
    // Open API
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-api:2.8.5")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.5")

    // Logback（ログをJSON出力するためのエンコーダー）
    implementation("net.logstash.logback:logstash-logback-encoder:7.4")

    // jwt
    implementation("com.auth0:java-jwt:4.4.0")

    // Mapper
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")

    // coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor:1.8.0")

    // icu4j
    implementation("com.ibm.icu:icu4j:77.1")

    // Test
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("com.tngtech.archunit:archunit:1.4.0")
    testImplementation("com.icegreen:greenmail:2.1.3")

    // Kotest
    testImplementation("io.kotest:kotest-runner-junit5-jvm:5.9.1")
}

openApi {
    apiDocsUrl.set("http://localhost:8082/api/v2/api-docs")
    outputFileName.set("api-docs.yaml")
    // 以下の設定を追加して、アプリケーション起動時の設定と一致させる
    waitTimeInSeconds.set(30)
    customBootRun {
        args.set(
            listOf(
                "--spring.profiles.active=dev",
            )
        )
    }
}

tasks.getByName<Jar>("jar") {
    enabled = false
}

tasks.withType<Test> {
    useJUnitPlatform()
    systemProperty("spring.profiles.active", "test")
    dependsOn(rootProject.tasks["buildTestContainer"])

    testLogging {
        events(TestLogEvent.PASSED, TestLogEvent.FAILED, TestLogEvent.SKIPPED)
        exceptionFormat = TestExceptionFormat.FULL
        showCauses = true
        showExceptions = true
        showStackTraces = true
    }
}

tasks.register("unitTest", Test::class) {
    group = "verification"

    useJUnitPlatform {
        excludeTags("integration")
    }
    systemProperty("spring.profiles.active", "test")
    dependsOn(rootProject.tasks["buildTestContainer"])

    testLogging {
        events(TestLogEvent.PASSED, TestLogEvent.FAILED, TestLogEvent.SKIPPED)
        exceptionFormat = TestExceptionFormat.FULL
        showCauses = true
        showExceptions = true
        showStackTraces = true
    }
}

tasks.register("integrationTest", Test::class) {
    group = "verification"

    useJUnitPlatform {
        includeTags("integration")
    }
    systemProperty("spring.profiles.active", "test")
    dependsOn(rootProject.tasks["buildTestContainer"])

    testLogging {
        events(TestLogEvent.PASSED, TestLogEvent.FAILED, TestLogEvent.SKIPPED)
        exceptionFormat = TestExceptionFormat.FULL
        showCauses = true
        showExceptions = true
        showStackTraces = true
    }
}

tasks.register("bootRunDev", BootRun::class) {
    group = "application"
    description = "Runs this project as a Spring Boot application with the dev profile"
    classpath = sourceSets.main.get().runtimeClasspath
    mainClass.set("jp.ne.simplex.AppApplicationKt")
    systemProperty("spring.profiles.active", "dev")
    // LocalStack接続用の環境変数
    environment("AWS_ACCESS_KEY_ID", "dummy")
    environment("AWS_SECRET_ACCESS_KEY", "dummy")
}

tasks.register("bootBatchRunDev", BootRun::class) {
    group = "application"
    description = "Runs this project as a Spring Boot batch application with the dev profile"
    classpath = sourceSets.main.get().runtimeClasspath
    mainClass.set("jp.ne.simplex.BatchApplicationKt")
    args(System.getenv("BATCH_NAME") ?: "NoBatchName")
    if (System.getenv("EXECUTE_OPTION") != null) {
        args(System.getenv("EXECUTE_OPTION"))
    }
    systemProperty("spring.profiles.active", "batch,dev")
    // LocalStack接続用の環境変数
    environment("AWS_ACCESS_KEY_ID", "dummy")
    environment("AWS_SECRET_ACCESS_KEY", "dummy")
}

tasks.register("bootBatchRun", BootRun::class) {
    group = "application"
    description = "Runs this project as a Spring Boot batch application"
    classpath = sourceSets.main.get().runtimeClasspath
    mainClass.set("jp.ne.simplex.BatchApplicationKt")
    args(System.getenv("BATCH_NAME") ?: "NoBatchName")
    if (System.getenv("EXECUTE_OPTION") != null) {
        args(System.getenv("EXECUTE_OPTION"))
    }
    systemProperty("spring.profiles.active", "batch")
}

tasks.register("buildBatch") {
    group = "build"
    description = "Build the project with a batch main class"
    doFirst {
        springBoot.mainClass.set("jp.ne.simplex.BatchApplicationKt")
    }

    finalizedBy("bootJar")
}
