import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    kotlin("jvm") version "1.9.25"
}

allprojects {
    group = "jp.ne.simplex"
    version = "0.0.1-SNAPSHOT"
    repositories {
        mavenCentral()
        maven {
        url = uri("https://repository.gael-systems.com/repository/public/")
    }
}
}

subprojects {
    apply(plugin = "org.jetbrains.kotlin.jvm")

    tasks.withType<KotlinCompile> {
        kotlinOptions {
            jvmTarget = "17"
        }
    }
}

tasks.register("prepare") {
    description = "copy required resources from propetech-handover repository."

    doFirst {
        exec {
            commandLine("mkdir", "-p", "db/src/test/resources")
        }
    }
    doLast {
        exec {
            commandLine("bash", "-c", "cp -rp ../propetech-handover/postgres/* db/src/test/resources/")
        }
    }
}

tasks.register("buildTestContainer") {
    description = "Build the Docker image for launch database container."

    dependsOn("prepare")
    mustRunAfter("prepare")

    doLast {
        // Dockerイメージの名前とタグを指定
        val imageName = "testcontainers/local-postgres"
        val dockerfilePath = "db/Dockerfile"
        val buildContext = "db"

        val result = providers.exec {
            commandLine(
                "docker",
                "build",
                "--no-cache",
                "-t",
                imageName,
                "-f",
                dockerfilePath,
                buildContext
            )
            isIgnoreExitValue = true // エラーが発生してもビルドを止めない
        }
        println(result.standardOutput.asText.get())
        println(result.standardError.asText.get())
    }
}
