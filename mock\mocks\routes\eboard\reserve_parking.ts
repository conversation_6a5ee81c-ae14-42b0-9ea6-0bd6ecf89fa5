import {
  EboardResponseType,
  eboardResponseMapping,
} from '../../shared/eboard/eboard_response_type';
import { keysToLowerCase } from '../../shared/shared_function';
import isFullwidthCodePoint from 'is-fullwidth-code-point';

/**
 * いい物件ボード/駐車場予約API
 *
 * サンプルリクエスト
 * curl -H "Authorization: Bearer 5cdd2e4e-01eb-4395-b2cc-fe29e0eae914" -H "Content-Type:application/x-www-form-urlencoded" -X POST "http://localhost:8083/eboard/newEboardApi/reserve_parking?tatemonoCd=000000001&status=1&uketukeDate=20250101&reserveName=aaa&tel=090-1234-5678&tantoName=bbb&bikou=ccc&id=100"
 */
export default [
  {
    id: 'eboard_reserve_parking',
    url: '/eboard/newEboardApi/reserve_parking',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: (req, res) => {
            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const responseType = checkParameter(req);

            if (responseType != EboardResponseType.SUCCESS) {
              res.status(400);
              res.send({
                result: responseType,
                message: eboardResponseMapping[responseType],
              });

              return;
            }

            // status: 200 の場合
            res.status(200);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
              updateResult: 'OK',
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            const responseType = EboardResponseType.OTHER_ERROR;

            res.status(500);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する
 * responseCodeMapping（api_response_specification.js）定義のエラーコードを返却する
 * @param {*} req
 * @returns {EboardResponseType}
 */
function checkParameter(req): EboardResponseType {
  const headers = keysToLowerCase(req.headers);
  if (headers['content-type'] != 'application/x-www-form-urlencoded') {
    return EboardResponseType.PARAMETER_INVALID;
  }

  const { tatemonoCd, parkingCd, status, uketukeDate, reserveName, tel, tantoName, bikou, id } = req.query;
  console.log(req.query)

  if (!tatemonoCd || !status || !uketukeDate || !id) {
    return EboardResponseType.PARAMETER_MISSING;
  }

  if (tatemonoCd.length != 9
    || (parkingCd && parkingCd.length != 3)
    || status.length != 1
    || uketukeDate.length != 8
    || (reserveName && reserveName.length > 12　&& isFullWidth(reserveName))
    || (tel && tel.length > 15)
    || (tantoName && tantoName.length > 12 && isFullWidth(tantoName))
    || (bikou && bikou.length > 50 && isFullWidth(bikou))
    || id.length < 3
  ) {
    return EboardResponseType.PARAMETER_INVALID;
  }

  // 状態区分はは0:受付(新規) 1:受付(更新) 2:契約済 3:ｷｬﾝｾﾙ 4:WP(ウェルカムパーク・新規)のみ受け付ける
  if (!['0', '1', '2', '3', '4'].includes(status)) {
    return EboardResponseType.PARAMETER_INVALID;
  }

  return EboardResponseType.SUCCESS
}

function isFullWidth(val: string): boolean {
  return Array.from(val).every(char => isFullwidthCodePoint(char.codePointAt(0)!));
}
