package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.ParkingLot
import jp.ne.simplex.application.repository.db.ParkingRepositoryInterface

class MockParkingRepository(
    val isParkingExistFunc: (parkingLotId: ParkingLot.Id) -> Boolean = { _ -> true },
    val getAllOrderCodesFunc: () -> List<Building.OrderCode> = { -> emptyList() },
    val getRoomCountByOrderCodeFunc: (orderCode: Building.OrderCode) -> Int = { _ -> 0 },
    val getVacancyRoomCountByOrderCodeFunc: (orderCode: Building.OrderCode) -> Int = { _ -> 0 },
    val getThirtyFiveYearBulkBuildingContractFunc: (orderCode: Building.OrderCode) -> List<Building.Code> = { _ -> emptyList() },
    val findParkingLotIdMapForWelcomeParkFunc: () -> Map<Boolean, Set<ParkingLot.Id>> = { -> emptyMap() },
    val getLocalDisplayNumberFunc: () -> String? = { -> null },
) : ParkingRepositoryInterface {

    override fun isParkingExist(parkingLotId: ParkingLot.Id): Boolean {
        return isParkingExistFunc(parkingLotId)
    }

    override fun getAllOrderCodes(): List<Building.OrderCode> {
        return getAllOrderCodesFunc()
    }

    override fun getRoomCountByOrderCode(orderCode: Building.OrderCode): Int {
        return getRoomCountByOrderCodeFunc(orderCode)
    }

    override fun getVacancyRoomCountByOrderCode(orderCode: Building.OrderCode): Int {
        return getVacancyRoomCountByOrderCodeFunc(orderCode)
    }

    override fun getThirtyFiveYearBulkBuildingContract(orderCode: Building.OrderCode): List<Building.Code> {
        return getThirtyFiveYearBulkBuildingContractFunc(orderCode)
    }

    override fun findParkingLotIdMapForWelcomePark(): Map<Boolean, Set<ParkingLot.Id>> {
        return findParkingLotIdMapForWelcomeParkFunc()
    }

    override fun getLocalDisplayNumber(
        buildingCode: Building.Code,
        parkingLotCode: ParkingLot.Code?
    ): String? {
        return getLocalDisplayNumberFunc()
    }
}
