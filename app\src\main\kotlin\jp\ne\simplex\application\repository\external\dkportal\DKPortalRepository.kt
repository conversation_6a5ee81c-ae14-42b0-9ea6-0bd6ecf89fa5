package jp.ne.simplex.application.repository.external.dkportal

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.external.ExternalApiGraphqlClient
import jp.ne.simplex.application.repository.external.QueryType
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalRequest
import jp.ne.simplex.application.repository.external.dkportal.config.DKPortalResponse
import jp.ne.simplex.application.repository.external.dkportal.dto.*
import jp.ne.simplex.shared.MultiThreadRunBlocking.Companion.runAsyncTasks
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Repository
import java.util.*

@Repository
class DKPortalRepository(
    @Qualifier("dkportalGraphqlClient")
    private val graphqlClient: ExternalApiGraphqlClient<DKPortalRequest, DKPortalResponse>,
) : DKPortalRepositoryInterface {

    companion object {
        private val log = LoggerFactory.getLogger(DKPortalRepository::class.java)
    }

    override fun updateAdff(
        updateAdFfList: List<UpdatePropertyMaintenance.AdFf>
    ): UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>> {
        val successIds = Collections.synchronizedList(mutableListOf<Property.Id>())
        val failedIds = Collections.synchronizedList(mutableListOf<Property.Id>())

        runAsyncTasks(maxThreads = 10, tasks = updateAdFfList) { task ->
            runCatching {
                val request = DKPortalUpdateAdFfRequest.of(task)

                graphqlClient.call(
                    queryType = QueryType.MUTATION,
                    operation = request.getDKPortalOperationName().value,
                    request = request,
                    responseClass = DKPortalUpdateAdFfResponse::class.java,
                ).throwIfReceivedError()
            }
                .onSuccess {
                    successIds.add(task.id)
                }
                .onFailure {
                    log.warn("[${task.id}] ${it.message}")
                    failedIds.add(task.id)
                }
        }

        return UpdatePropertyMaintenance.Result(successIds, failedIds)
    }

    override fun updateRoomStatus(
        updatePublishStatus: List<UpdatePropertyMaintenance.PublishStatusWithUpState>
    ): UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>> {
        val successIds = Collections.synchronizedList(mutableListOf<Property.Id>())
        val failedIds = Collections.synchronizedList(mutableListOf<Property.Id>())

        runAsyncTasks(maxThreads = 10, tasks = updatePublishStatus) { task ->
            runCatching {
                val request = DKPortalUpdateRoomStatusRequest.of(task)

                graphqlClient.call(
                    queryType = QueryType.MUTATION,
                    operation = request.getDKPortalOperationName().value,
                    request = request,
                    responseClass = DKPortalUpdateRoomStatusResponse::class.java,
                ).throwIfReceivedError()
            }
                .onSuccess {
                    successIds.add(task.id)
                }
                .onFailure {
                    log.warn("[${task.id}] ${it.message}")
                    failedIds.add(task.id)
                }
        }

        return UpdatePropertyMaintenance.Result(successIds, failedIds)
    }

    override fun updateParkingLot(status: ParkingContractPossibility) {

        val request = DKPortalUpdateParkingLotRequest.of(status)

        graphqlClient.call(
            queryType = QueryType.MUTATION,
            operation = request.getDKPortalOperationName().value,
            request = request,
            responseClass = DKPortalUpdateParkingLotResponse::class.java,
        ).throwIfReceivedError()
    }

    override fun isNeedUpdateParkingLot(
        oldStatus: ParkingContractPossibility,
        newStatus: ParkingContractPossibility
    ): Boolean {
        if (oldStatus.orderCode != newStatus.orderCode) {
            log.warn("Parking lot update check failed: Order code mismatch detected.")
            return false
        }
        return !DKPortalUpdateParkingLotRequest.equals(oldStatus, newStatus)
    }

    override fun createExclusive(
        property: Property,
        record: ExclusivePropertyAction.Record
    ) {
        val request = DKPortalCreateExclusiveRequest.of(property, record)

        graphqlClient.call(
            queryType = QueryType.MUTATION,
            operation = request.getDKPortalOperationName().value,
            request = request,
            responseClass = DKPortalCreateExclusiveListResponse::class.java,
        )
    }

    override fun updateExclusive(
        property: Property,
        exclusiveId: ExclusiveProperty.Id,
        record: ExclusivePropertyAction.Record
    ) {
        val request = DKPortalUpdateExclusiveRequest.of(property, exclusiveId, record)

        return graphqlClient.call(
            queryType = QueryType.MUTATION,
            operation = request.getDKPortalOperationName().value,
            request = request,
            responseClass = DKPortalUpdateExclusiveResponse::class.java,
        ).throwIfReceivedError()
    }

    override fun deleteExclusive(property: Property, exclusiveId: ExclusiveProperty.Id) {
        val request = DKPortalDeleteExclusiveRequest.of(property, exclusiveId)

        return graphqlClient.call(
            queryType = QueryType.MUTATION,
            operation = request.getDKPortalOperationName().value,
            request = request,
            responseClass = DKPortalDeleteExclusiveResponse::class.java,
        ).throwIfReceivedError()
    }
}

interface DKPortalRepositoryInterface {
    /** 物件メンテナンス（AD・FF） */
    fun updateAdff(
        updateAdFfList: List<UpdatePropertyMaintenance.AdFf>
    ): UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>>

    /** 物件メンテナンス（部屋情報更新） */
    fun updateRoomStatus(
        updatePublishStatus: List<UpdatePropertyMaintenance.PublishStatusWithUpState>
    ): UpdatePropertyMaintenance.Result<List<Property.Id>, List<Property.Id>>

    /** 駐車場検索用情報連携 */
    fun updateParkingLot(status: ParkingContractPossibility)

    /** 駐車場検索用情報連携要否判定 */
    fun isNeedUpdateParkingLot(
        oldStatus: ParkingContractPossibility,
        newStatus: ParkingContractPossibility
    ): Boolean

    /** 先行物件登録 */
    fun createExclusive(
        property: Property,
        record: ExclusivePropertyAction.Record
    )

    /** 先行物件更新 */
    fun updateExclusive(
        property: Property,
        exclusiveId: ExclusiveProperty.Id,
        record: ExclusivePropertyAction.Record
    )

    /** 先行物件削除 */
    fun deleteExclusive(property: Property, exclusiveId: ExclusiveProperty.Id)
}
