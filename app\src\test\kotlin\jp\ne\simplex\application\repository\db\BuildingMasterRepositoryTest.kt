package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Building
import jp.ne.simplex.db.jooq.gen.tables.references.BUILDING_INFO_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.BUILDING_MASTER
import jp.ne.simplex.stub.stubBuildingInfoMasterPojo
import jp.ne.simplex.stub.stubBuildingMasterPojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class BuildingMasterRepositoryTest : AbstractTestContainerTest() {
    private lateinit var repository: BuildingMasterRepository

    override fun beforeEach() {
        repository = BuildingMasterRepository(dslContext)
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(BUILDING_MASTER, BUILDING_INFO_MASTER)
    }

    @Nested
    @DisplayName("受注コードの紐づく建物コード取得の検証")
    inner class Scenario1 {

        @BeforeEach
        fun setup() {
            dslContext.saveBuildingMasterPojo(
                stubBuildingMasterPojo(
                    buildingCode = "000024301",
                ),
                stubBuildingMasterPojo(
                    buildingCode = "000014301",
                ),
                stubBuildingMasterPojo(
                    buildingCode = "000014401",
                ),
                stubBuildingMasterPojo(
                    buildingCode = "000024303",
                ),
                stubBuildingMasterPojo(
                    buildingCode = "000024302",
                ),
            )
            dslContext.saveBuildingInfoMasterPojo(
                stubBuildingInfoMasterPojo(
                    buildingCode = "000024302",
                ),
            )
        }

        @Test
        @DisplayName("入居中建物_建物コードから建物情報を取得できること")
        fun test01() {
            val buildingCode = "000024301"
            val building = repository.findBy(Building.Code.of(buildingCode))
            assert(building != null) { "建物情報が取得できませんでした: $buildingCode" }
            assert(building?.code?.value == buildingCode) { "建物コードが一致しません: $buildingCode" }
        }

        @Test
        @DisplayName("募集中建物_建物コードから建物情報を取得できること")
        fun test02() {
            val buildingCode = "000024302"
            val building = repository.findActiveBy(Building.Code.of(buildingCode))
            assert(building != null) { "建物情報が取得できませんでした: $buildingCode" }
            assert(building?.code?.value == buildingCode) { "建物コードが一致しません: $buildingCode" }
        }

    }

}
