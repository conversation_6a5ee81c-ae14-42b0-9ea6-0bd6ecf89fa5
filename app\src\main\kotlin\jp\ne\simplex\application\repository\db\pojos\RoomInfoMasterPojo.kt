package jp.ne.simplex.application.repository.db.pojos

import jp.ne.simplex.application.model.Building
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.application.model.Property
import jp.ne.simplex.application.model.Room
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import org.slf4j.LoggerFactory
import java.time.LocalDate

// Jooqによって生成された jp.ne.simplex.db.jooq.gen.tables.pojos.RoomInfoMasterPojo は、
// 引数超過（カラム数が多すぎる）で、インスタンス化した際に実行時エラーが発生するため、特別に Pojo を定義している
data class RoomInfoMasterPojo(
    val recordType: String,
    val propertyCdType: String,
    val propertyBuildingCd: String,
    val propertyRoomCd: String,
    val propertyType: String? = null,
    val recordStatusType: String? = null,
    val customerCompletionFlag: String? = null,
    val roomNumber: String? = null,
    val moveInApplicationDate: String? = null,
    val direction: String? = null,
    val buildingName: String? = null,
    val changeDivision: String? = null,
    val applicationScheduledDate: String? = null,
    val marketingBranchOfficeCd: String? = null
) {

    companion object {
        private val log = LoggerFactory.getLogger(RoomInfoMasterPojo::class.java)
    }

    fun getProperty(): Property? {
        return try {
            Property(
                id = Property.Id(
                    buildingCode = Building.Code.of(propertyBuildingCd),
                    roomCode = Room.Code.of(propertyRoomCd),
                ),
                roomNumber = this.roomNumber?.let { Room.Number.of(it) },
                buildingType = this.getBuildingType(),
                recordStatusType = this.getRecordStatusType(),
                customerCompletionFlag = (customerCompletionFlag != null && customerCompletionFlag == "1"),
                direction = direction,
                moveInApplicationDate = moveInApplicationDateToLocalDate(moveInApplicationDate),
                changeDivision = changeDivision,
                applicationScheduledDate = moveInApplicationDateToLocalDate(applicationScheduledDate),
                marketingBranchOfficeCode = this.marketingBranchOfficeCd
                    ?.let { Office.Code.of(it) }
            )
        } catch (_: Exception) {
            // この catch 句は、基本想定していない。不正データの場合でしか発生し得ない想定
            log.warn("Failed to deserialize RoomInfoMaster record. $this")
            return null
        }
    }

    private fun moveInApplicationDateToLocalDate(moveInApplicationDate: String?): LocalDate? {
        return try {
            moveInApplicationDate?.yyyyMMdd()
        } catch (_: Exception) {
            null
        }
    }

    private fun getBuildingType(): Building.Type? {
        return propertyType?.let { value ->
            Building.Type.entries.find { it.code == value }
        }
    }

    private fun getRecordStatusType(): Property.RecordStatusType? {
        return recordStatusType?.let { value ->
            Property.RecordStatusType.entries.find { it.code == value }
        }
    }
}
