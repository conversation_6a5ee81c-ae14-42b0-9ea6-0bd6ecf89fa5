package jp.ne.simplex.application.repository.db

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.repository.db.pojos.ExclusivePropertiesSearchPojo
import jp.ne.simplex.application.repository.db.pojos.ExclusivePropertyForECloudBatchPojo
import jp.ne.simplex.application.repository.db.pojos.ExclusivePropertyForFcNyuKoBatchPojo
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.db.jooq.gen.tables.references.*
import jp.ne.simplex.shared.BooleanExtension.Companion.toInt
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmmss
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.enums.PerPage
import jp.ne.simplex.shared.enums.SortOrder
import org.jooq.*
import org.jooq.impl.DSL
import org.jooq.impl.DSL.concat
import org.springframework.stereotype.Repository
import java.time.LocalDate
import java.time.LocalDateTime

@Repository
class ExclusivePropertyRepository(private val context: DSLContext) {

    /** 先行公開新規登録 */
    fun register(
        config: Configuration,
        authInfo: AuthInfo.Jwt,
        property: Property,
        record: ExclusivePropertyAction.Record
    ) {
        val currentDateTime = LocalDateTime.now()
        val exclusiveTarget = record.exclusiveTargetWithId.target

        config.dsl().insertInto(EXCLUSIVE_PROPERTY)
            .set(EXCLUSIVE_PROPERTY.ID, record.exclusiveTargetWithId.id.value)
            .set(EXCLUSIVE_PROPERTY.BUILDING_CODE, record.propertyId.buildingCode.value)
            .set(EXCLUSIVE_PROPERTY.ROOM_CODE, record.propertyId.roomCode.value)
            .set(
                EXCLUSIVE_PROPERTY.SALES_OFFICE_CODE,
                property.marketingBranchOfficeCode!!.value
            )
            .set(EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM, record.exclusiveRange.from.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.EXCLUSIVE_TO, record.exclusiveRange.to.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.COMPANY_TYPE, exclusiveTarget.companyType.value.toByte())
            .set(EXCLUSIVE_PROPERTY.CREATION_DATE, currentDateTime.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.CREATION_TIME, currentDateTime.HHmmss().toInt())
            .set(EXCLUSIVE_PROPERTY.CREATOR, authInfo.getRequestUser().value)
            .set(EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG, false.toInt().toString())
            .set(EXCLUSIVE_PROPERTY.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.UPDATE_TIME, currentDateTime.HHmmss().toInt())
            .set(EXCLUSIVE_PROPERTY.UPDATER, authInfo.getRequestUser().value)
            .set(EXCLUSIVE_PROPERTY.DELETE_FLAG, false.toInt().toString())
            .execute()

        if (exclusiveTarget.eCode != null) {
            config.dsl().insertInto(EXCLUSIVE_PROPERTY_E_CODE)
                .set(EXCLUSIVE_PROPERTY_E_CODE.ID, record.exclusiveTargetWithId.id.value)
                .set(EXCLUSIVE_PROPERTY_E_CODE.E_CODE, exclusiveTarget.eCode.value)
                .set(EXCLUSIVE_PROPERTY_E_CODE.CREATION_DATE, currentDateTime.yyyyMMdd().toInt())
                .set(EXCLUSIVE_PROPERTY_E_CODE.CREATION_TIME, currentDateTime.HHmmss().toInt())
                .set(EXCLUSIVE_PROPERTY_E_CODE.CREATOR, authInfo.getRequestUser().value)
                .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
                .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATE_TIME, currentDateTime.HHmmss().toInt())
                .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATER, authInfo.getRequestUser().value)
                .set(EXCLUSIVE_PROPERTY.DELETE_FLAG, false.toInt().toString())
                .execute()
        }
        return
    }

    /** 先行公開修正 */
    fun update(
        config: Configuration,
        authInfo: AuthInfo.Jwt,
        targetId: ExclusiveProperty.Id,
        record: ExclusivePropertyAction.Record,
    ) {
        val currentDateTime = LocalDateTime.now()
        val exclusiveTarget = record.exclusiveTargetWithId.target

        config.dsl().update(EXCLUSIVE_PROPERTY)
            .set(EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM, record.exclusiveRange.from.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.EXCLUSIVE_TO, record.exclusiveRange.to.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.COMPANY_TYPE, exclusiveTarget.companyType.value.toByte())
            .set(EXCLUSIVE_PROPERTY.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.UPDATE_TIME, currentDateTime.HHmmss().toInt())
            .set(EXCLUSIVE_PROPERTY.UPDATER, authInfo.getRequestUser().value)
            .where(EXCLUSIVE_PROPERTY.ID.eq(targetId.value))
            .execute()

        if (exclusiveTarget.eCode != null) {
            config.dsl().update(EXCLUSIVE_PROPERTY_E_CODE)
                .set(EXCLUSIVE_PROPERTY_E_CODE.E_CODE, exclusiveTarget.eCode.value)
                .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
                .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATE_TIME, currentDateTime.HHmmss().toInt())
                .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATER, authInfo.getRequestUser().value)
                .where(EXCLUSIVE_PROPERTY_E_CODE.ID.eq(targetId.value))
                .execute()
        }
        return
    }

    /** 先行公開解除 */
    fun cancel(config: Configuration, authInfo: AuthInfo.Jwt, info: ExclusivePropertyInfo) {
        val currentDateTime = LocalDateTime.now()

        config.dsl().update(EXCLUSIVE_PROPERTY)
            .let {
                if (info.isBeforePublished()) {
                    // 先行公開前の場合、削除フラグを立てる
                    it.set(EXCLUSIVE_PROPERTY.DELETE_FLAG, true.toInt().toString())
                } else {
                    // 先行公開中の場合、終了日を本日かつ早期終了フラグを立てる
                    it.set(EXCLUSIVE_PROPERTY.EXCLUSIVE_TO, currentDateTime.yyyyMMdd().toInt())
                    it.set(EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG, true.toInt().toString())
                }
            }
            .set(EXCLUSIVE_PROPERTY.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY.UPDATE_TIME, currentDateTime.HHmmss().toInt())
            .set(EXCLUSIVE_PROPERTY.UPDATER, authInfo.getRequestUser().value)
            .where(EXCLUSIVE_PROPERTY.ID.eq(info.id.value))
            .execute()

        config.dsl().update(EXCLUSIVE_PROPERTY_E_CODE)
            .set(EXCLUSIVE_PROPERTY_E_CODE.DELETE_FLAG, true.toInt().toString())
            .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATE_DATE, currentDateTime.yyyyMMdd().toInt())
            .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATE_TIME, currentDateTime.HHmmss().toInt())
            .set(EXCLUSIVE_PROPERTY_E_CODE.UPDATER, authInfo.getRequestUser().value)
            .where(EXCLUSIVE_PROPERTY_E_CODE.ID.eq(info.id.value))
            .execute()
    }

    /** IDによる先行公開検索 */
    fun listBy(
        idList: List<ExclusiveProperty.Id>,
        comeFrom: ComeFromType = ComeFromType.Default
    ): List<ExclusivePropertyInfo> {
        return searchWithoutPaging(null, idList, comeFrom)
    }

    /** 物件IDによる先行公開検索 */
    fun listBy(propertyId: Property.Id): List<ExclusivePropertyInfo> {
        return searchWithoutPaging(propertyId, null)
    }

    /** 先行公開検索（ページングなし） */
    private fun searchWithoutPaging(
        propertyId: Property.Id?,
        idList: List<ExclusiveProperty.Id>?,
        comeFrom: ComeFromType = ComeFromType.Default,
    ): List<ExclusivePropertyInfo> {
        val searchCondition = ExclusivePropertiesSearch(
            page = 1, perPage = PerPage.FIFTY, // ページングは無視する
            sortBy = emptyList(),
            sortOrder = SortOrder.ASC,
            idList = idList,
            buildingCode = propertyId?.buildingCode?.value,
            roomCode = propertyId?.roomCode?.value,
            exclusiveFrom = null,
            exclusiveTo = null,
            exclusiveTarget = null,
            listingSituationTypeList = emptyList(),
            companyTypeList = null,
        )
        return search(searchCondition, ignorePaging = true, comeFrom = comeFrom).first
    }

    /** 先行公開検索 */
    fun search(
        parameter: ExclusivePropertiesSearch,
        ignorePaging: Boolean = false,
        comeFrom: ComeFromType = ComeFromType.Default,
    ): Pair<List<ExclusivePropertyInfo>, Int> {
        //フロントエンド側はエンプティステートを表示したい、エラーポップアップを表示したくない
        if (parameter.buildingCode != null && parameter.buildingCode.length > Building.Code.LENGTH) {
            return Pair(
                emptyList<ExclusivePropertyInfo>(),
                0
            )
        }

        if (parameter.roomCode != null && parameter.roomCode.length > Room.Code.LENGTH) {
            return Pair(
                emptyList<ExclusivePropertyInfo>(),
                0
            )
        }

        val companyTypeSortField = DSL
            .choose(EXCLUSIVE_PROPERTY.COMPANY_TYPE)
            .`when`(1, 1)
            .`when`(2, 2)
            .`when`(0, 3)
            .otherwise(4)
            .`as`("company_type_sort")

        val selectTargets =
            listOf<Field<*>>(
                EXCLUSIVE_PROPERTY.ID,
                EXCLUSIVE_PROPERTY.BUILDING_CODE,
                EXCLUSIVE_PROPERTY.ROOM_CODE,
                EXCLUSIVE_PROPERTY.SALES_OFFICE_CODE,
                EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM,
                EXCLUSIVE_PROPERTY.EXCLUSIVE_TO,
                EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG,
                EXCLUSIVE_PROPERTY.CREATION_DATE,
                EMPLOYEE_MASTER.NAME_KANJI.`as`("CREATOR"),
                EXCLUSIVE_PROPERTY.UPDATE_DATE,
                EMPLOYEE_MASTER.`as`("EM_FOR_UPDATER").NAME_KANJI.`as`("UPDATER"),
                EXCLUSIVE_PROPERTY.COMPANY_TYPE,
                concat(
                    AGENT.CHUKAI_GYOSHAMEI_KANJI,
                    AGENT.HONSHITENMEI_KANJI,
                ).`as`("EXCLUSIVE_TARGET"),
                BUILDING_INFO_MASTER.PROPERTY_NAME,
                ROOM_INFO_MASTER.ROOM_NUMBER,
                EXCLUSIVE_PROPERTY_E_CODE.E_CODE,
                // 新たに追加されたソートフィールド（デフォルトソート用
                companyTypeSortField,
                EXCLUSIVE_PROPERTY.DELETE_FLAG,
            )
        val subQueryWhereConditions =
            buildSubQueryWhereConditions(parameter, ignorePaging, comeFrom)
        val subQuery = buildSubQuery(
            selectTargets,
            subQueryWhereConditions,
        )
        val whereConditions =
            buildWhereConditions(parameter, subQuery)
        val query = buildQuery(
            subQuery,
            parameter,
            whereConditions,
        )

        val pojoList = if (ignorePaging) {
            query.fetchInto(ExclusivePropertiesSearchPojo::class.java)
        } else {
            query.offset((parameter.page - 1) * parameter.perPage.value)
                .limit(parameter.perPage.value).fetchInto(ExclusivePropertiesSearchPojo::class.java)
        }

        return Pair(
            pojoList.mapNotNull { it.toExclusivePropertyInfo() },
            // リクエストとして渡すページ数が極端に多いと、不整合が起きるが、実務上は問題ない認識
            if (pojoList.isNotEmpty()) pojoList[0].totalCount else 0
        )
    }

    /**
     * サブクエリのWhere句の条件を返却
     *
     * 下記の処理を実行
     * -建物CDが指定されている場合、指定の建物CDと部分一致しているものでフィルタ
     * -部屋CDが指定されている場合、指定の部屋CDと部分一致しているものでフィルタ
     * -先行期間FROMが指定されている場合、先行期間FROM以降のレコードでフィルタ
     * -先行期間TOが指定されている場合、先行期間TO以降のレコードでフィルタ
     * -削除フラグがないレコードでフィルタ
     * -リーシング先行公開がtrueの場合、リーシングのレコードでフィルタ
     * -不動産会社先行公開がtrueの場合、不動産会社のレコードでフィルタ
     *
     */
    private fun buildSubQueryWhereConditions(
        parameter: ExclusivePropertiesSearch,
        ignorePaging: Boolean = false,
        comeFrom: ComeFromType = ComeFromType.Default,
    ): List<Condition> {
        val subQueryWhereConditions = mutableListOf<Condition>()
        parameter.idList?.takeIf { it.isNotEmpty() }?.let {
            subQueryWhereConditions.add(EXCLUSIVE_PROPERTY.ID.`in`(it.map(ExclusiveProperty.Id::value)))
        }
        parameter.buildingCode?.let {
            subQueryWhereConditions.add(EXCLUSIVE_PROPERTY.BUILDING_CODE.like("%${it}%"))
        }
        parameter.roomCode?.let {
            subQueryWhereConditions.add(EXCLUSIVE_PROPERTY.ROOM_CODE.like("%${it}%"))
        }
        parameter.exclusiveFrom?.let {
            subQueryWhereConditions.add(
                EXCLUSIVE_PROPERTY.EXCLUSIVE_TO.ge(it.yyyyMMdd().toInt())
            )
        }
        parameter.exclusiveTo?.let {
            subQueryWhereConditions.add(
                EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM.le(it.yyyyMMdd().toInt())
            )
        }

        if (ignorePaging) {
            //新規作成
            if (comeFrom != ComeFromType.Cancel && comeFrom != ComeFromType.Update) {
                subQueryWhereConditions.add(
                    EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG.eq(
                        false.toInt().toString()
                    )
                )

                val today = LocalDate.now()
                subQueryWhereConditions.add(
                    EXCLUSIVE_PROPERTY.EXCLUSIVE_TO.ge(today.yyyyMMdd().toInt())
                )

            }

            subQueryWhereConditions.add(EXCLUSIVE_PROPERTY.DELETE_FLAG.eq(false.toInt().toString()))
        }


        if (parameter.listingSituationTypeList.isNotEmpty()) {
            subQueryWhereConditions.add(parameter.listingSituationTypeList.map {
                getListingSituationTypeCondition(it, LocalDate.now().yyyyMMdd().toInt())
            }.reduce { acc, conditions -> acc.or(conditions) })
        }


        parameter.officeCode?.let {
            subQueryWhereConditions.add(
                ROOM_INFO_MASTER.MARKETING_BRANCH_OFFICE_CD.eq(
                    it
                )
            )
        }


        parameter.createDate?.let {
            subQueryWhereConditions.add(EXCLUSIVE_PROPERTY.CREATION_DATE.eq(it.yyyyMMdd().toInt()))
        }

        parameter.propertyName?.let {
            subQueryWhereConditions.add(BUILDING_INFO_MASTER.PROPERTY_NAME.like("%${it}%"))
        }


        return subQueryWhereConditions.toList()
    }

    /**
     * メインクエリのWhere句の条件を返却
     *
     * 下記の処理を実行
     * -先行先が指定されている場合、指定の先行先と部分一致しているものでフィルタ
     *
     */
    private fun buildWhereConditions(
        parameter: ExclusivePropertiesSearch,
        subQuery: Table<Record>,
    ): List<Condition> {
        val whereConditions = mutableListOf<Condition>()

        if (!parameter.companyTypeList.isNullOrEmpty()) {
            val orConditions = parameter.companyTypeList.map {
                if (it.value == ExclusiveProperty.CompanyType.RealEstate.value && !parameter.exclusiveTarget.isNullOrEmpty()) {
                    subQuery.field("company_type", Int::class.java)!!
                        .eq(ExclusiveProperty.CompanyType.RealEstate.value)
                        .and(
                            subQuery.field("EXCLUSIVE_TARGET", String::class.java)!!.like(
                                "%${parameter.exclusiveTarget}%"
                            )
                        )
                } else {
                    // 通常の条件
                    subQuery.field("company_type", Int::class.java)!!.eq(it.value)
                }
            }

            if (orConditions.isNotEmpty()) {
                whereConditions.add(
                    orConditions.reduce { acc, condition -> acc.or(condition) }
                )
            }
        }


        parameter.creator?.let {
            whereConditions.add(
                subQuery.field("CREATOR", String::class.java)!!.like(
                    "%${it}%"
                )
            )
        }


        return whereConditions
    }

    /**
     * クエリのサブクエリ部分を返却
     *
     * @param selectTargets 出力するカラムの一覧
     * @param subQueryWhereConditions サブクエリのwhere句
     */
    private fun buildSubQuery(
        selectTargets: List<Field<*>>,
        subQueryWhereConditions: List<Condition>,
    ) = context.select(selectTargets)
        .from(EXCLUSIVE_PROPERTY)
        .leftJoin(BUILDING_INFO_MASTER)
        .on(EXCLUSIVE_PROPERTY.BUILDING_CODE.eq(BUILDING_INFO_MASTER.BUILDING_CODE))
        .leftJoin(ROOM_INFO_MASTER)
        .on(
            EXCLUSIVE_PROPERTY.BUILDING_CODE.eq(ROOM_INFO_MASTER.PROPERTY_BUILDING_CD)
                .and(EXCLUSIVE_PROPERTY.ROOM_CODE.eq(ROOM_INFO_MASTER.PROPERTY_ROOM_CD))
        )
        .leftJoin(EXCLUSIVE_PROPERTY_E_CODE)
        .on(EXCLUSIVE_PROPERTY.ID.eq(EXCLUSIVE_PROPERTY_E_CODE.ID))
        .leftJoin(AGENT)
        .on(EXCLUSIVE_PROPERTY_E_CODE.E_CODE.eq(AGENT.CHUKAI_GYOSHA_CD))
        .leftJoin(EMPLOYEE_MASTER)
        .on(
            EXCLUSIVE_PROPERTY.CREATOR.eq(EMPLOYEE_MASTER.EMPLOYEE_NUMBER)
        )
        .leftJoin(EMPLOYEE_MASTER.`as`("EM_FOR_UPDATER"))
        .on(
            EXCLUSIVE_PROPERTY.UPDATER.eq(EMPLOYEE_MASTER.`as`("EM_FOR_UPDATER").EMPLOYEE_NUMBER)
        )
        .where(subQueryWhereConditions)
        .asTable("SUB_QUERY")

    /**
     * 受け取ったパラメータからメインクエリを作成し、ソート条件を付与して返却
     *
     * @param subQuery メインクエリのfrom句
     * @param parameter 先行公開検索に使用するパラメータ
     * @param whereConditions メインクエリのwhere句
     */
    private fun buildQuery(
        subQuery: Table<Record>,
        parameter: ExclusivePropertiesSearch,
        whereConditions: List<Condition>,
    ): SelectLimitStep<Record> {
        val isAscend = parameter.sortOrder.isAscend()
        val baseQuery = context
            .select(
                // select *はパフォーマンスが遅くなる認識のため、パフォーマンス面での問題があった場合は修正可能
                subQuery.asterisk(),
                DSL.count().over().`as`("TOTAL_COUNT")
            )
            .from(subQuery)
            .where(whereConditions)

        // ソート条件を追加
        val sortFields = parameter.sortBy.flatMap { sortBy ->
            when (sortBy) {
                ExclusivePropertiesSearch.SortBy.DEFAULT_SORT ->
                    listOf(
                        sortField(
                            subQuery.field("exclusive_to", Int::class.java)!!,
                            isAscend
                        ),
                        sortField(
                            DSL.`when`(subQuery.field("company_type", Int::class.java)!!.eq(1), 1)
                                .`when`(subQuery.field("company_type", Int::class.java)!!.eq(2), 2)
                                .`when`(subQuery.field("company_type", Int::class.java)!!.eq(0), 3)
                                .otherwise(4)
                                .`as`("company_type_sort"),
                            isAscending = isAscend
                        )

                    )

                ExclusivePropertiesSearch.SortBy.BUILDING_CODE ->
                    listOf(
                        sortField(
                            subQuery.field("building_code", String::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.ROOM_CODE ->
                    listOf(
                        sortField(
                            subQuery.field("room_code", String::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.PROPERTY_NAME ->
                    listOf(
                        sortField(
                            subQuery.field("property_name", String::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.EXCLUSIVE_DATE ->
                    listOf(
                        sortField(
                            subQuery.field("exclusive_from", Int::class.java)!!,
                            isAscend
                        ),
                        sortField(
                            subQuery.field("exclusive_to", Int::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.EXCLUSIVE_END_DATE ->
                    listOf(
                        sortField(
                            subQuery.field("exclusive_to", Int::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.EXCLUSIVE_TARGET ->
                    listOf(
                        sortField(
                            subQuery.field("EXCLUSIVE_TARGET", String::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.CREATION_DATE ->
                    listOf(
                        sortField(
                            subQuery.field("creation_date", Int::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.CREATOR ->
                    listOf(
                        sortField(
                            subQuery.field("creator", String::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.UPDATE_DATE ->
                    listOf(
                        sortField(
                            subQuery.field("update_date", Int::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.UPDATER ->
                    listOf(
                        sortField(
                            subQuery.field("updater", String::class.java)!!,
                            isAscend
                        )
                    )

                ExclusivePropertiesSearch.SortBy.COMPANY_TYPE ->
                    listOf(
                        sortField(
                            subQuery.field("company_type", Int::class.java)!!,
                            isAscend
                        )
                    )
            }
        }
        return baseQuery.orderBy(sortFields)
    }

    /**
     * 昇順または降順のjooqの設定を付与
     */
    private fun <T> sortField(field: Field<T>, isAscending: Boolean): SortField<T> {
        return if (isAscending) field.asc() else field.desc()
    }

    private fun getListingSituationTypeCondition(
        listingSituationType: PublishStatus,
        now: Int
    ): Condition {
        return when (listingSituationType) {
            PublishStatus.PreRelease ->
                EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM.gt(now)
                    .and(
                        EXCLUSIVE_PROPERTY.DELETE_FLAG.eq(
                            false.toInt().toString()
                        )
                    )

            PublishStatus.InProgress ->
                EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM.le(now)
                    .and(EXCLUSIVE_PROPERTY.EXCLUSIVE_TO.ge(now))
                    .and(EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG.eq(false.toInt().toString()))
                    .and(
                        EXCLUSIVE_PROPERTY.DELETE_FLAG.eq(
                            false.toInt().toString()
                        )
                    )

            PublishStatus.Completed ->
                EXCLUSIVE_PROPERTY.EXCLUSIVE_TO.lt(now)
                    .or(EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG.eq(true.toInt().toString()))
                    .or(
                        EXCLUSIVE_PROPERTY.DELETE_FLAG.eq(
                            true.toInt().toString()
                        )
                    )

        }
    }

    // TODO: レコード数が多いとOOMとなってしまうので、Flowでメモリ使用を制限しながら後続処理へ流すよう変更が必要。
    fun findExclusivePropertyForECloudBatch(param: ExclusivePropertiesSearchForBatch): List<ExclusivePropertyInfo> {
        val subQueryWhereConditions = mutableListOf<Condition>()
        if (param.listingSituationTypeList.isNotEmpty()) {
            subQueryWhereConditions.add(param.listingSituationTypeList.map {
                getListingSituationTypeCondition(it, LocalDate.now().yyyyMMdd().toInt())
            }.reduce { acc, conditions -> acc.or(conditions) })
        }

        if (!param.companyTypeList.isNullOrEmpty()) {
            subQueryWhereConditions.add(param.companyTypeList.map {
                EXCLUSIVE_PROPERTY.COMPANY_TYPE.eq(it.value.toByte())
            }.reduce { acc, conditions -> acc.or(conditions) })
        }
        return context.select(
            EXCLUSIVE_PROPERTY.ID,
            EXCLUSIVE_PROPERTY.BUILDING_CODE,
            EXCLUSIVE_PROPERTY.ROOM_CODE,
            ROOM_INFO_MASTER.BUILDING_NAME,
            ROOM_INFO_MASTER.ROOM_NUMBER,
            EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM,
            EXCLUSIVE_PROPERTY.EXCLUSIVE_TO,
            EXCLUSIVE_PROPERTY.COMPANY_TYPE,
            EXCLUSIVE_PROPERTY.SALES_OFFICE_CODE,
            EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG,
            EXCLUSIVE_PROPERTY.CREATION_DATE,
            EXCLUSIVE_PROPERTY.CREATOR,
            EXCLUSIVE_PROPERTY.UPDATE_DATE,
            EXCLUSIVE_PROPERTY.UPDATER,
        ).from(EXCLUSIVE_PROPERTY)
            .join(ROOM_INFO_MASTER)
            .on(
                EXCLUSIVE_PROPERTY.BUILDING_CODE.eq(ROOM_INFO_MASTER.PROPERTY_BUILDING_CD)
                    .and(EXCLUSIVE_PROPERTY.ROOM_CODE.eq(ROOM_INFO_MASTER.PROPERTY_ROOM_CD))
            )
            .where(subQueryWhereConditions)
            .fetchInto(ExclusivePropertyForECloudBatchPojo::class.java)
            .mapNotNull { it.toExclusivePropertyForECloudBatch() }
    }

    fun findExclusivePropertyForFcNyuKoBatch(param: ExclusivePropertiesSearchForBatch): List<ExclusivePropertyInfo> {
        val subQueryWhereConditions = mutableListOf<Condition>()
        if (param.listingSituationTypeList.isNotEmpty()) {
            subQueryWhereConditions.add(param.listingSituationTypeList.map {
                getListingSituationTypeCondition(it, LocalDate.now().yyyyMMdd().toInt())
            }.reduce { acc, conditions -> acc.or(conditions) })
        }

        if (!param.companyTypeList.isNullOrEmpty()) {
            subQueryWhereConditions.add(param.companyTypeList.map {
                EXCLUSIVE_PROPERTY.COMPANY_TYPE.eq(it.value.toByte())
            }.reduce { acc, conditions -> acc.or(conditions) })
        }
        subQueryWhereConditions.add(
            (TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE.isNull)
                .or(
                    TEMPORARY_RESERVATION_FILE.APPLICATION_SCHEDULED_DATE.eq("")
                )
        )
        subQueryWhereConditions.add(
            ROOM_INFO_MASTER.RECORD_STATUS_TYPE.notIn(
                listOf(
                    "20",
                    "30",
                    "40",
                    "50"
                )
            )
        )
        return context.select(
            EXCLUSIVE_PROPERTY.ID,
            EXCLUSIVE_PROPERTY.BUILDING_CODE,
            EXCLUSIVE_PROPERTY.ROOM_CODE,
            ROOM_INFO_MASTER.BUILDING_NAME,
            ROOM_INFO_MASTER.ROOM_NUMBER,
            EXCLUSIVE_PROPERTY.EXCLUSIVE_FROM,
            EXCLUSIVE_PROPERTY.EXCLUSIVE_TO,
            EXCLUSIVE_PROPERTY.COMPANY_TYPE,
            EXCLUSIVE_PROPERTY.SALES_OFFICE_CODE,
            EXCLUSIVE_PROPERTY.EARLY_CLOSURE_FLAG,
            EXCLUSIVE_PROPERTY.CREATION_DATE,
            EXCLUSIVE_PROPERTY.CREATOR,
            EXCLUSIVE_PROPERTY.UPDATE_DATE,
            EXCLUSIVE_PROPERTY.UPDATER,
            EXCLUSIVE_PROPERTY_E_CODE.E_CODE,
        ).from(EXCLUSIVE_PROPERTY)
            .join(ROOM_INFO_MASTER)
            .on(
                EXCLUSIVE_PROPERTY.BUILDING_CODE.eq(ROOM_INFO_MASTER.PROPERTY_BUILDING_CD)
                    .and(EXCLUSIVE_PROPERTY.ROOM_CODE.eq(ROOM_INFO_MASTER.PROPERTY_ROOM_CD))
            )
            .leftJoin(EXCLUSIVE_PROPERTY_E_CODE)
            .on(EXCLUSIVE_PROPERTY.ID.eq(EXCLUSIVE_PROPERTY_E_CODE.ID))
            .leftJoin(AGENT)
            .on(EXCLUSIVE_PROPERTY_E_CODE.E_CODE.eq(AGENT.CHUKAI_GYOSHA_CD))
            .join(CODE_MAPPING)
            .on(EXCLUSIVE_PROPERTY_E_CODE.E_CODE.eq(CODE_MAPPING.E_CODE))
            .leftJoin(TEMPORARY_RESERVATION_FILE)
            .on(
                ROOM_INFO_MASTER.PROPERTY_BUILDING_CD.eq(TEMPORARY_RESERVATION_FILE.BUILDING_CD)
                    .and(ROOM_INFO_MASTER.PROPERTY_ROOM_CD.eq(TEMPORARY_RESERVATION_FILE.ROOM_CD))
            )
            .where(subQueryWhereConditions)
            .fetchInto(ExclusivePropertyForFcNyuKoBatchPojo::class.java)
            .mapNotNull { it.toExclusivePropertyForFcNyuKoBatch() }
    }
}

