/**
 * DKポータル/先行物件削除API
 *
 * サンプルリクエスト（居住用先行物件削除）
 *  curl -i -X POST -H "Content-Type: application/json" http://localhost:8083/dkportal -d '{"query": "mutation { deleteExclusiveHousing(dk_link_id: 1) { status } }"}'
 *
 * サンプルリクエスト（事業用先行物件削除）
 *  curl -i -X POST -H "Content-Type: application/json" http://localhost:8083/dkportal -d '{"query": "mutation { deleteExclusiveBusiness(dk_link_id: 1) { status } }"}'
*
 */

import { DKPortalValidationError } from '../../shared/dkportal/dkportal_response';
import { deserializeGraphqlBody, getOperationName } from '../../shared/shared_function';

// 居住用先行物件削除APIのoperation名
const targetOperationHousing = 'deleteExclusiveHousing';

// 事業用先行物件削除APIのoperation名
const targetOperationBusiness = 'deleteExclusiveBusiness';

export default [
  {
    id: 'dkportal_delete_exclusive',
    url: '/dkportal',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: async (req, res, next) => {
            const query = req.body['query'];
            // 対象の関数ではない場合は処理を終了
            const operation = getOperationName(query);
            if (targetOperationBusiness !== operation && targetOperationHousing !== operation) {
              next();
              return;
            }

            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const body = deserializeGraphqlBody(query);
            const result = await checkParameter(body);

            if (result) {
              res.status(400);
              res.send({
                errors: [result],
                data: { [operation]: null },
              });
              return;
            }

            res.status(200);
            res.send({
              data: {
                [operation]:{ status: true }
              },
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            res.status(500);
            res.send({
              error_message: '予期せぬエラーが発生しました。',
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する。適切な場合はtrueを返す。
 * @param {*} body
 * @returns {Boolean}
 */
async function checkParameter(body): Promise<DKPortalValidationError> {
  const { dk_link_id} = body;

  // 必須チェック
  if (!dk_link_id) {
    return new DKPortalValidationError(
      targetOperationBusiness,
      'dk_link_id',
      '先行物件IDは必須です。',
    );
  }

  return undefined;
}
