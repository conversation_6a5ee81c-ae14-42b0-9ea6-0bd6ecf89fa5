import axios from 'axios';
import {
  EboardResponseType,
  eboardResponseMapping,
} from '../../shared/eboard/eboard_response_type';

/**
 * いい物件ボード/仮押さえAPI
 *
 * サンプルリクエスト
 * curl -H "Authorization: Bearer 9b7aace8-1e0e-43e2-8394-c70563283594" -G -d tatemonoCd=21421 -d  heyaCd=202 -d updKbn=1 -d nYoteiDate=20241231 -d firmId=54353 -d firmName=Test -d branchName=fgf -d regName=gfrg http://localhost:8083/eboard/newEboardApi/update_kariosae
 */
export default [
  {
    id: 'eboard_update_kariosae',
    url: '/eboard/newEboardApi/update_kariosae',
    method: 'GET',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: async (req, res) => {
            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const responseType = checkParameter(req);

            if (responseType != EboardResponseType.SUCCESS) {
              res.status(400);
              res.send({
                result: responseType,
                message: eboardResponseMapping[responseType],
              });

              return;
            }

            // status: 200 の場合
            res.status(200);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
              updateResult: 'OK',
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            const responseType = EboardResponseType.OTHER_ERROR;

            res.status(500);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
            });
          },
        },
      }
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する
 * responseCodeMapping（api_response_specification.js）定義のエラーコードを返却する
 * @param {*} req
 * @returns {EboardResponseType}
 */
function checkParameter(req): EboardResponseType {
  const {
    tatemonoCd,
    heyaCd,
    updKbn,
    nYoteiDate,
    firmId,
    firmName,
    branchName,
    regName,
    staffCd,
    comment,
  } = req.query;

  // 登録
  if(updKbn == 1) {
    if (
      !tatemonoCd ||
      !heyaCd ||
      !updKbn ||
      !nYoteiDate ||
      !firmId ||
      !firmName ||
      !branchName ||
      !regName
    ) {
      return EboardResponseType.PARAMETER_MISSING;
    }
  }
  // 解除
  else if(updKbn == 0) {
    if (!tatemonoCd || !heyaCd) {
      return EboardResponseType.PARAMETER_MISSING;
    }
  }
  else {
    return EboardResponseType.PARAMETER_INVALID;
  }

  return EboardResponseType.SUCCESS;
}
