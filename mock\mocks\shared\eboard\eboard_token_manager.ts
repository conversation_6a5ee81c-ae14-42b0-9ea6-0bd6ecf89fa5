export default class EboardTokenManager {
  private static _instance: EboardTokenManager;

  private cache: Map<String, Date> = new Map<String, Date>();

  private constructor() {}

  public static get instance(): EboardTokenManager {
    if (!this._instance) {
      this._instance = new EboardTokenManager();
    }

    return this._instance;
  }

  public add(token: String, expiredAt: Date) {
    this.cache.set(token, expiredAt);
  }

  public verify(token: String): boolean {
    const expiredAt = this.cache.get(token);
    if (!expiredAt) {
      return false;
    }
    return new Date() < expiredAt;
  }
}
