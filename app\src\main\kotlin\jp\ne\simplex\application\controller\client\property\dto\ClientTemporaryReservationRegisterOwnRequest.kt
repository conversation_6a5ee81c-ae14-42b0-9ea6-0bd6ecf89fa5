package jp.ne.simplex.application.controller.client.property.dto

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.controller.client.shared.ClientBranchDto
import jp.ne.simplex.application.controller.client.shared.ClientEmployeeDto
import jp.ne.simplex.application.model.*
import jp.ne.simplex.exception.ClientValidationException
import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import jp.ne.simplex.shared.StringExtension.Companion.yyyyMMdd
import java.time.format.DateTimeParseException

data class ClientTemporaryReservationRegisterOwnRequest(
    @JsonProperty("buildingCd")
    @field:Schema(description = "建物コード", example = "000130305")
    val buildingCd: String,

    @JsonProperty("roomCd")
    @field:Schema(description = "部屋コード", example = "01010")
    val roomCd: String,

    @JsonProperty("scheduledMoveInDate")
    @field:Schema(description = "入居予定日（yyyyMMddフォーマット）", example = "20241031")
    val scheduledMoveInDate: String,

    @JsonProperty("comment")
    @field:Schema(description = "コメント", example = "コメントが入ります")
    val comment: String?,

    @JsonProperty("assignedBranch")
    @field:Schema(description = "仮押さえ担当支店")
    val assignedBranch: ClientBranchDto,

    @JsonProperty("assignedEmployee")
    @field:Schema(description = "仮押さえ担当従業員")
    val assignedEmployee: ClientEmployeeDto,

    @JsonProperty("registrationDate")
    @field:Schema(description = "登録日", example = "20241031")
    val registrationDate: String?,

    @JsonProperty("registrationTime")
    @field:Schema(description = "登録時刻", example = "101952")
    val registrationTime: String?,
) {

    // Service層の Interface に変換する
    fun toServiceInterface(): TemporaryReservation {
        val branch = assignedBranch.getBranch()

        // 担当支店の所属会社は、大東建託/大東建託パートナーズ/大東建託リーシングのいずれかのみを許容する
        // 画面から入力では、上記3つの会社所属する支店しか選択できないので、基本的にそれ以外がリクエストされることはない
        if (branch.company is Company.OtherCompany) {
            throw ClientValidationException(ErrorMessage.TEMPORARY_RESERVATION_INVALID_BRANCH_INFO.format())
        }

        try {
            return RegisterTemporaryReservation.OwnCompanyRegisterTemporaryReservation(
                id = Property.Id(
                    buildingCode = Building.Code.of(buildingCd),
                    roomCode = Room.Code.of(roomCd),
                ),
                assignedBranch = assignedBranch.getBranch(),
                assignedEmployee = assignedEmployee.getEmployee(),
                scheduledMoveInDate = scheduledMoveInDate.yyyyMMdd(),
                comment = TemporaryReservation.Comment.of(comment),
                version = registrationDate?.let { date ->
                    registrationTime?.let { time ->
                        TemporaryReservation.Version.of(date, time)
                    }
                }
            )
        } catch (e: ModelCreationFailedException) {
            throw ClientValidationException(e.detail)
        } catch (e: DateTimeParseException) {
            throw ClientValidationException(ErrorMessage.INVALID_DATE_FORMAT.format("yyyyMMdd"))
        }
    }
}
