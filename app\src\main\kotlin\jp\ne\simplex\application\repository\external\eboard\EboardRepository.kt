package jp.ne.simplex.application.repository.external.eboard

import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.RegisterTemporaryReservation.OtherCompanyRegisterTemporaryReservation
import jp.ne.simplex.application.model.RegisterTemporaryReservation.OwnCompanyRegisterTemporaryReservation
import jp.ne.simplex.application.repository.aws.SecretManagerRepository
import jp.ne.simplex.application.repository.db.OfficeRepositoryInterface
import jp.ne.simplex.application.repository.external.ExternalApiRestClient
import jp.ne.simplex.application.repository.external.Method
import jp.ne.simplex.application.repository.external.eboard.config.EboardRequest
import jp.ne.simplex.application.repository.external.eboard.config.EboardResponse
import jp.ne.simplex.application.repository.external.eboard.config.EboardTokenManager
import jp.ne.simplex.application.repository.external.eboard.dto.*
import jp.ne.simplex.authentication.AuthInfo
import jp.ne.simplex.shared.MultiThreadRunBlocking.Companion.runAsyncTasks
import jp.ne.simplex.shared.StringExtension.Companion.toMap
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.stereotype.Repository
import java.util.*

@Repository
class EboardRepository(
    @Qualifier("eboardRestClient")
    private val restClient: ExternalApiRestClient<EboardRequest, EboardResponse>,
    @Value("\${external.eboard.secret-id}")
    private val secretId: String,
    private val secretManager: SecretManagerRepository,
    private val officeRepository: OfficeRepositoryInterface,
) : EboardRepositoryInterface {

    companion object {
        private val log = LoggerFactory.getLogger(EboardRepository::class.java)
    }

    override fun login() {
        val secrets = secretManager.getValue(secretId).toMap()
        val request = EboardTokenDpIssueRequest(
            userid = secrets["userid"] as String,
            password = secrets["password"] as String,
            passphrase = secrets["passphrase"] as String,
        )

        val response = restClient.call(
            method = Method.POST,
            path = request.getApiPath().value,
            request = request,
            responseClass = EboardTokenDpIssueResponse::class.java
        )

        return EboardTokenManager.updateToken(response.accesstoken)
    }

    override fun registerTemporaryReservation(rtr: RegisterTemporaryReservation) {
        val request = when (rtr) {
            is OwnCompanyRegisterTemporaryReservation -> {
                EboardUpdateKariosaeRequest.of(rtr, rtr.getOfficeOrNull())
            }

            is OtherCompanyRegisterTemporaryReservation -> {
                EboardUpdateKariosaeRequest.of(rtr)
            }
        }

        return restClient.call(
            method = Method.GET,
            path = request.getApiPath().value,
            request = request,
            responseClass = EboardUpdateKariosaeResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }

    override fun cancelTemporaryReservation(
        ctr: CancelTemporaryReservation,
        canceled: TemporaryReservationInfo
    ) {
        val request =
            EboardUpdateKariosaeRequest.of(ctr, canceled, canceled.getOfficeOrNull()) ?: return

        return restClient.call(
            method = Method.GET,
            path = request.getApiPath().value,
            request = request,
            responseClass = EboardUpdateKariosaeResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }

    override fun updateTemporaryReservationComment(req: UpdateTemporaryReservationComment) {
        val request = EboardUpdateKariosaeRequest.of(req)

        restClient.call(
            method = Method.GET,
            path = request.getApiPath().value,
            request = request,
            responseClass = EboardUpdateKariosaeResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }

    override fun instructPublic(
        requestUser: AuthInfo.RequestUser,
        updatePublishStatusList: List<UpdatePropertyMaintenance.PublishStatus>
    ): UpdatePropertyMaintenance.Result<Map<Property.Id, Property.UpState?>, List<Property.Id>> {
        val successResult =
            Collections.synchronizedMap(mutableMapOf<Property.Id, Property.UpState>())
        val failedResult = Collections.synchronizedList(mutableListOf<Property.Id>())

        // リクエストに設定できるのが100件までなので、100件毎に分割する
        val tasks = updatePublishStatusList.chunked(100)

        runAsyncTasks(maxThreads = 10, tasks = tasks) { task ->
            runCatching {
                val request = EboardInstructPublicRequest.of(requestUser, task)

                restClient.call(
                    method = Method.POST,
                    path = request.getApiPath().value,
                    request = request,
                    responseClass = EboardInstructPublicResponse::class.java,
                    authenticationFailureCallback = { login() }
                )
            }.onSuccess { result ->
                result.list.forEach {
                    if (!it.isSuccess()) {
                        failedResult.add(it.getPropertyId())
                        return@forEach
                    }
                    return@forEach when (val upState = it.getUpState()) {
                        null -> {
                            // 公開済み物件に対して公開指示した際など、いい物件側でデータ更新が発生しなかった場合は、
                            // updateResult = OK で、upState = "" で返却されるので、その物件はDKPへの連携処理に流さないようにする
                            // しかし、いい物件との差異を解消するため、DBには保存する
                            successResult[it.getPropertyId()] = null
                            log.warn("想定外の値（upState = \"${it.upState}\"）を受信しましたが、updateResult=OKのため、後続に流します。詳細：\"${it.message}\"")
                        }

                        else -> {
                            successResult[it.getPropertyId()] = upState
                        }
                    }
                }
            }.onFailure {
                log.warn(it.message)
                // API自体が失敗（5xxなど）の場合は、リクエストした全てのIDを失敗として判断
                failedResult.addAll(task.map { ins -> ins.id })
            }
        }

        return UpdatePropertyMaintenance.Result(successResult, failedResult)
    }

    override fun reserveParking(
        parkingReservationAction: ParkingReservationAction,
        parkingReservationInfo: ParkingReservationInfo
    ) {
        val request = EBoardUpdateParkingReservationRequest.of(
            parkingReservationAction,
            parkingReservationInfo
        )

        restClient.call(
            method = Method.POST,
            path = request.getApiPath().value,
            request = request,
            contentType = MediaType.APPLICATION_FORM_URLENCODED,
            responseClass = EBoardUpdateParkingReservationResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }

    // 支店が所属している会社がパートナーズの場合は、紐づく営業所を取得する
    private fun OwnCompanyRegisterTemporaryReservation.getOfficeOrNull(): Office? {
        if (this.assignedBranch.company is Company.DaitouKentakuPartners) {
            return officeRepository.findBy(this.assignedBranch.code)
        }
        return null
    }

    // 支店が所属している会社がパートナーズの場合は、紐づく営業所を取得する
    private fun TemporaryReservationInfo.getOfficeOrNull(): Office? {
        return when (this) {
            is TemporaryReservationInfo.OtherCompanyTemporaryReservationInfo -> {
                null
            }

            is TemporaryReservationInfo.OwnCompanyTemporaryReservationInfo -> {
                if (this.assignedBranch.company is Company.DaitouKentakuPartners) {
                    return officeRepository.findBy(this.assignedBranch.code)
                }
                return null
            }

            is TemporaryReservationInfo.CancelledTemporaryReservationInfo -> {
                null
            }
        }
    }

    override fun registerParkingImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile,
    ) {
        val request =
            EboardUpdateParkingImageRequest.ofRegister(requestUser, buildingCode, imageFile)
        return restClient.call(
            method = Method.POST,
            path = request.getApiPath().value,
            contentType = MediaType.MULTIPART_FORM_DATA,
            request = request,
            responseClass = EboardUpdateParkingImageResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }

    override fun deleteParkingImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
    ) {
        val request = EboardUpdateParkingImageRequest.ofDelete(requestUser, buildingCode)
        return restClient.call(
            method = Method.POST,
            path = request.getApiPath().value,
            contentType = MediaType.MULTIPART_FORM_DATA,
            request = request,
            responseClass = EboardUpdateParkingImageResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }

    override fun registerGarbageImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile
    ) {
        val request =
            EboardUpdateGarbageImageRequest.ofRegister(requestUser, buildingCode, imageFile)
        return restClient.call(
            method = Method.POST,
            path = request.getApiPath().value,
            contentType = MediaType.MULTIPART_FORM_DATA,
            request = request,
            responseClass = EboardUpdateGarbageImageResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }

    override fun deleteGarbageImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code
    ) {
        val request = EboardUpdateGarbageImageRequest.ofDelete(requestUser, buildingCode)
        return restClient.call(
            method = Method.POST,
            path = request.getApiPath().value,
            contentType = MediaType.MULTIPART_FORM_DATA,
            request = request,
            responseClass = EboardUpdateGarbageImageResponse::class.java,
            authenticationFailureCallback = { login() }
        ).throwIfReceivedError()
    }
}

interface EboardRepositoryInterface {

    /** 認証 */
    fun login()

    /** 仮押さえ更新（登録） */
    fun registerTemporaryReservation(rtr: RegisterTemporaryReservation)

    /**
     * 仮押さえ更新（解除）
     *   @param ctr 仮押さえ解除要求
     *   @param canceled 解除対象の仮押さえが、登録/更新された時の情報
     */
    fun cancelTemporaryReservation(
        ctr: CancelTemporaryReservation,
        canceled: TemporaryReservationInfo
    )

    /**
     * 仮押さえコメント更新
     *   @param req 仮押さえコメント更新要求
     */
    fun updateTemporaryReservationComment(req: UpdateTemporaryReservationComment)

    /** 公開指示 */
    fun instructPublic(
        requestUser: AuthInfo.RequestUser,
        updatePublishStatusList: List<UpdatePropertyMaintenance.PublishStatus>
    ): UpdatePropertyMaintenance.Result<Map<Property.Id, Property.UpState?>, List<Property.Id>>

    /** 駐車場予約 */
    fun reserveParking(
        parkingReservationAction: ParkingReservationAction,
        parkingReservationInfo: ParkingReservationInfo
    )

    /** 駐車場配置図画像登録 */
    fun registerParkingImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile,
    )

    /** 駐車場配置図画像削除 */
    fun deleteParkingImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
    )

    /** ゴミ置場画像登録 */
    fun registerGarbageImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
        imageFile: ImageFile,
    )

    /** ゴミ置場画像削除 */
    fun deleteGarbageImage(
        requestUser: AuthInfo.RequestUser,
        buildingCode: Building.Code,
    )
}
