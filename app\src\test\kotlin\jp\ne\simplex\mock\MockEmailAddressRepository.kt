package jp.ne.simplex.mock

import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.EmailAddress
import jp.ne.simplex.application.repository.db.EmailAddressRepositoryInterface

class MockEmailAddressRepository(
    val getTenantAddressListFunc: (branchCode: Branch.Code) -> List<EmailAddress> = { _ -> emptyList() },
    val getDaitateAddressListFunc: (branchCode: Branch.Code) -> List<EmailAddress> = { _ -> emptyList() },
    val getAddressListByBranchCodeFunc: (branchCodes: List<Branch.Code>) -> List<EmailAddress> = { _ -> emptyList() },
) : EmailAddressRepositoryInterface {
    override fun getTenantAddressList(branchCode: Branch.Code): List<EmailAddress> {
        return getTenantAddressListFunc(branchCode)
    }

    override fun getDaitateAddressList(branchCode: Branch.Code): List<EmailAddress> {
        return getDaitateAddressListFunc(branchCode)
    }

    override fun getAddressListByBranchCode(branchCodes: List<Branch.Code>): List<EmailAddress> {
        return getAddressListByBranchCodeFunc(branchCodes)
    }
}
