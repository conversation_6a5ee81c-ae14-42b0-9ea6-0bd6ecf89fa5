import { OperationTypeNode, parse } from 'graphql';

export function keysToLowerCase(obj: Record<string, any>): Record<string, any> {
  return Object.keys(obj).reduce(
    (acc, key) => {
      acc[key.toLowerCase()] = obj[key];
      return acc;
    },
    {} as Record<string, any>,
  );
}

export function getOperationName(query): string {
  const parsedQuery = parse(query);
  const mutation = parsedQuery.definitions[0];

  // オペレーションがミューテーションであることを確認
  if (
    mutation.kind === 'OperationDefinition' &&
    mutation.operation === OperationTypeNode.MUTATION
  ) {
    const selection = mutation.selectionSet.selections[0];

    // FieldNodeであることを確認
    if (selection.kind === 'Field') {
      return selection.name.value;
    }
    throw new Error('Expected a Field node.');
  }
  throw new Error('Invalid GraphQL mutation.');
}

export function deserializeGraphqlBody(query): Record<string, any> {
  const parsedQuery = parse(query);
  const mutation = parsedQuery.definitions[0];

  // オペレーションがミューテーションであることを確認
  if (
    mutation.kind === 'OperationDefinition' &&
    mutation.operation === OperationTypeNode.MUTATION
  ) {
    const selection = mutation.selectionSet.selections[0];

    // FieldNodeであることを確認
    if (selection.kind === 'Field') {
      const args = selection.arguments.reduce((acc, arg) => {
        // 引数の名前をキー、値を値にマッピング
        acc[arg.name.value] = arg.value;
        return acc;
      }, {});
      return args; // 引数をオブジェクトとして返す
    }
    throw new Error('Expected a Field node.');
  }
  throw new Error('Invalid GraphQL mutation.');
}
