{"files.eol": "\r\n", "editor.codeActionsOnSave": {"source.removeUnusedImports": "explicit", "source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "prettier.configPath": ".prettier<PERSON>", "prettier.prettierPath": "./node_modules/prettier", "typescript.tsdk": "node_modules/typescript/lib", "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "testing.automaticallyOpenPeekView": "never", "eslint.workingDirectories": [{"pattern": "./libs/*/"}, {"pattern": "./apps/*/"}], "eslint.useFlatConfig": true, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "cSpell.words": ["accesstoken", "adff", "bikou", "dkportal", "Eboard", "greatsimplex", "heya", "kario<PERSON><PERSON>", "keisai", "kentaku", "propetech", "ta<PERSON><PERSON>", "uuidv", "<PERSON><PERSON><PERSON>"]}