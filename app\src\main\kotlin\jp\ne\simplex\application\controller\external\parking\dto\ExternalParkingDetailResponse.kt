package jp.ne.simplex.application.controller.external.parking.dto

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import jp.ne.simplex.application.model.*
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibility
import jp.ne.simplex.application.model.ParkingContractPossibility.ContractPossibilityAutoJudge
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.yyyyMMdd
import java.math.BigDecimal
import java.math.RoundingMode

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ExternalParkingDetailResponse(
    @JsonProperty("contractPossibleStatus")
    @field:Schema(description = "契約可否ステータス")
    val contractPossibleStatus: ContractPossibleStatusResponse,
    @JsonProperty("parkingList")
    @field:Schema(description = "駐車場リスト")
    val parkingList: List<ParkingResponse>,
) {
    // 契約可否ステータス
    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ContractPossibleStatusResponse(
        @JsonProperty("firstParking")
        @field:Schema(
            description = "駐車場一台目契約可否ステータス(0: 可, 1: 不可, 2: 要問合せ)",
            example = "0"
        )
        val firstParking: Int,
        @JsonProperty("secondParking")
        @field:Schema(
            description = "駐車場二台目契約可否ステータス(0: 可, 1: 不可, 2: 要問合せ)",
            example = "2"
        )
        val secondParking: Int,
        @JsonProperty("isAutoJudge")
        @field:Schema(
            description = "駐車場契約可否自動判定有無(0: 自動判定なし, 1: 自動判定あり)",
            example = "1"
        )
        val isAutoJudge: Int,
    )

    // 駐車場
    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ParkingResponse(
        @JsonProperty("buildingCode")
        @field:Schema(description = "建物コード", example = "000130302")
        val buildingCode: String,
        @JsonProperty("buildingName")
        @field:Schema(description = "建物名称", example = "xxxxx")
        val buildingName: String? = null,
        @JsonProperty("postalCode")
        @field:Schema(description = "郵便番号", example = "123-4567")
        val postalCode: String? = null,
        @JsonProperty("location")
        @field:Schema(description = "所在地", example = "東京都 A区 B 1-2-3")
        val location: String? = null,
        @JsonProperty("parkingLayoutImageUrl")
        @field:Schema(
            description = "駐車場配置図URL",
            example = "https://image.aaa.bbb/ccc/ddd/xxxx.jpg"
        )
        val parkingLayoutImageUrl: String,
        @JsonProperty("officeCode")
        @field:Schema(description = "営業所コード", example = "858")
        val officeCode: String?,
        @JsonProperty("parkingLotList")
        @field:Schema(description = "区画リスト")
        val parkingLotList: List<ParkingLotResponse> = emptyList(),
    )

    // 駐車場区画
    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ParkingLotResponse(
        @JsonProperty("parkingCode")
        @field:Schema(description = "駐車場コード", example = "001")
        val parkingLotCode: String,
        @JsonProperty("localDisplayNumber")
        @field:Schema(description = "現地表示番号", example = "1")
        val localDisplayNumber: String? = null,
        @JsonProperty("parkingCategory")
        @field:Schema(
            description = "駐車場区分(1: 単体, 2: 縦列, 3: 軽自動車, 4: 立体, 5: バイク)",
            example = "1"
        )
        val parkingCategory: Int? = null,
        @JsonProperty("vacancyParkingStatus")
        @field:Schema(
            description = "駐車場空き区画ステータス(0: 可, 1: 不可, 2: 要問合せ)",
            example = "1"
        )
        val vacancyParkingStatus: Int,
        @JsonProperty("isAvailable")
        @field:Schema(description = "利用可否(0: 不可, 1: 可能)", example = "0")
        val isAvailable: Int,
        @JsonProperty("fee")
        @field:Schema(description = "賃料(税込み)", example = "15000")
        val fee: Int? = null,
        @JsonProperty("assessmentDivision")
        @field:Schema(description = "査定区分(1: 査定, 2: 査定外)", example = "1")
        val assessmentDivision: Int? = null,
        @JsonProperty("specialContractFlag")
        @field:Schema(description = "特約有無サイン(0: 無し, 1: 有り)", example = "0")
        val specialContractFlag: Int? = null,
        @JsonProperty("expectedMoveOutDate")
        @field:Schema(description = "退去予定日(yyyyMMdd)", example = "20250125")
        val expectedMoveOutDate: String? = null,
        @JsonProperty("brokerApplicationPossibility")
        @field:Schema(description = "斡旋可否(0: 無し, 1: 有り)", example = "0")
        val brokerApplicationPossibility: Int? = null,
        @JsonProperty("offSiteParkingCategory")
        @field:Schema(description = "敷地外駐車場区分(1: 敷地外, 2: 敷地内)", example = "1")
        val offSiteParkingCategory: Int,
        @JsonProperty("offSiteParkingDistance")
        @field:Schema(description = "敷地外駐車場との距離", example = "20")
        val offSiteParkingDistance: Int? = null,
        @JsonProperty("linkedBuildingCode")
        @field:Schema(description = "区画に紐づく物件の建物コード", example = "026373501")
        val linkedBuildingCode: String? = null,
        @JsonProperty("linkedRoomCode")
        @field:Schema(description = "区画に紐づく物件の部屋コード", example = "02030")
        val linkedRoomCode: String? = null,
        @JsonProperty("linkedRoomNumber")
        @field:Schema(description = "区画に紐づく物件の部屋番号", example = "203")
        val linkedRoomNumber: String? = null,
        @JsonProperty("reservationList")
        @field:Schema(description = "予約リスト")
        val reservationList: List<ParkingReservationResponse> = emptyList(),
    )

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ParkingReservationResponse(
        @JsonProperty("reservationStatus")
        @field:Schema(
            description = "予約状態(0: 仮申込, 1: 受付, 2: 契約済み, 3: キャンセル)",
            example = "1"
        )
        val reservationStatus: Int,
        @JsonProperty("reservationType")
        @field:Schema(
            description = "予約種別(0: 仮押さえ(自動), 1: 仮押さえ(手動), 2: 作業, 3: 場所変更, 4: 1日利用)",
            example = "1"
        )
        val reservationType: Int,
        @JsonProperty("reserveStartDate")
        @field:Schema(description = "利用開始日(yyyyMMdd)", example = "20250215")
        val reserveStartDate: String? = null,
        @JsonProperty("reserveEndDate")
        @field:Schema(description = "利用終了日(yyyyMMdd)", example = "20260215")
        val reserveEndDate: String? = null,
        @JsonProperty("remarks")
        @field:Schema(description = "予約メモ", example = "コメント")
        val remarks: String? = null,
        @JsonProperty("reserverSystem")
        @field:Schema(
            description = "予約システム(0: DKリンク, 1: キマルームサイン, 2: ウェルカムパーク)",
            example = "1"
        )
        val reserverSystem: Int? = null,
    )

    companion object {
        fun from(
            contractPossibility: ParkingContractPossibility,
            parkingList: List<Parking>,
            urlBase: String,
            consumptionTaxRate: ConsumptionTaxRate
        ): ExternalParkingDetailResponse {
            return ExternalParkingDetailResponse(
                contractPossibleStatus = from(contractPossibility),
                parkingList = parkingList.map { from(it, urlBase, consumptionTaxRate) },
            )
        }

        fun from(source: ParkingContractPossibility): ContractPossibleStatusResponse {
            return if (source.isAutoJudge == ContractPossibilityAutoJudge.MANUAL) {
                ContractPossibleStatusResponse(
                    firstParking = ContractPossibility.REQUIRED_CONFIRM.value.toInt(),
                    secondParking = ContractPossibility.REQUIRED_CONFIRM.value.toInt(),
                    isAutoJudge = ContractPossibilityAutoJudge.MANUAL.value.toInt(),
                )
            } else {
                ContractPossibleStatusResponse(
                    firstParking = source.firstParkingContractPossibility.value.toInt(),
                    secondParking = source.secondParkingContractPossibility.value.toInt(),
                    isAutoJudge = source.isAutoJudge.value.toInt(),
                )
            }
        }

        fun from(
            source: Parking, urlBase: String, consumptionTaxRate: ConsumptionTaxRate
        ): ParkingResponse {
            var tmpPostalCode = source.building.postalCode
            if (tmpPostalCode?.length == 7) {
                tmpPostalCode = "${tmpPostalCode.substring(0, 3)}-${tmpPostalCode.substring(3)}"
            }
            var tmpLocation = source.building.location
            if (tmpLocation != null && tmpLocation.length > 100) {
                tmpLocation = tmpLocation.take(100)
            }
            // フォーマッタが変な改行をするので別変数に抜き出し
            val tmpUrlBase = if (urlBase.endsWith("/")) urlBase else "${urlBase}/"
            val parkingLayoutImageUrl = source.building.code.toLayoutImageUrl(tmpUrlBase)
            return ParkingResponse(
                buildingCode = source.building.code.value,
                buildingName = source.building.name.value,
                postalCode = tmpPostalCode,
                location = tmpLocation,
                parkingLayoutImageUrl = parkingLayoutImageUrl,
                officeCode = source.building.businessOfficeCode?.value,
                parkingLotList = source.parkingLotList.map { from(it, consumptionTaxRate) }
            )
        }

        fun from(source: ParkingLot, consumptionTaxRate: ConsumptionTaxRate): ParkingLotResponse {
            return ParkingLotResponse(
                parkingLotCode = source.id.parkingLotCode.value,
                localDisplayNumber = source.localDisplayNumber,
                parkingCategory = source.parkingLotCategory?.value?.toInt(),
                vacancyParkingStatus = source.vacancyParkingStatus.value,
                isAvailable = if (source.isAvailable) 1 else 0,
                fee = if (source.parkingFeeInTax == 1) source.parkingFee else source.parkingFee?.let {
                    BigDecimal(it).multiply(
                        BigDecimal.ONE.add(consumptionTaxRate.nationalTaxConsumptionPercent)
                    ).setScale(0, RoundingMode.FLOOR).toInt()
                },
                assessmentDivision = source.assessmentDivision?.value,
                specialContractFlag = source.specialContractFlag?.value,
                expectedMoveOutDate = source.expectedMoveOutDate?.yyyyMMdd(),
                brokerApplicationPossibility = source.brokerApplicationPossibility?.value,
                offSiteParkingCategory = source.offSiteParkingLotCategory.value,
                offSiteParkingDistance = source.offSiteParkingDistance,
                linkedBuildingCode = source.linkedPropertyId?.buildingCode?.value,
                linkedRoomCode = source.linkedPropertyId?.roomCode?.value,
                linkedRoomNumber = source.linkedRoomNumber?.getFormattedValue(),
                reservationList = source.reservationList.map { from(it) },
            )
        }

        fun from(source: ParkingReservationInfo): ParkingReservationResponse {
            return ParkingReservationResponse(
                reservationStatus = source.status.value.toInt(),
                reservationType = ExternalReservationType.fromReservationType(source.reservationType).value,
                reserveStartDate = source.reserveStartDatetime?.yyyyMMdd(),
                reserveEndDate = source.reserveEndDatetime?.yyyyMMdd(),
                remarks = source.remarks?.value,
                reserverSystem = source.requestSource?.value?.toInt(),
            )
        }
    }
}
