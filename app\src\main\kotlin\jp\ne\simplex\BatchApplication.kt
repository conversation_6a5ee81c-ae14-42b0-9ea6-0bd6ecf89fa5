package jp.ne.simplex

import jp.ne.simplex.application.batch.BatchInterface
import jp.ne.simplex.application.batch.ExecuteOptionType
import org.slf4j.LoggerFactory
import org.springframework.boot.SpringApplication
import org.springframework.boot.WebApplicationType
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
import org.springframework.boot.builder.SpringApplicationBuilder
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.context.annotation.Profile

@Profile("batch")
@SpringBootApplication(
    scanBasePackages = [
        "jp.ne.simplex.application",
        "jp.ne.simplex.authentication",
        "jp.ne.simplex.exception",
        "jp.ne.simplex.log",
        "jp.ne.simplex.shared",
    ],
    exclude = [UserDetailsServiceAutoConfiguration::class],
)
@ConfigurationPropertiesScan
class BatchApplication(
    private val batchMap: Map<String, BatchInterface>,
) {
    companion object {
        private val log = LoggerFactory.getLogger(BatchApplication::class.java)
    }

    fun execute(args: Array<String>) {
        log.info("batch start.")
        val start = System.currentTimeMillis()
        if (args.isEmpty() || !batchMap.containsKey(args[0])) {
            throw IllegalArgumentException("Invalid batch name.[${args[0]}]")
        }
        val executeOption = if (args.size == 1) null else ExecuteOptionType.fromValue(args[1])
        batchMap[args[0]]!!.execute(executeOption)
        log.info("batch finish. latency=${System.currentTimeMillis() - start}ms")
    }
}

fun main(args: Array<String>) {
    SpringApplicationBuilder(BatchApplication::class.java)
        .web(WebApplicationType.NONE)
        .run(*args).use {
            it.getBean(BatchApplication::class.java).execute(args)
            SpringApplication.exit(it)
        }
}
