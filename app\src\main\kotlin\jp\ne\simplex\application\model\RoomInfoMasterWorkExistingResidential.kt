package jp.ne.simplex.application.model

import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMM
import jp.ne.simplex.shared.LocalDateExtension.Companion.yyyyMMdd
import jp.ne.simplex.shared.LocalDateTimeExtension.Companion.HHmm
import java.time.LocalDateTime

/** 入居中物件データ作成バッチ用の居住用既存物件ワークデータ */
data class RoomInfoMasterWorkExistingResidential(
    /** 支店CD */
    val branchCode: String?,
    /** 状況 */
    val status: String?,
    /** 日付 */
    val date: Int?,
    /** 交渉 */
    val negotiation: String?,
    /** 建物CD */
    val buildingCode: String,
    /** 部屋CD */
    val roomCode: String,
    /** 家主名称 */
    val landlordName: String?,
    /** 建物名称 */
    val buildingName: String?,
    /** 間取り名 */
    val layoutName: String?,
    /** 間取り */
    val layout: String?,
    /** 所在地1 */
    val address1: String?,
    /** 所在地2 */
    val address2: String?,
    /** 家賃 */
    val rent: Int?,
    /** 駐車料 */
    val parkingFee: Int?,
    /** 共益費 */
    val commonFee: Int?,
    /** 礼金 */
    val keyMoney: Int?,
    /** 保証金（敷金） */
    val securityDeposit: Int?,
    /** 町内会費 */
    val neighborhoodAssociationFee: Int?,
    /** 退居日 */
    val moveOutDate: Int?,
    /** 備考1 */
    val remarks1: String?,
    /** 備考2 */
    val remarks2: String?,
    /** ロッキー区分 */
    val rockyCategory: String?,
    /** 区分A */
    val categoryA: String?,
    /** 区分B */
    val categoryB: String?,
    /** FF区分 */
    val ffCategory: String?,
    /** AD区分 */
    val adCategory: String?,
    /** 都道府県CD */
    val prefectureCode: String?,
    /** 市区郡CD */
    val cityCode: String?,
    /** 町村字通称CD */
    val townCode: String?,
    /** 物件住所かな */
    val propertyAddressKana: String?,
    /** 町村かな */
    val townNameKana: String?,
    /** 部屋番号 */
    val roomNumber: String?,
    /** 時間 */
    val time: Int?,
    /** 完工年月 */
    val constructionYearMonth: Int?,
    /** 抽出用支店CD */
    val extractionBranchCode: String?,
    /** 所在地3 */
    val address3: String?,
) {
    companion object {

        fun from(
            source: RoomInfoMasterWorkSource
        ): RoomInfoMasterWorkExistingResidential {
            return RoomInfoMasterWorkExistingResidential(
                branchCode = "", // 空白固定
                status = getStatus(),
                date = getDate(source),
                negotiation = source.negotiationEmployeeName ?: "",
                buildingCode = source.buildingCode,
                roomCode = source.roomCode,
                landlordName = source.landlordName ?: "",
                buildingName = source.buildingName ?: "",
                layoutName = source.layoutDetails,
                layout = "", // 間取区分マスタが連携されないため空白をセット
                address1 = source.townKanjiName ?: "",
                address2 = source.addressDetails ?: "",
                rent = source.rent?.let { it / 100 },
                parkingFee = 0, // 消費税計算処理が不明なため0固定
                commonFee = source.commonFee?.let { it / 1000 },
                keyMoney = source.keyMoneyAmount?.let { keyMoney ->
                    source.rent?.takeIf { it > 0 }?.let { rent -> keyMoney / rent }
                },
                securityDeposit = source.depositAmount?.let { deposit ->
                    source.rent?.takeIf { it > 0 }?.let { rent -> deposit / rent }
                },
                neighborhoodAssociationFee = source.neighborhoodAssociationFee,
                moveOutDate = source.moveOutDate?.yyyyMMdd()?.toInt(),
                remarks1 = "", // 空白固定
                remarks2 = "", // 空白固定
                rockyCategory = null, // null固定
                categoryA = null, // null固定
                categoryB = null, // null固定
                ffCategory = source.frontFreeRentSign?.toString(),
                adCategory = "", // 空白固定
                prefectureCode = source.prefectureCode,
                cityCode = source.cityCode,
                townCode = source.townCode,
                propertyAddressKana = source.addressDetails,
                townNameKana = source.townKanaName,
                roomNumber = source.roomNumber,
                time = LocalDateTime.now().HHmm().toInt(),
                constructionYearMonth = getConstructionYearMonth(source),
                extractionBranchCode = source.tenantRecruitmentBranchCode,
                address3 = (source.townKanjiName ?: "") + (source.addressDetails ?: ""),
            )
        }

        private fun getStatus(): String {
            // テナントキャンセルDBが連携されないため空白をセット
            return ""
        }

        private fun getDate(source: RoomInfoMasterWorkSource): Int? {
            return when (getStatus()) {
                "申" -> source.moveInApplicationDate?.yyyyMMdd()?.toInt()
                "手" -> source.depositChangeDate?.yyyyMMdd()?.toInt()
                "確" -> source.leaseContractDate?.yyyyMMdd()?.toInt()
                else -> 0
            }
        }

        private fun getConstructionYearMonth(source: RoomInfoMasterWorkSource): Int? {
            return source.completionDeliveryDate?.yyyyMM()?.toInt()
                ?: source.completionExpectedDate?.yyyyMM()?.toInt()
        }
    }
}
