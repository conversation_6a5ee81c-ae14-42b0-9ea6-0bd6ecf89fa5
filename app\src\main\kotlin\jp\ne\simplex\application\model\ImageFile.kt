package jp.ne.simplex.application.model

import jp.ne.simplex.exception.ErrorMessage
import jp.ne.simplex.exception.ModelCreationFailedException
import org.springframework.web.multipart.MultipartFile

class ImageFile private constructor(
    // ファイル名
    val fileName: String,
    // 画像ファイルデータ
    val imageData: ByteArray,
) {
    companion object {
        private const val IMAGE_CONTENT_TYPE = "image/jpeg"
        private const val IMAGE_EXTENSION = ".jpg"

        private fun MultipartFile.isValidImageFile(maxBytes: Long): Boolean {
            return (!this.isEmpty) && this.size <= maxBytes
                    && this.contentType == IMAGE_CONTENT_TYPE
                    && this.originalFilename?.endsWith(IMAGE_EXTENSION) == true
        }

        fun of(image: MultipartFile, maxBytes: Long): ImageFile {
            if (!image.isValidImageFile(maxBytes)) {
                throw ModelCreationFailedException(
                    ErrorMessage.INVALID_REGISTER_IMAGE_FILE.format(maxBytes / 1024)
                )
            }
            return ImageFile(image.originalFilename!!, image.bytes)
        }

        fun of(buildingCode: Building.Code, org: ImageFile): ImageFile {
            return ImageFile("${buildingCode.value}${IMAGE_EXTENSION}", org.imageData)
        }
    }
}
