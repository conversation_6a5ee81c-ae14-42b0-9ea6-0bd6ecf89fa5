package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Company
import jp.ne.simplex.application.model.Employee
import jp.ne.simplex.db.jooq.gen.tables.pojos.EmployeeMasterPojo
import jp.ne.simplex.db.jooq.gen.tables.records.EmployeeMasterRecord
import jp.ne.simplex.db.jooq.gen.tables.references.EMPLOYEE_MASTER
import jp.ne.simplex.db.jooq.gen.tables.references.PASSWORD_MASTER
import jp.ne.simplex.mock.MockBranchRepository
import jp.ne.simplex.mock.MockOfficeBranchMappingRepository
import jp.ne.simplex.stub.stubEmployeeMasterPojo
import jp.ne.simplex.stub.stubLoginInfo
import jp.ne.simplex.stub.stubPasswordMasterPojo
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertNotNull

class AuthRepositoryTest : AbstractTestContainerTest() {

    private lateinit var repository: AuthRepository

    override fun beforeEach() {
        repository = AuthRepository(
            dslContext, EmployeeRepository(
            dslContext,
            MockBranchRepository(),
            MockOfficeBranchMappingRepository(),
        )
        )
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(PASSWORD_MASTER, EMPLOYEE_MASTER)
    }

    @Nested
    @DisplayName("ログイン処理の検証")
    inner class Scenario1 {

        @Nested
        @DisplayName("PasswordMasterテーブルに、指定されたID/Passのレコードが存在する場合")
        inner class Scenario1x1 {
            @BeforeEach
            fun setup() {
                dslContext.savePasswordMasterPojo(
                    stubPasswordMasterPojo(employeeId = "000011", currentPassword = "0000AA"),
                )
            }

            @Test
            @DisplayName("EmployeeMasterテーブルに、指定されたIDのレコードが存在する場合、そのEmployeeが返却されること")
            fun case01() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    stubEmployeeMasterPojo(employeeNumber = "000011", affiliationCode = "950313",
                        companyCode = Company.DaitouKentaku.code),
                )

                // execute
                val actual = repository.login(stubLoginInfo("000011", "0000AA"))

                // verify
                assertNotNull(actual)
                assertEquals("000011", actual.code.value)
                assertEquals("950313", actual.affiliationCode)
                assertEquals(Company.DaitouKentaku.code, actual.company?.code)
            }

            @Test
            @DisplayName("EmployeeMasterテーブルに、指定されたIDのレコードが存在しない場合、nullが返却されること")
            fun case02() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    stubEmployeeMasterPojo(employeeNumber = "000022", affiliationCode = "950313"),
                )

                // execute
                val actual = repository.login(stubLoginInfo("000011", "0000AA"))

                // verify
                assertNull(actual)
            }
        }

        @Nested
        @DisplayName("PasswordMasterテーブルに、指定されたID/Passのレコードが存在しない場合")
        inner class Scenario1x2 {

            @Test
            @DisplayName("EmployeeMasterテーブルに、指定されたIDのレコードが存在する場合、nullが返却されること")
            fun case01() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    stubEmployeeMasterPojo(employeeNumber = "000011", affiliationCode = "950313"),
                )

                // execute
                val actual = repository.login(stubLoginInfo("000011", "0000AA"))

                // verify
                assertNull(actual)
            }

            @Test
            @DisplayName("EmployeeMasterテーブルに、指定されたIDのレコードが存在しない場合、nullが返却されること")
            fun case02() {
                // setup
                dslContext.saveEmployeeMasterPojo(
                    stubEmployeeMasterPojo(employeeNumber = "000099", affiliationCode = "950313"),
                )

                // execute
                val actual = repository.login(stubLoginInfo("000011", "0000AA"))

                // verify
                assertNull(actual)
            }
        }
    }

    @Nested
    @DisplayName("SSOログイン処理の検証")
    inner class Scenario2 {
        @Test
        @DisplayName("EmployeeMasterテーブルに、指定されたIDのレコードが存在する場合、そのEmployeeが返却されること")
        fun case01() {
            //setup
            dslContext.save(
                table = EMPLOYEE_MASTER,
                recordConstructor = { p: EmployeeMasterPojo -> EmployeeMasterRecord(p) },
                pojos = listOf(
                    stubEmployeeMasterPojo(
                        employeeNumber = "000011",
                        affiliationCode = "950313"
                    ),
                )
            )

            // execute
            val actual = repository.getEmployee(Employee.Code.of("000011"))

            // verify
            assertNotNull(actual)
            assertEquals("000011", actual.code.value)
            assertEquals("950313", actual.affiliationCode)
        }

        @Test
        @DisplayName("EmployeeMasterテーブルに、指定されたIDのレコードが存在しない場合、nullが返却されること")
        fun case02() {

            // execute
            val actual = repository.getEmployee(Employee.Code.of("000011"))

            // verify
            assertNull(actual)
        }
    }

}
