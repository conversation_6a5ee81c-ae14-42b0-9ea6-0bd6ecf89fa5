package jp.ne.simplex.application.service

import jp.ne.simplex.application.model.UpdateParkingLotAvailability
import jp.ne.simplex.application.repository.db.ParkingEnableRepository
import jp.ne.simplex.authentication.AuthInfo
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ParkingLotAvailabilityService(
    private val repository: ParkingEnableRepository,
    private val parkingContractService: ParkingContractService,
) {

    companion object {
        private val log = LoggerFactory.getLogger(ParkingLotAvailabilityService::class.java)
    }

    fun update(requestUser: AuthInfo.RequestUser, parameter: UpdateParkingLotAvailability) {
        repository.upsert(requestUser, parameter)

        // 駐車場申込判定結果の更新及びDKポータルへの連携
        runCatching {
            parkingContractService.updateParkingContractPossibilityAndInformDKPortal(
                requestUser, parameter.parkingLotId.buildingCode.getOrderCode()
            )
        }.getOrElse {
            log.warn(
                "駐車場申込判定結果の更新及びDKポータルへの連携に失敗しました。", it
            )
        }
    }
}
