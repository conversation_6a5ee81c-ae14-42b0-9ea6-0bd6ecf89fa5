package jp.ne.simplex.application.repository.db

import jp.ne.simplex.AbstractTestContainerTest
import jp.ne.simplex.application.model.Branch
import jp.ne.simplex.application.model.Office
import jp.ne.simplex.db.jooq.gen.tables.records.DaitoBuildingManagementTableRecord
import jp.ne.simplex.db.jooq.gen.tables.references.DAITO_BUILDING_MANAGEMENT_TABLE
import jp.ne.simplex.exception.DataInconsistencyException
import jp.ne.simplex.stub.stubDaitoBuildingManagementTable
import org.jooq.impl.TableImpl
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class OfficeBranchMappingRepositoryTest : AbstractTestContainerTest() {

    private lateinit var repository: OfficeBranchMappingRepository

    private val existsOfficeCode = Office.Code.of("763")

    private val existsBranchCode = Branch.Code.of("983")

    override fun beforeEach() {
        repository = OfficeBranchMappingRepository(dslContext)

        dslContext.save(
            table = DAITO_BUILDING_MANAGEMENT_TABLE,
            recordConstructor = { p -> DaitoBuildingManagementTableRecord(p) },
            pojos = listOf(
                stubDaitoBuildingManagementTable(
                    officeCode = existsOfficeCode.value,
                    branchCode = existsBranchCode.getPrefix(),
                    usageStartDate = "20250101",
                    usageEndDate = "99999999"
                ),
                stubDaitoBuildingManagementTable(
                    officeCode = existsOfficeCode.value,
                    branchCode = null,
                    usageStartDate = "20250101",
                    usageEndDate = "99999999"
                ),
                stubDaitoBuildingManagementTable(
                    officeCode = null,
                    branchCode = null,
                    usageStartDate = "20250101",
                    usageEndDate = "99999999"
                ),
                stubDaitoBuildingManagementTable(
                    officeCode = null,
                    branchCode = existsBranchCode.getPrefix(),
                    usageStartDate = "20250101",
                    usageEndDate = "99999999"
                ),
            )
        )
    }

    override fun targetTables(): List<TableImpl<*>> {
        return listOf(DAITO_BUILDING_MANAGEMENT_TABLE)
    }

    @Nested
    @DisplayName("営業所コードから支店コードを取得できること")
    inner class Scenario1 {

        @Test
        @DisplayName("DBに対応するマッピングレコードが存在しない場合、Exceptionがスローされること")
        fun case1() {
            assertThrows<DataInconsistencyException> {
                repository.get(Office.Code.of("659"))
            }
        }

        @Test
        @DisplayName("DBに対応するマッピングレコードが存在する場合、対応する支店コードを取得できること")
        fun case2() {
            assertEquals(existsBranchCode, repository.get(existsOfficeCode))
        }
    }

    @Nested
    @DisplayName("支店コードから営業所コードを取得できること")
    inner class Scenario2 {

        @Test
        @DisplayName("DBに対応するマッピングレコードが存在しない場合、Exceptionがスローされること")
        fun case1() {
            assertThrows<DataInconsistencyException> {
                repository.get(Branch.Code.of("659"))
            }
        }

        @Test
        @DisplayName("DBに対応するマッピングレコードが存在する場合、対応する支店コードを取得できること")
        fun case2() {
            assertEquals(existsOfficeCode, repository.get(existsBranchCode))
        }
    }
}
