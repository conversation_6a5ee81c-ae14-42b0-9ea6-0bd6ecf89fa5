import {
  EboardResponseType,
  eboardResponseMapping,
} from '../../shared/eboard/eboard_response_type';

/**
 * いい物件ボード/公開指示API
 *
 * サンプルリクエスト
 * curl -i -H "Authorization: Bearer 9b7aace8-1e0e-43e2-8394-c70563283594" -X POST -H "Content-Type: application/json" http://localhost:8083/eboard/newEboardApi/instruct_public -d '{"list": [{"tatemonoCd": "1111", "heyaCd": "202", "keisai": "1"}]}'
 */
export default [
  {
    id: 'eboard_instruct_public',
    url: '/eboard/newEboardApi/instruct_public',
    method: 'POST',
    variants: [
      {
        id: 'base',
        type: 'middleware',
        options: {
          middleware: (req, res) => {
            // 必須パラメータが存在しない場合 or パラメータの値が不正な場合
            const responseType = checkParameter(req);

            if (responseType != EboardResponseType.SUCCESS) {
              res.status(400);
              res.send({
                result: responseType,
                message: eboardResponseMapping[responseType],
              });

              return;
            }

            // status: 200 の場合
            res.status(200);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
              list: req.body['list'].map((json) => {
                const isValidRequest = ['0', '1'].includes(json['keisai']);
                const randomIndex = Math.floor(Math.random() * 4);

                return {
                  tatemonoCd: json['tatemonoCd'],
                  heyaCd: json['heyaCd'],
                  updateResult: isValidRequest ? 'OK' : 'NG',
                  up_state: isValidRequest ? ['1', '2', '3', '4'][randomIndex] : '',
                  message: isValidRequest ? '' : '公開指示の値が不正です。',
                };
              }),
            });
          },
        },
      },
      {
        id: 'error',
        type: 'middleware',
        options: {
          middleware: (_, res) => {
            // 障害発生時
            const responseType = EboardResponseType.OTHER_ERROR;

            res.status(500);
            res.send({
              result: responseType,
              message: eboardResponseMapping[responseType],
            });
          },
        },
      },
    ],
  },
];

/**
 * 必須パラメータが存在するか、パラメータの値が適切かどうかを確認する
 * responseCodeMapping（api_response_specification.js）定義のエラーコードを返却する
 * @param {*} req
 * @returns {EboardResponseType}
 */
function checkParameter(req): EboardResponseType {
  const body = req.body;
  if (!body) {
    return EboardResponseType.PARAMETER_MISSING;
  }

  const requestBodyList = body['list'];
  if (!requestBodyList) {
    return EboardResponseType.PARAMETER_MISSING;
  }

  var existsRequiredParameter = true;
  requestBodyList.forEach((json) => {
    if (!json['tatemonoCd'] || !json['heyaCd'] || !json['keisai']) {
      existsRequiredParameter = false;
    }
  });

  return existsRequiredParameter
    ? EboardResponseType.SUCCESS
    : EboardResponseType.PARAMETER_MISSING;
}
