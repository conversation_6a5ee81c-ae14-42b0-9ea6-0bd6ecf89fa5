package jp.ne.simplex.application.repository.external.eboard.dto

import com.fasterxml.jackson.annotation.JsonProperty
import jp.ne.simplex.application.repository.external.eboard.config.EboardApiPath
import jp.ne.simplex.application.repository.external.eboard.config.EboardRequest
import jp.ne.simplex.exception.ErrorType
import jp.ne.simplex.log.MaskTarget
import jp.ne.simplex.log.MaskValue

@MaskTarget
class EboardTokenDpIssueRequest(
    @field:JsonProperty("userid")
    private val userid: String,

    @MaskValue
    @field:JsonProperty("password")
    private val password: String,

    @MaskValue
    @field:JsonProperty("passphrase")
    private val passphrase: String,
) : EboardRequest {
    
    override fun getApiPath(): EboardApiPath {
        return EboardApiPath.TOKEN_DP_ISSUE
    }
}
